import 'package:dartz/dartz.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/models/get_vehile_type.dart';
import 'package:safari_yatri/core/network/api_service.dart';
import 'package:safari_yatri/core/services/app_cached_keys.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';

abstract class VehicleRepository {
  FutureEither<List<GetVehicleType>> getVehicleType({
    bool includeBlocked = false,
  });
}

class VehicleRepositoryI implements VehicleRepository {
  final ApiService _apiService;
  final CoreLocalDataSource _local = CoreLocalDataSource();

  VehicleRepositoryI({required ApiService apiService})
    : _apiService = apiService;

  @override
  FutureEither<List<GetVehicleType>> getVehicleType({
    bool includeBlocked = false,
  }) async {
   

    final failureOrVehicleType = await _apiService.get<List<GetVehicleType>>(
      'VehicleType/GetList/$includeBlocked',
      fromJson: (data) {
        return List.from(
          data,
        ).map((item) => GetVehicleType.fromMap(item)).toList();
      },
    );

    return await failureOrVehicleType.fold(
      (failure) {
        return Left(failure);
      },

      (networkVehicleTypes) async {
        if (!includeBlocked) {
          await _local.saveModelList(
            AppCachedKeys.vehicleTypes,
            networkVehicleTypes.map((e) => e.toMap()).toList(),
          );
          await _local.setTimestamp(AppCachedKeys.vehicleTypes, DateTime.now());
        }

        return Right(networkVehicleTypes);
      },
    );
  }
}
