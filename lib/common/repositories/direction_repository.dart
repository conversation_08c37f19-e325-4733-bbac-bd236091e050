import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/errors/error_handler.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/config/env_secrete_config.dart';
import 'package:safari_yatri/core/models/direction_route_model.dart';

abstract interface class DirectionRepository {
  FutureEither<DirectionRouteModel> getDirectionRoute({
    LatLng? current,
    required LatLng start,
    required List<LatLng> destinations,
  });
}

class GoogleDirectionRepository implements DirectionRepository {
  final Dio _dio;

  GoogleDirectionRepository({required Dio dio}) : _dio = dio;

  @override
  FutureEither<DirectionRouteModel> getDirectionRoute({
    required LatLng start,
    required List<LatLng> destinations,
    LatLng? current,
  }) async {
    final apiKey = EnvSecreteConfig.instance.getGoogleMapApiKey;

    try {
      final origin = '${start.latitude},${start.longitude}';
      final destination =
          '${destinations.last.latitude},${destinations.last.longitude}';

      final waypoints =
          destinations.length > 1
              ? destinations
                  .sublist(0, destinations.length - 1)
                  .map((e) => '${e.latitude},${e.longitude}')
                  .join('|')
              : null;

      final queryParams = {
        'origin': origin,
        'destination': destination,
        'key': apiKey,
        'mode': 'driving',
        if (waypoints != null) 'waypoints': 'optimize:true|$waypoints',
      };

      final response = await _dio.get(
        'https://maps.googleapis.com/maps/api/directions/json',
        queryParameters: queryParams,
      );

      if (response.data['status'] != 'OK') {
        return Left(
          DirectionRouteFailure(
            message: response.data['error_message'] ?? 'Route fetch failed',
          ),
        );
      }

      final route = response.data['routes'][0];
      final encodedPolyline = route['overview_polyline']['points'] as String;
      final polylinePoints = _decodePolyline(encodedPolyline);

      final legs = route['legs'] as List;
      int totalDistance = 0;
      int totalDuration = 0;

      for (final leg in legs) {
        totalDistance += leg['distance']['value'] as int;
        totalDuration += leg['duration']['value'] as int;
      }

      // 🔄 Extra polyline from current → start
      List<LatLng>? currentToStartPolyline;
      if (current != null) {
        final currentToStartResponse = await _dio.get(
          'https://maps.googleapis.com/maps/api/directions/json',
          queryParameters: {
            'origin': '${current.latitude},${current.longitude}',
            'destination': '${start.latitude},${start.longitude}',
            'key': apiKey,
            'mode': 'driving',
          },
        );

        if (currentToStartResponse.data['status'] == 'OK') {
          final currentEncoded =
              currentToStartResponse
                      .data['routes'][0]['overview_polyline']['points']
                  as String;
          currentToStartPolyline = _decodePolyline(currentEncoded);
        }
      }

      return Right(
        DirectionRouteModel(
          polylinePoints: polylinePoints,
          totalDistanceInMeters: totalDistance,
          estimatedDurationInSeconds: totalDuration,
          polylinePointsFromCurrentToStart: currentToStartPolyline,
        ),
      );
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  List<LatLng> _decodePolyline(String encoded) {
    List<LatLng> polyline = [];
    int index = 0, len = encoded.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;

      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int deltaLat = (result & 1) != 0 ? ~(result >> 1) : (result >> 1);
      lat += deltaLat;

      shift = 0;
      result = 0;

      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int deltaLng = (result & 1) != 0 ? ~(result >> 1) : (result >> 1);
      lng += deltaLng;

      polyline.add(LatLng(lat / 1e5, lng / 1e5));
    }

    return polyline;
  }
}

class LocationIQDirectionRepository implements DirectionRepository {
  final Dio _dio;

  LocationIQDirectionRepository({required Dio dio}) : _dio = dio;

  @override
  FutureEither<DirectionRouteModel> getDirectionRoute({
    required LatLng start,
    required List<LatLng> destinations,
    LatLng? current, // Optional current location
  }) async {
    try {
      LatLng round(LatLng latLng) {
        double factor = 1e6;
        return LatLng(
          (latLng.latitude * factor).roundToDouble() / factor,
          (latLng.longitude * factor).roundToDouble() / factor,
        );
      }

      final roundedStart = round(start);
      final roundedDests = destinations.map(round).toList();

      // 🗺️ Prepare coordinates: format => lon,lat
      final coordinateList = [
        '${roundedStart.longitude},${roundedStart.latitude}',
        ...roundedDests.map((d) => '${d.longitude},${d.latitude}'),
      ];

      final coordinates = coordinateList.join(';');

      final queryParams = {
        'key': EnvSecreteConfig.instance.locationIQApiKey,
        // 'overview': 'full',
      };

      final response = await _dio.get(
        'https://us1.locationiq.com/v1/directions/driving/$coordinates',
        queryParameters: queryParams,
      );

      final data = response.data;

      if (data['code'] != 'Ok') {
        return Left(
          UnexpectedFailure(
            message:
                'Failed to fetch route: ${data['message'] ?? 'Unknown error'}',
          ),
        );
      }

      final encodedPolyline = data['routes'][0]['geometry'] as String? ?? '';
      final polylinePoints = _decodePolyline(encodedPolyline);

      final totalDistance =
          (data['routes'][0]['distance'] as num).toInt(); // meters
      final totalDuration =
          (data['routes'][0]['duration'] as num).toInt(); // seconds

      // 🔁 Fetch polyline from current → start
      List<LatLng>? polylinePointsFromCurrentToStart;

      if (current != null) {
        final currentToStartCoordinates =
            '${current.longitude},${current.latitude};${start.longitude},${start.latitude}';

        final currentToStartQueryParams = {
          'key': EnvSecreteConfig.instance.locationIQApiKey,
        };

        final currentToStartResp = await _dio.get(
          'https://us1.locationiq.com/v1/directions/driving/$currentToStartCoordinates',
          queryParameters: currentToStartQueryParams,
        );

        final currentToStartData = currentToStartResp.data;

        if (currentToStartData['code'] == 'Ok') {
          final currentPolyline =
              currentToStartData['routes'][0]['geometry'] as String? ?? '';
          polylinePointsFromCurrentToStart = _decodePolyline(currentPolyline);
        }
      }

      return Right(
        DirectionRouteModel(
          polylinePoints: polylinePoints,
          totalDistanceInMeters: totalDistance,
          estimatedDurationInSeconds: totalDuration,
          polylinePointsFromCurrentToStart: polylinePointsFromCurrentToStart,
        ),
      );
    } catch (e) {
      return Left(UnexpectedFailure(message: e.toString()));
    }
  }

  /// Decode Google's encoded polyline into LatLng points
  List<LatLng> _decodePolyline(String encoded) {
    List<LatLng> polyline = [];
    int index = 0, len = encoded.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;

      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int deltaLat = (result & 1) != 0 ? ~(result >> 1) : (result >> 1);
      lat += deltaLat;

      shift = 0;
      result = 0;

      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int deltaLng = (result & 1) != 0 ? ~(result >> 1) : (result >> 1);
      lng += deltaLng;

      polyline.add(LatLng(lat / 1e5, lng / 1e5));
    }

    return polyline;
  }
}
