import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/features/location/blocs/location_permission/location_permission_bloc.dart';

///aba yesle chaii check garxa weather location permission granted or not base on that
///haii it will navigate or stay in that page
class LocationPermissionListenerWidget extends StatelessWidget {
  const LocationPermissionListenerWidget({super.key, required this.child});
  final Widget child;
  @override
  Widget build(BuildContext context) {
    return BlocListener<LocationPermissionBloc, LocationPermissionState>(
      listener: (context, state) {
        state.whenOrNull(
          loaded: (grantedStatus) {},
          failure: (failure) {
            context.goNamed(AppRoutesName.locationPermissionHandler);
          },
        );
      },
      child: child,
    );
  }
}
