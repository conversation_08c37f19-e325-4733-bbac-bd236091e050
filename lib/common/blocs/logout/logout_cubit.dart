import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/services/app_cached_keys.dart';
import 'package:safari_yatri/core/services/cache_service.dart';
import 'package:safari_yatri/core/services/driver_navigation_mode_service.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/role/services/remote_role_service.dart';

part 'logout_state.dart';

class LogoutCubit extends Cubit<LogoutState> {
  final CacheService _cacheService = CacheService.instance;
  final CoreLocalDataSource _coreLocalDataSource = CoreLocalDataSource();
  final RemoteRoleService _remoteRoleService = RemoteRoleService.instance;
  LogoutCubit() : super(LogoutState.initial());

  Future<void> logout() async {
    emit(LogoutState.loading());
    try {
      for (String key in AppCachedKeys.allCachedKey) {
        await _coreLocalDataSource.clear(key);
      }

      // Clear all cached data
      await Future.wait([
        _cacheService.clearTokenData(),
        RiderLocallySwitchingModeServices.clear(),
        _coreLocalDataSource.clearAllBox(),
        _remoteRoleService.clearUserRole(),
      ]);

      await Future.delayed(const Duration(seconds: 1));

      emit(LogoutState.loaded("Logout successfully"));
    } catch (e) {
      emit(LogoutState.failure(UnexpectedFailure(message: e.toString())));
    }
  }
}
