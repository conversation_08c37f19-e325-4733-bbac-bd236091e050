import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

class ImagePickerBloc extends Bloc<ImagePickerEvent, ImagePickerState> {
  final ImagePicker _picker = ImagePicker();

  ImagePickerBloc() : super(const ImagePickerState()) {
    on<PickImagesEvent>(_onPickImages);
    on<RemoveImageEvent>(_onRemoveImage);
    on<LoadInitialImagesEvent>(_onLoadInitialImages);
  }

  Future<void> _onLoadInitialImages(
    LoadInitialImagesEvent event,
    Emitter<ImagePickerState> emit,
  ) async {
    final List<XFile> initialFiles = [];

    for (final xFile in event.paths) {
      final xfile = XFile(xFile.path);
      try {
        // Try accessing file bytes to validate it exists
        await xfile.length();
        initialFiles.add(xfile);
      } catch (e) {
        debugPrint('Initial image file not found or unreadable: ${xFile.path}');
      }
    }

    emit(state.copyWith(selectedImages: initialFiles, errorMessage: null));
  }

  Future<void> _onPickImages(
    PickImagesEvent event,
    Emitter<ImagePickerState> emit,
  ) async {
    try {
      List<XFile> pickedFiles = [];

      if (event.mode == ImagePickMode.multiple) {
        pickedFiles = await _picker.pickMultiImage();
      } else {
        final single = await _picker.pickImage(source: ImageSource.gallery);
        if (single != null) {
          pickedFiles = [single];
        }
      }

      if (pickedFiles.isNotEmpty) {
        final List<XFile> currentImages = List.from(state.selectedImages);
        String? validationError;

        for (final xfile in pickedFiles) {
          if (currentImages.length >= 5) {
            validationError = "You can only select up to 5 images.";
            break;
          }

          final int size = await xfile.length();
          if (size > 5 * 1024 * 1024) {
            validationError = "Image ${xfile.name} is larger than 5MB.";
            continue;
          }

          final ext = xfile.name.split('.').last.toLowerCase();
          if (!['jpg', 'jpeg', 'png'].contains(ext)) {
            validationError =
                "Only JPG and PNG files are allowed. (${xfile.name})";
            continue;
          }

          currentImages.add(xfile);
        }

        emit(
          state.copyWith(
            selectedImages: currentImages,
            errorMessage: validationError,
          ),
        );
      } else {
        emit(state.copyWith(errorMessage: null));
      }
    } catch (e) {
      debugPrint("Error while picking images: $e");
      emit(
        state.copyWith(
          errorMessage: "Error while picking images: ${e.toString()}",
        ),
      );
    }
  }

  void _onRemoveImage(RemoveImageEvent event, Emitter<ImagePickerState> emit) {
    final List<XFile> updated = List.from(state.selectedImages)
      ..removeAt(event.index);
    emit(state.copyWith(selectedImages: updated));
  }
}

class ImagePickerState extends Equatable {
  final List<XFile> selectedImages;
  final String? errorMessage;
  final List<String> selectedWeather; // New field for selected weather

  const ImagePickerState({
    this.selectedImages = const [],
    this.errorMessage,
    this.selectedWeather = const [], // Initialize new field
  });

  ImagePickerState copyWith({
    List<XFile>? selectedImages,
    String? errorMessage,
    List<String>? selectedWeather, // Include new field in copyWith
  }) {
    return ImagePickerState(
      selectedImages: selectedImages ?? this.selectedImages,
      errorMessage: errorMessage,
      selectedWeather:
          selectedWeather ?? this.selectedWeather, // Assign new field
    );
  }

  @override
  List<Object?> get props => [
    selectedImages,
    errorMessage,
    selectedWeather, // Include new field in props
  ];
}

abstract class ImagePickerEvent extends Equatable {
  const ImagePickerEvent();

  @override
  List<Object?> get props => [];
}

enum ImagePickMode { single, multiple }

class PickImagesEvent extends ImagePickerEvent {
  final BuildContext context;
  final ImagePickMode mode;

  const PickImagesEvent(this.context, this.mode);

  @override
  List<Object?> get props => [context, mode];
}

class RemoveImageEvent extends ImagePickerEvent {
  final int index;
  const RemoveImageEvent(this.index);

  @override
  List<Object?> get props => [index];
}

class LoadInitialImagesEvent extends ImagePickerEvent {
  final List<XFile> paths;
  const LoadInitialImagesEvent(this.paths);

  @override
  List<Object?> get props => [paths];
}
