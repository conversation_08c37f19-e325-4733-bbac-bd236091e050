import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/common/repositories/vehicle_repository.dart';
import 'package:safari_yatri/core/models/get_vehile_type.dart';
import 'package:safari_yatri/core/services/app_cached_keys.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';

part 'get_vehicle_type_event.dart';
part 'get_vehicle_type_state.dart';
part 'get_vehicle_type_bloc.freezed.dart';

class GetVehicleTypeBloc
    extends Bloc<GetVehicleTypeEvent, GetVehicleTypeState> {
  final VehicleRepository _vehicleRepository;
  final CoreLocalDataSource _local = CoreLocalDataSource();
  List<GetVehicleType> _vehicleTypes = [];

  GetVehicleTypeBloc({required VehicleRepository repo})
    : _vehicleRepository = repo,
      super(const GetVehicleTypeState.initial()) {
    on<GetVehicleTypeEvent>(_onGetVehicleType);
  }

  Future<void> _onGetVehicleType(
    GetVehicleTypeEvent event,
    Emitter<GetVehicleTypeState> emit,
  ) async {
    if (_vehicleTypes.isNotEmpty) {
      return emit(GetVehicleTypeState.loaded(_vehicleTypes));
    }

    emit(const GetVehicleTypeState.loading());

    final result = await _vehicleRepository.getVehicleType();

    result.fold(
      (failure) {
        final localVehicleTypes = _local.getModelList(
          AppCachedKeys.vehicleTypes,
          (json) {
            return GetVehicleType.fromMap(json);
          },
        );
        if (localVehicleTypes.isNotEmpty) {
          return emit(GetVehicleTypeState.loaded(localVehicleTypes));
        }

        emit(GetVehicleTypeState.failure(failure));
      },
      (vehicleTypes) {
        _vehicleTypes = vehicleTypes;
        emit(GetVehicleTypeState.loaded(vehicleTypes));
      },
    );
  }
}
