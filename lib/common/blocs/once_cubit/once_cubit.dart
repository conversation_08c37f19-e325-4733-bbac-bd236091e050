import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/services/once_cache_service.dart';
part 'once_state.dart';

class OnceCubit extends Cubit<OnceState> {
  OnceCubit() : super(OnceState());

  void completedOnBoaring() async {
    try {
      await OnceCacheService.set();
      emit(OnceState(onBoardingCompleted: 'success'));
    } catch (e) {
      emit(OnceState(onBoardingCompleted: null));
    }
  }

  void getOnBoarding() async {
    try {
      final getOnboarding = await OnceCacheService.get();
      emit(OnceState(onBoardingCompleted: getOnboarding));
    } catch (e) {
      emit(OnceState(onBoardingCompleted: null));
    }
  }
}
