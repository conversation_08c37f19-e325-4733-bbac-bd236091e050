import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/models/ride_shift_model.dart';
import 'package:safari_yatri/core/services/app_cached_keys.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/repositories/ride_shift_repository.dart';

part 'get_ride_shift_event.dart';
part 'get_ride_shift_state.dart';
part 'get_ride_shift_bloc.freezed.dart';

///I have implemented caching in Ride shift like when we pass include blocked false in that case we make sure to return cached values within 24 hour period
class GetRideShiftBloc extends Bloc<GetRideShiftEvent, GetRideShiftState> {
  final RideShiftRepository _rideShiftRepository;

  List<RideShiftModel> _rideShiftModels = [];
  final CoreLocalDataSource _local = CoreLocalDataSource();

  GetRideShiftBloc({required RideShiftRepository repo})
    : _rideShiftRepository = repo,
      super(GetRideShiftState.initial()) {
    on<_Get>(_onGet);
  }

  Future<void> _onGet(_Get event, Emitter<GetRideShiftState> emit) async {
    if (_rideShiftModels.isNotEmpty && event.refetch == false) {
      emit(GetRideShiftState.loaded(_rideShiftModels));
      return;
    }
    emit(GetRideShiftState.loading());
    final result = await _rideShiftRepository.getRideShiftList(
      event.includeBlocked ?? false,
    );

    result.fold(
      (failure) {
        final getRideShifts = _local.getModelList<RideShiftModel>(
          AppCachedKeys.rideShift,
          (json) => RideShiftModel.fromMap(json),
        );
        if (getRideShifts.isNotEmpty) {
          return emit(GetRideShiftState.loaded(getRideShifts));
        }

        emit(GetRideShiftState.failure(failure));
      },
      (data) {
        _rideShiftModels = data;
        emit(GetRideShiftState.loaded(data));
      },
    );
  }
}
