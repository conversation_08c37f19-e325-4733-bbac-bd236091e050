import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'booking_detail_event.dart';
part 'booking_detail_state.dart';
part 'booking_detail_bloc.freezed.dart';

class BookingDetailBloc extends Bloc<BookingDetailEvent, BookingDetailState> {
  final BookingRepository _repository;

  final Map<int, BookingModel> _cache = {};

  BookingDetailBloc({required BookingRepository repo})
    : _repository = repo,
      super(BookingDetailState.initial()) {
    on<_Get>(_onGet);
  }

  Future<void> _onGet(_Get event, Emitter<BookingDetailState> emit) async {
    final bookingId = event.bookingId;

    if (event.forceFetch != true && _cache.containsKey(bookingId)) {
      emit(BookingDetailState.loaded(_cache[bookingId]!));
      return;
    }

    emit(BookingDetailState.loading());
    final result = await _repository.getBookingDetail(id: bookingId);

    result.fold((failure) => emit(BookingDetailState.failure(failure)), (data) {
      _cache[bookingId] = data;
      emit(BookingDetailState.loaded(data));
    });
  }
}
