import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/location/repositories/location_repository.dart';

part 'current_lat_lng_address_event.dart';
part 'current_lat_lng_address_state.dart';
part 'current_lat_lng_address_bloc.freezed.dart';

///Helps to show current address from latlng
class CurrentLatLngAddressBloc
    extends Bloc<CurrentLatLngAddressEvent, CurrentLatLngAddressState> {
  final LocationRepository _locationRepository;
  CurrentLatLngAddressBloc({required LocationRepository repo})
    : _locationRepository = repo,
      super(const CurrentLatLngAddressState.initial()) {
    on<_Get>(_onGet);
  }

  Future<void> _onGet(
    _Get event,
    Emitter<CurrentLatLngAddressState> emit,
  ) async {
    emit(const CurrentLatLngAddressState.loading());

    final failureOrAddress = await _locationRepository
        .getAddressFromCoordinates(
          event.position.latitude,
          event.position.longitude,
        );

    await failureOrAddress.fold(
      (failure) async => emit(CurrentLatLngAddressState.failure(failure)),
      (address) async {
        emit(CurrentLatLngAddressState.loaded(address));
      },
    );
  }
}
