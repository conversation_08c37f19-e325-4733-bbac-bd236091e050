import 'package:image_picker/image_picker.dart';

/// An extension on [XFile] to easily determine the MIME type of image files.
extension ImageMimeTypeExtension on XFile {
  /// Returns the inferred MIME type of the image file based on its extension.
  ///
  /// Returns 'application/octet-stream' if the extension is not recognized
  /// as a common image type.
  String get imageMimeType {
    // Get the file name and extract the extension
    final String fileName = name;
    final int lastDotIndex = fileName.lastIndexOf('.');

    if (lastDotIndex == -1 || lastDotIndex == fileName.length - 1) {
      // No extension or empty extension
      return 'application/octet-stream';
    }

    final String extension = fileName.substring(lastDotIndex + 1).toLowerCase();

    // Determine MIME type based on common image extensions
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'bmp':
        return 'image/bmp';
      case 'webp':
        return 'image/webp';
      case 'tiff':
      case 'tif':
        return 'image/tiff';
      case 'svg':
        return 'image/svg+xml';
      case 'heic': // Apple's High Efficiency Image File Format
        return 'image/heic';
      case 'heif': // High Efficiency Image File Format
        return 'image/heif';
      default:
        // Default to a generic binary stream if extension is not a recognized image type
        return 'application/octet-stream';
    }
  }
}
