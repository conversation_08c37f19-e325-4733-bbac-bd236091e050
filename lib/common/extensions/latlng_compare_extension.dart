import 'dart:math';

import 'package:google_maps_flutter/google_maps_flutter.dart';

extension LatLngComparisonExtension on LatLng {
  static const double _earthRadiusMeters = 6371000.0;

  double _toRadians(double degrees) {
    return degrees * pi / 180.0;
  }

  double distanceTo(LatLng other) {
    final lat1Rad = _toRadians(latitude);
    final lon1Rad = _toRadians(longitude);
    final lat2Rad = _toRadians(other.latitude);
    final lon2Rad = _toRadians(other.longitude);

    final dLat = lat2Rad - lat1Rad;
    final dLon = lon2Rad - lon1Rad;

    final a =
        pow(sin(dLat / 2), 2) +
        cos(lat1Rad) * cos(lat2Rad) * pow(sin(dLon / 2), 2);
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return _earthRadiusMeters * c;
  }

  bool isNear(LatLng other, {double thresholdMeters = 10.0}) {
    return distanceTo(other) <= thresholdMeters;
  }
}
