import 'package:flutter/material.dart';

class AdminPaymentSectionPage extends StatelessWidget {
  const AdminPaymentSectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: SingleChildScrollView(
        // Allow scrolling
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Reconciliation & Refunds',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 20),
            // Placeholder for Error Message UI
            ErrorMessageWidget(message: null), // Pass null initially

            const SizedBox(height: 20),
            // Responsive grid layout for stat cards
            LayoutBuilder(
              builder: (context, constraints) {
                // Adjust grid based on screen size
                int crossAxisCount =
                    constraints.maxWidth > 1200
                        ? 3 // Large desktop
                        : constraints.maxWidth > 800
                        ? 3 // Medium desktop/tablet
                        : 1; // Mobile

                double childAspectRatio =
                    constraints.maxWidth > 1200
                        ? 1.5
                        : constraints.maxWidth > 800
                        ? 1.8
                        : 1.5;

                return GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: crossAxisCount,
                  childAspectRatio: childAspectRatio,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  children: const [
                    DashboardStatCard(
                      // Reusing the stat card widget
                      title: 'Total Processed',
                      value: '\$150,000',
                      icon: Icons.account_balance_wallet,
                      color: Colors.teal,
                    ),
                    DashboardStatCard(
                      title: 'Total Refunds',
                      value: '\$1,500',
                      icon: Icons.money_off,
                      color: Colors.deepOrange,
                    ),
                    DashboardStatCard(
                      title: 'Pending Reconciliation',
                      value: '\$5,000',
                      icon: Icons.pending_actions,
                      color: Colors.blueGrey,
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 30),

            Text(
              'Recent Transactions',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 10),
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Example transaction items (replace with dynamic data)
                    TransactionItem(
                      icon: Icons.arrow_circle_down,
                      title: 'Payment Received',
                      amount: '\$25.00',
                      description: 'Booking #100125 - John Doe',
                      time: '10 minutes ago',
                      isIncoming: true,
                    ),
                    const Divider(),
                    TransactionItem(
                      icon: Icons.arrow_circle_up,
                      title: 'Refund Issued',
                      amount: '\$15.00',
                      description: 'Booking #100120 - Jane Smith',
                      time: '1 hour ago',
                      isIncoming: false,
                    ),
                    const Divider(),
                    TransactionItem(
                      icon: Icons.arrow_circle_down,
                      title: 'Payment Received',
                      amount: '\$30.00',
                      description: 'Booking #100119 - Bob Johnson',
                      time: '3 hours ago',
                      isIncoming: true,
                    ),
                    const Divider(),
                    TransactionItem(
                      icon: Icons.arrow_circle_down,
                      title: 'Payment Received',
                      amount: '\$18.50',
                      description: 'Booking #100118 - Alice Brown',
                      time: '5 hours ago',
                      isIncoming: true,
                    ),
                    const Divider(),
                    TransactionItem(
                      icon: Icons.arrow_circle_up,
                      title: 'Refund Issued',
                      amount: '\$22.00',
                      description: 'Booking #100115 - Charlie Davis',
                      time: '1 day ago',
                      isIncoming: false,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 30),

            Text(
              'Payment Analytics',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 10),
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Monthly Revenue (Example)',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                    const SizedBox(height: 10),
                    Container(
                      height: 200, // Fixed height for chart placeholder
                      color: Colors.grey[200], // Placeholder color
                      child: const Center(child: Text('Chart Area')),
                    ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildAnalyticItem(
                          'This Month',
                          '\$12,500',
                          Colors.blue,
                        ),
                        _buildAnalyticItem(
                          'Last Month',
                          '\$10,800',
                          Colors.green,
                        ),
                        _buildAnalyticItem('Growth', '+15.7%', Colors.orange),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 14)),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
      ],
    );
  }
}

// Widget for displaying error messages
class ErrorMessageWidget extends StatelessWidget {
  final String? message;

  const ErrorMessageWidget({super.key, this.message});

  @override
  Widget build(BuildContext context) {
    if (message == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade300),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red.shade700),
          const SizedBox(width: 16),
          Expanded(
            child: Text(message!, style: TextStyle(color: Colors.red.shade700)),
          ),
        ],
      ),
    );
  }
}

// Widget for dashboard stat cards
class DashboardStatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const DashboardStatCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
                Icon(icon, color: color, size: 28),
              ],
            ),
            const SizedBox(height: 10),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                // color: color.shade800,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Widget for transaction items
class TransactionItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String amount;
  final String description;
  final String time;
  final bool isIncoming;

  const TransactionItem({
    super.key,
    required this.icon,
    required this.title,
    required this.amount,
    required this.description,
    required this.time,
    required this.isIncoming,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            backgroundColor:
                isIncoming
                    ? Colors.green.withOpacity(0.2)
                    : Colors.red.withOpacity(0.2),
            child: Icon(
              icon,
              color: isIncoming ? Colors.green : Colors.red,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      amount,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: isIncoming ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                ),
                const SizedBox(height: 4),
                Text(
                  time,
                  style: TextStyle(color: Colors.grey.shade500, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
