import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_pending_rider/get_pending_rider_bloc.dart';
import 'package:safari_yatri/features/admin/core/models/rider_model.dart';

class RiderManagementPage extends StatefulWidget {
  const RiderManagementPage({super.key});

  @override
  State<RiderManagementPage> createState() => _RiderManagementPageState();
}

class _RiderManagementPageState extends State<RiderManagementPage> {
  @override
  initState() {
    super.initState();

    _getPendingRiders();
  }

  void _getPendingRiders() {
    sl<GetPendingRiderBloc>().add(const GetPendingRiderEvent.get());
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        _getPendingRiders();
      },
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        body: BlocBuilder<GetPendingRiderBloc, GetPendingRiderState>(
          builder: (context, state) {
            return state.maybeWhen(
              loading: () => const Center(child: CircularProgressIndicator()),
              failure: (failure) {
                return ErrorWidgetWithRetry(
                  failure: failure,
                  onRetry: () => _getPendingRiders(),
                );
              },
              loaded: (data) {
                return _pendingRidersCard(data);
              },
              orElse: () {
                return SizedBox();
              },
            );
          },
        ),
      ),
    );
  }

  LayoutBuilder _pendingRidersCard(
    List<RiderApplication> pendingRidersApplication,
  ) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final bool isDesktop = constraints.maxWidth > 800;

        return Padding(
          padding: EdgeInsets.all(isDesktop ? 32.0 : 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              const SizedBox(height: 24),

              // const _RiderSearchBar(),
              // const SizedBox(height: 24),
              Expanded(
                child: Column(
                  children: [
                    const SizedBox(height: 16),
                    Expanded(
                      child: PendingRidersView(
                        onTap: (p0) {
                          context.pushNamed(
                            AppRoutesName.pendingRiderDetails,
                            pathParameters: {'id': p0.userId.toString()},
                          );
                        },
                        applications: pendingRidersApplication,
                        isDesktop: isDesktop,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Pending Riders',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 4),
        Text(
          'Manage pending rider applications ',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }
}

class PendingRidersView extends StatelessWidget {
  final List<RiderApplication> applications;
  final bool isDesktop;

  final Function(RiderApplication) onTap;

  const PendingRidersView({
    super.key,
    required this.applications,
    required this.isDesktop,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // return isDesktop
    //     ? PendingRidersDesktopTable(
    //       applications: applications,
    //       onAccept: onAccept,
    //       onReject: onReject,
    //       onTap: onTap,
    //     )
    return PendingRidersMobileList(applications: applications, onTap: onTap);
  }
}

class PendingRidersDesktopTable extends StatelessWidget {
  final List<RiderApplication> applications;
  final Function(RiderApplication) onAccept;
  final Function(RiderApplication) onReject;

  const PendingRidersDesktopTable({
    super.key,
    required this.applications,
    required this.onAccept,
    required this.onReject,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            columnSpacing: 40,
            dataRowMinHeight: 48,
            dataRowMaxHeight: 60,
            headingRowColor: WidgetStateProperty.all(Colors.grey[50]),
            headingTextStyle: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            columns: const [
              DataColumn(label: Text('User ID')),
              DataColumn(label: Text('Name')),
              DataColumn(label: Text('Phone No')),
              DataColumn(label: Text('Email')),
              DataColumn(label: Text('Applied Date')),
              DataColumn(label: Text('Applied Time')),
              DataColumn(label: Text('Actions')),
            ],
            rows:
                applications.map((application) {
                  return DataRow(
                    cells: [
                      DataCell(Text(application.userId.toString())),
                      DataCell(Text(application.userName)),
                      DataCell(Text(application.phoneNo)),
                      DataCell(
                        application.emailAddress == null
                            ? SizedBox()
                            : Text(application.emailAddress!),
                      ),
                      DataCell(Text(application.registeredDate)),
                      DataCell(Text(application.registeredTime)),
                      DataCell(
                        Row(
                          children: [
                            IconButton(
                              icon: const Icon(
                                Icons.check_circle_outline,
                                color: Colors.green,
                              ),
                              onPressed: () {
                                onAccept(application);
                              },
                              tooltip: 'Accept',
                            ),
                            IconButton(
                              icon: const Icon(
                                Icons.cancel_outlined,
                                color: Colors.red,
                              ),
                              onPressed: () {
                                onReject(application);
                              },
                              tooltip: 'Reject',
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }).toList(),
          ),
        ),
      ),
    );
  }
}

class PendingRidersMobileList extends StatelessWidget {
  final List<RiderApplication> applications;
  final Function(RiderApplication) onTap;

  const PendingRidersMobileList({
    super.key,
    required this.applications,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: applications.length,
      itemBuilder: (context, index) {
        final application = applications[index];
        return GestureDetector(
          onTap: () => onTap(application),
          child: Card(
            margin: const EdgeInsets.symmetric(vertical: 8.0),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    application.userName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _InfoRow(
                    label: 'User ID:',
                    value: application.userId.toString(),
                  ),
                  _InfoRow(label: 'Phone:', value: application.phoneNo),
                  application.emailAddress == null
                      ? SizedBox()
                      : _InfoRow(
                        label: 'Email:',
                        value: application.emailAddress!,
                      ),
                  _InfoRow(
                    label: 'Applied Date:',
                    value: application.registeredDate,
                  ),
                  _InfoRow(
                    label: 'Applied Time:',
                    value: application.registeredTime,
                  ),
                  const SizedBox(height: 12),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class _InfoRow extends StatelessWidget {
  final String label;
  final String value;

  const _InfoRow({required this.label, required this.value});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }
}
