import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/get_ride_shift/get_ride_shift_bloc.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/models/ride_shift_model.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';

class RideShiftManagementPage extends StatefulWidget {
  const RideShiftManagementPage({super.key});

  @override
  State<RideShiftManagementPage> createState() =>
      _RideShiftManagementPageState();
}

class _RideShiftManagementPageState extends State<RideShiftManagementPage> {
  bool includeInactiveShifts = false;
  late GetRideShiftBloc _rideShiftBloc;
  @override
  initState() {
    super.initState();
    _rideShiftBloc = sl<GetRideShiftBloc>();
    _getRideShift();
  }

  void _getRideShift({bool includeCancel = false}) {
    _rideShiftBloc.add(GetRideShiftEvent.get(includeCancel));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Ride Shifts"),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.pushNamed(AppRoutesName.rideShiftForm);
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async => _getRideShift(),
        child: BlocBuilder<GetRideShiftBloc, GetRideShiftState>(
          builder: (context, state) {
            return state.maybeWhen(
              orElse: () => Center(child: CircularProgressIndicator()),
              loaded: (data) {
                return ListView.builder(
                  itemCount: data.length + 1,
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return _buildCheckBox();
                    }

                    return _buildRideShiftCard(data[index - 1]);
                  },
                );
              },
              failure:
                  (f) => ErrorWidgetWithRetry(
                    failure: f,
                    onRetry: () => _getRideShift(),
                  ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildRideShiftCard(RideShiftModel rideShift) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          title: Text(
            rideShift.shiftName,
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.access_time, size: 16),
                    SizedBox(width: 4),
                    Text("${rideShift.startTime}-${rideShift.endTime}"),
                  ],
                ),
                SizedBox(height: 4),
                Text.rich(
                  TextSpan(
                    text: "Vehicle: ",
                    style: TextStyle(fontWeight: FontWeight.bold),
                    children: [
                      TextSpan(
                        text: rideShift.vehicleTypeName,
                        style: TextStyle(fontWeight: FontWeight.normal),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 2),
                Text.rich(
                  TextSpan(
                    text: "Fare Rates: ",
                    style: TextStyle(fontWeight: FontWeight.bold),
                    children: [
                      TextSpan(
                        text: rideShift.fareRateItems.length.toString(),
                        style: TextStyle(fontWeight: FontWeight.normal),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            context.pushNamed(AppRoutesName.rideShiftDetails, extra: rideShift);
          },
        ),
      ],
    );
  }

  Row _buildCheckBox() {
    return Row(
      children: [
        Checkbox(
          value: includeInactiveShifts,
          onChanged: (value) {
            setState(() {
              includeInactiveShifts = value ?? false;
              _getRideShift(includeCancel: includeInactiveShifts);
            });
          },
        ),
        const Text("Include inactive shifts"),
      ],
    );
  }
}
