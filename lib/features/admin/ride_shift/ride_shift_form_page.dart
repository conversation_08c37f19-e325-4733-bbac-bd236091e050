import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/get_ride_shift/get_ride_shift_bloc.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/models/get_vehile_type.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/vehicle_type_widget.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_ride_shift/manage_ride_shift_bloc.dart';
import 'package:safari_yatri/features/admin/core/models/ride_shift_form.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class ShiftDetailsFormPage extends StatefulWidget {
  const ShiftDetailsFormPage({super.key});

  @override
  State<ShiftDetailsFormPage> createState() => _ShiftDetailsFormPageState();
}

class _ShiftDetailsFormPageState extends State<ShiftDetailsFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _shiftNameController = TextEditingController();
  final _startTimeController = TextEditingController();
  final _endTimeController = TextEditingController();
  // final _vehicleTypeIdController = TextEditingController(text: "1");

  GetVehicleType? _selectedVehicleType;

  late ManageRideShiftBloc _manageRideShiftBloc;

  @override
  initState() {
    super.initState();
    _manageRideShiftBloc = sl<ManageRideShiftBloc>();
  }

  @override
  void dispose() {
    _shiftNameController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    super.dispose();
  }

  Future<void> _selectTime(TextEditingController controller) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null) {
      controller.text = picked.format(context);
    }
  }

  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      _manageRideShiftBloc.add(
        ManageRideShiftEvent.insert(
          RideShiftForm(
            shiftName: _shiftNameController.text,
            startTime: _startTimeController.text,
            endTime: _endTimeController.text,
            vehicleTypeId: _selectedVehicleType!.vehicleTypeId,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context); // Access current theme
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Scaffold(
      appBar: AppBar(title: Text("Shift Details", style: textTheme.titleLarge)),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: _shiftNameController,
                decoration: const InputDecoration(
                  labelText: "Shift Name",
                  border: OutlineInputBorder(),
                ),
                validator:
                    (value) =>
                        value == null || value.isEmpty
                            ? "Please enter shift name"
                            : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _startTimeController,
                readOnly: true,
                onTap: () => _selectTime(_startTimeController),
                decoration: const InputDecoration(
                  labelText: "Start Time",
                  hintText: "e.g. 09:00 AM",
                  border: OutlineInputBorder(),
                ),
                validator:
                    (value) =>
                        value == null || value.isEmpty
                            ? "Please enter start time"
                            : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _endTimeController,
                readOnly: true,
                onTap: () => _selectTime(_endTimeController),
                decoration: const InputDecoration(
                  labelText: "End Time",
                  hintText: "e.g. 05:00 PM",
                  border: OutlineInputBorder(),
                ),
                validator:
                    (value) =>
                        value == null || value.isEmpty
                            ? "Please enter end time"
                            : null,
              ),
              const SizedBox(height: 16),
              VehicleTypeDropdown(
                selectedVehicleType: _selectedVehicleType,
                onChanged: (p0) {
                  _selectedVehicleType = p0;
                },
              ),
              const SizedBox(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      "Cancel",
                      style: textTheme.labelLarge?.copyWith(
                        color: colorScheme.primary,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  BlocListener<ManageRideShiftBloc, ManageRideShiftState>(
                    bloc: _manageRideShiftBloc,
                    listener: _manageRiderShiftBlocListener,
                    child: CustomButtonPrimary(
                      width: 100,
                      height: 40,
                      title: "Save",
                      onPressed: _submitForm,
                      leadingIcon: Icon(LucideIcons.save),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _manageRiderShiftBlocListener(
    BuildContext context,
    ManageRideShiftState state,
  ) {
    state.whenOrNull(
      loading: () => AppLoadingDialog.show(context),
      loaded: (msg) {
        AppLoadingDialog.hide(context);
        CustomToast.showSuccess(msg);
        sl<GetRideShiftBloc>().add(GetRideShiftEvent.get());
        context.pop();
      },
      failure: (failure) {
        AppLoadingDialog.hide(context);
        CustomToast.showError(failure.message);
      },
    );
  }
}
