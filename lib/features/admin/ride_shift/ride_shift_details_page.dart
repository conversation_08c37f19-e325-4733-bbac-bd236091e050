import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/get_ride_shift/get_ride_shift_bloc.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/models/ride_shift_model.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/date_time_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_fare_rate/manage_fare_rate_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_ride_shift/manage_ride_shift_bloc.dart';
import 'package:safari_yatri/features/admin/core/models/fare_rate_insert_request_model.dart';
import 'package:safari_yatri/features/admin/core/models/fare_rate_model.dart';
import '../core/widgets/switch_widget.dart';

class RideShiftDetailPage extends StatefulWidget {
  const RideShiftDetailPage({super.key, required this.rideShift});
  final RideShiftModel rideShift;

  @override
  State<RideShiftDetailPage> createState() => _RideShiftDetailPageState();
}

class _RideShiftDetailPageState extends State<RideShiftDetailPage> {
  late RideShiftModel rideShiftModel;
  late TextEditingController shiftNameController;
  late TextEditingController vehicleTypeController;
  late ManageFareRateBloc fareRateBloc;

  List<FareRateModel> fareRates = [];
  bool isActive = true;
  bool hasChanged = false;
  TimeOfDay? startTime;
  TimeOfDay? endTime;
  late final ManageRideShiftBloc _manageRideShiftBloc;
  @override
  void initState() {
    super.initState();
    _manageRideShiftBloc = sl<ManageRideShiftBloc>();
    rideShiftModel = widget.rideShift;
    isActive = widget.rideShift.isActive;
    fareRates = List.from(widget.rideShift.fareRateItems);

    shiftNameController = TextEditingController(text: rideShiftModel.shiftName);
    vehicleTypeController = TextEditingController(
      text: rideShiftModel.vehicleTypeName,
    );

    startTime = AppDateTimeUtils.parseTimeOfDayFromString(
      rideShiftModel.startTime,
    );
    endTime = AppDateTimeUtils.parseTimeOfDayFromString(rideShiftModel.endTime);

    shiftNameController.addListener(() => setState(() => hasChanged = true));
    vehicleTypeController.addListener(() => setState(() => hasChanged = true));
    fareRateBloc = sl<ManageFareRateBloc>();
  }

  @override
  void dispose() {
    shiftNameController.dispose();
    vehicleTypeController.dispose();
    super.dispose();
  }

  void _handleUpdate() {
    setState(() {
      rideShiftModel = rideShiftModel.copyWith(
        shiftName: shiftNameController.text,
        vehicleTypeName: vehicleTypeController.text,
        startTime: AppDateTimeUtils.getTimeInHHMMAndPeriod(startTime),
        endTime: AppDateTimeUtils.getTimeInHHMMAndPeriod(endTime),
        isActive: isActive,
        fareRateItems: fareRates,
      );
      hasChanged = false;
    });

    _manageRideShiftBloc.add(ManageRideShiftEvent.update(rideShiftModel));
  }

  Future<void> _selectTime(BuildContext context, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime:
          isStartTime
              ? (startTime ?? TimeOfDay.now())
              : (endTime ?? TimeOfDay.now()),
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          startTime = picked;
        } else {
          endTime = picked;
        }
        hasChanged = true;
      });
    }
  }

  void _showAddFareRateDialog() {
    final distanceController = TextEditingController();
    final perMeterRateController = TextEditingController();
    final additionalCostController = TextEditingController();
    final discountController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(
                'Add Fare Rate',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildDialogTextField(
                      'Distance (meters)',
                      distanceController,
                      TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    _buildDialogTextField(
                      'Rate per Meter',
                      perMeterRateController,
                      TextInputType.numberWithOptions(decimal: true),
                    ),
                    const SizedBox(height: 16),
                    _buildDialogTextField(
                      'Additional Cost',
                      additionalCostController,
                      TextInputType.numberWithOptions(decimal: true),
                    ),
                    const SizedBox(height: 16),
                    _buildDialogTextField(
                      'Multi-Passenger Discount (%)',
                      discountController,
                      TextInputType.numberWithOptions(decimal: true),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Active:',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                FilledButton(
                  onPressed: () {
                    if (_validateFareRateInputs(
                      distanceController,
                      perMeterRateController,
                      additionalCostController,
                      discountController,
                    )) {
                      final newFareRate = FareRateInsertRequestModel(
                        shiftId: rideShiftModel.shiftId,
                        distanceInMeter: int.parse(distanceController.text),
                        perMeterRate: double.parse(perMeterRateController.text),
                        additionalCost: double.parse(
                          additionalCostController.text,
                        ),
                        multiplePassengerDiscountInPercent: double.parse(
                          discountController.text,
                        ),
                      );

                      fareRateBloc.add(ManageFareRateEvent.insert(newFareRate));

                      Navigator.of(context).pop();
                    }
                  },
                  child: const Text('Add'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showEditFareRateDialog(int index) {
    final item = fareRates[index];
    final distanceController = TextEditingController(
      text: item.distanceInMeter.toString(),
    );
    final perMeterRateController = TextEditingController(
      text: item.perMeterRate.toString(),
    );
    final additionalCostController = TextEditingController(
      text: item.additionalCost.toString(),
    );
    final discountController = TextEditingController(
      text: item.multiplePassengerDiscountInPercent.toString(),
    );
    bool isActiveRate = item.isActive;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(
                'Edit Fare Rate',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildDialogTextField(
                      'Distance (meters)',
                      distanceController,
                      TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    _buildDialogTextField(
                      'Rate per Meter',
                      perMeterRateController,
                      TextInputType.numberWithOptions(decimal: true),
                    ),
                    const SizedBox(height: 16),
                    _buildDialogTextField(
                      'Additional Cost',
                      additionalCostController,
                      TextInputType.numberWithOptions(decimal: true),
                    ),
                    const SizedBox(height: 16),
                    _buildDialogTextField(
                      'Multi-Passenger Discount (%)',
                      discountController,
                      TextInputType.numberWithOptions(decimal: true),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Active:',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w500),
                        ),
                        Switch(
                          value: isActiveRate,
                          onChanged: (value) {
                            setDialogState(() {
                              isActiveRate = value;
                            });
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),

                FilledButton(
                  onPressed: () {
                    if (_validateFareRateInputs(
                      distanceController,
                      perMeterRateController,
                      additionalCostController,
                      discountController,
                    )) {
                      final updatedFareRate = FareRateModel(
                        sn: item.sn,
                        isActive: isActiveRate,
                        shiftId: item.shiftId,
                        distanceInMeter: int.parse(distanceController.text),
                        perMeterRate: double.parse(perMeterRateController.text),
                        additionalCost: double.parse(
                          additionalCostController.text,
                        ),
                        multiplePassengerDiscountInPercent: double.parse(
                          discountController.text,
                        ),
                      );

                      setState(() {
                        fareRates[index] = updatedFareRate;
                        hasChanged = true;
                      });

                      fareRateBloc.add(
                        ManageFareRateEvent.update(updatedFareRate),
                      );

                      Navigator.of(context).pop();
                    }
                  },
                  child: const Text('Update'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  bool _validateFareRateInputs(
    TextEditingController distance,
    TextEditingController perMeterRate,
    TextEditingController additionalCost,
    TextEditingController discount,
  ) {
    if (distance.text.isEmpty ||
        perMeterRate.text.isEmpty ||
        additionalCost.text.isEmpty ||
        discount.text.isEmpty) {
      // CustomToast.showError('Please fill all fields');
      return false;
    }

    try {
      int.parse(distance.text);
      double.parse(perMeterRate.text);
      double.parse(additionalCost.text);
      double.parse(discount.text);
      return true;
    } catch (e) {
      // CustomToast.showError('Please enter valid numbers');
      return false;
    }
  }

  Widget _buildDialogTextField(
    String label,
    TextEditingController controller,
    TextInputType keyboardType,
  ) {
    return TextField(
      controller: controller,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 12,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocListener<ManageFareRateBloc, ManageFareRateState>(
      bloc: fareRateBloc,
      listener: _manageFareRateStateBlocListenr,
      child: Scaffold(
        appBar: AppBar(title: const Text('Shift Details')),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          rideShiftModel.shiftName,
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          rideShiftModel.vehicleTypeName,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  OptionalSwitch(
                    value: isActive,
                    onChanged: (value) {
                      setState(() {
                        isActive = value;
                        hasChanged = true;
                      });
                    },
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Shift Info Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Shift Information',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildEditableField("Shift Name", shiftNameController),
                      _buildEditableField(
                        "Vehicle Type",
                        vehicleTypeController,
                        true,
                      ),
                      _buildTimeField(
                        label: "Start Time",
                        time: startTime,
                        isStartTime: true,
                      ),
                      _buildTimeField(
                        label: "End Time",
                        time: endTime,
                        isStartTime: false,
                      ),
                      const SizedBox(height: 16),
                      BlocListener<ManageRideShiftBloc, ManageRideShiftState>(
                        bloc: _manageRideShiftBloc,
                        listener: _manageRideShiftBlocListenr,
                        child: SizedBox(
                          width: double.infinity,
                          child: CustomButtonPrimary(
                            title: "Update",
                            onPressed: hasChanged ? _handleUpdate : null,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Fare Rates Section
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Fare Rates',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  FloatingActionButton.small(
                    onPressed: _showAddFareRateDialog,
                    child: const Icon(Icons.add),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              if (fareRates.isEmpty)
                _emptyFareRate(theme)
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: fareRates.length,
                  itemBuilder: (context, index) {
                    final item = fareRates[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        onTap: () => _showEditFareRateDialog(index),
                        leading: Container(
                          width: 8,
                          height: 40,
                          decoration: BoxDecoration(
                            color:
                                item.isActive
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        title: Text('${item.distanceInMeter} meters'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Rate: रु ${item.perMeterRate}/m • Extra: रु ${item.additionalCost}',
                            ),
                            Text(
                              'Discount: ${item.multiplePassengerDiscountInPercent}%',
                            ),
                          ],
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    item.isActive
                                        ? theme.colorScheme.primaryContainer
                                        : theme
                                            .colorScheme
                                            .surfaceContainerHighest,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                item.isActive ? 'Active' : 'Inactive',
                                style: theme.textTheme.labelSmall?.copyWith(
                                  color:
                                      item.isActive
                                          ? theme.colorScheme.onPrimaryContainer
                                          : theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              Icons.edit_outlined,
                              size: 20,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }

  Card _emptyFareRate(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.monetization_on_outlined,
                size: 48,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(height: 12),
              Text(
                'No fare rates yet',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Tap + to add your first fare rate',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _manageRideShiftBlocListenr(
    BuildContext context,
    ManageRideShiftState state,
  ) {
    state.whenOrNull(
      loading: () => AppLoadingDialog.show(context),
      loaded: (msg) {
        CustomToast.showSuccess(msg);
        AppLoadingDialog.hide(context);
        sl<GetRideShiftBloc>().add(GetRideShiftEvent.get(false, true));
        context.pop();
      },
      failure: (f) {
        CustomToast.showError(f.message);
        AppLoadingDialog.hide(context);
      },
    );
  }

  void _manageFareRateStateBlocListenr(
    BuildContext context,
    ManageFareRateState state,
  ) {
    state.whenOrNull(
      loading: () => AppLoadingDialog.show(context),
      loaded: (msg) {
        CustomToast.showSuccess(msg);
        AppLoadingDialog.hide(context);

        sl<GetRideShiftBloc>().add(GetRideShiftEvent.get(false, true));
        context.pop();
      },
      failure: (f) {
        CustomToast.showError(f.message);
        AppLoadingDialog.hide(context);
      },
    );
  }

  Widget _buildEditableField(
    String label,
    TextEditingController controller, [
    bool readOnly = false,
  ]) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        readOnly: readOnly,
        enabled: !readOnly,
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildTimeField({
    required String label,
    TimeOfDay? time,
    bool isStartTime = false,
  }) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _selectTime(context, isStartTime),
        child: InputDecorator(
          decoration: InputDecoration(
            labelText: label,
            border: const OutlineInputBorder(),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
            suffixIcon: const Icon(Icons.access_time),
          ),
          child: Text(
            time != null
                ? AppDateTimeUtils.getTimeInHHMMAndPeriod(time)
                : 'Select $label',
            style:
                time != null
                    ? theme.textTheme.bodyMedium
                    : theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
          ),
        ),
      ),
    );
  }
}
