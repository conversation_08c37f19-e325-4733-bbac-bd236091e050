import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/features/admin/core/layout/widgets/desktop_sidebar.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class CustomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;
  final List<CustomNavItem> items;

  const CustomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color:
            Theme.of(context).bottomNavigationBarTheme.backgroundColor ??
            Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children:
              items.map((item) {
                final int index = items.indexOf(item);
                final bool isSelected = index == currentIndex;

                return GestureDetector(
                  onTap: () => onTap(index),
                  behavior: HitTestBehavior.opaque,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        item.icon,
                        size: 24, // Adjust size as needed
                        color:
                            isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(
                                  context,
                                ).colorScheme.onSurface.withOpacity(0.6),
                      ),
                      const SizedBox(height: 4),
                      if (item.label != null)
                        Text(
                          item.label!,
                          style: TextStyle(
                            fontSize: 12,
                            color:
                                isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(
                                      context,
                                    ).colorScheme.onSurface.withOpacity(0.8),
                          ),
                        ),
                    ],
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }
}

class CustomNavItem {
  final IconData icon;
  final String? label;

  const CustomNavItem({required this.icon, this.label});
}

class AdminLayoutScaffold extends StatelessWidget {
  final StatefulNavigationShell navigationShell;

  const AdminLayoutScaffold({super.key, required this.navigationShell});

  void _onTap(int index, {required BuildContext context}) {
    navigationShell.goBranch(
      index,
      initialLocation: index == navigationShell.currentIndex,
    );
  }

  @override
  Widget build(BuildContext context) {
    final int currentIndex = navigationShell.currentIndex;
    final bool isLargeScreen = MediaQuery.of(context).size.width > 800;

    final List<CustomNavItem> navItems = [
      const CustomNavItem(
        icon: LucideIcons.layoutDashboard,
        label: 'Dashboard',
      ),
      const CustomNavItem(icon: LucideIcons.users, label: 'Users'),
      const CustomNavItem(icon: LucideIcons.settings, label: 'Settings'),
      const CustomNavItem(icon: LucideIcons.creditCard, label: 'Payments'),
      const CustomNavItem(icon: LucideIcons.userCog, label: 'Riders'),
    ];

    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
        automaticallyImplyLeading: !isLargeScreen,
      ),
      drawer:
          isLargeScreen
              ? null
              // : MobileDrawer(
              //   selectedIndex: currentIndex,
              //   onItemTapped: (index) => _onTap(index, context: context),
              // ),
              : Drawer(),
      body: Row(
        children: [
          if (isLargeScreen)
            DesktopSidebar(
              selectedIndex: currentIndex,
              onItemTapped: (index) => _onTap(index, context: context),
            ),
          Expanded(child: navigationShell),
        ],
      ),
      bottomNavigationBar:
          isLargeScreen
              ? null
              : CustomNavigationBar(
                currentIndex: currentIndex,
                onTap: (index) => _onTap(index, context: context),
                items: navItems,
              ),
    );
  }
}
