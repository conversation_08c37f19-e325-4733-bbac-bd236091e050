import 'package:flutter/material.dart';

class MobileDrawer extends StatelessWidget {
  final int selectedIndex;
  final void Function(int index) onItemTapped;

  const MobileDrawer({
    super.key,
    required this.selectedIndex,
    required this.onItemTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Builder(
        // We need a new context here to access Scaffold
        builder: (drawerContext) => ListView(
          children: [
            _buildTile(drawerContext, Icons.dashboard, 'Dashboard', 0),
            _buildTile(drawerContext, Icons.people, 'Users', 1),
            _buildTile(drawerContext, Icons.settings, 'Settings', 2),
            _buildTile(drawerContext, Icons.payment, 'Payments', 3),
            _buildTile(drawerContext, Icons.assignment_ind, 'Roles', 4),
          ],
        ),
      ),
    );
  }

  Widget _buildTile(
    BuildContext context,
    IconData icon,
    String label,
    int index,
  ) {
    return ListTile(
      selected: selectedIndex == index,
      leading: Icon(icon),
      title: Text(label),
      onTap: () {
        Navigator.of(context).pop(); // ✅ This will only close the drawer
        onItemTapped(index); // This triggers navigation
      },
    );
  }
}
