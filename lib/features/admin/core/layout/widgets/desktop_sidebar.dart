import 'package:flutter/material.dart';

class DesktopSidebar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemTapped;

  const DesktopSidebar({
    super.key,
    required this.selectedIndex,
    required this.onItemTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280, // Slightly wider sidebar
      color: Colors.indigo.shade50,
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 20.0,
              vertical: 12.0,
            ), // Increased padding
            child: Text(
              'Admin Menu',
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                color: Colors.indigo,
                fontWeight: FontWeight.bold,
              ), // Larger title
            ),
          ),
          const Divider(indent: 20, endIndent: 20), // Separator
          SidebarTile(
            // Custom widget for sidebar items
            icon: Icons.dashboard,
            title: 'Dashboard',
            isSelected: selectedIndex == 0,
            onTap: () => onItemTapped(0),
          ),
          SidebarTile(
            icon: Icons.people,
            title: 'User Management',
            isSelected: selectedIndex == 1,
            onTap: () => onItemTapped(1),
          ),
          SidebarTile(
            icon: Icons.settings,
            title: 'Settings',
            isSelected: selectedIndex == 2,
            onTap: () => onItemTapped(2),
          ),
          SidebarTile(
            icon: Icons.payment,
            title: 'Payments',
            isSelected: selectedIndex == 3,
            onTap: () => onItemTapped(3),
          ),
          SidebarTile(
            icon: Icons.assignment_ind,
            title: 'Role List',
            isSelected: selectedIndex == 4,
            onTap: () => onItemTapped(4),
          ),
        ],
      ),
    );
  }
}

class SidebarTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  const SidebarTile({
    super.key,
    required this.icon,
    required this.title,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected ? Colors.indigo.withOpacity(0.15) : Colors.transparent,
        borderRadius: BorderRadius.circular(10),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? Colors.indigo : Colors.grey,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.indigo : Colors.grey.shade800,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
