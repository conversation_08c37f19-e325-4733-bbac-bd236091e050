import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/features/admin/core/blocs/change_email_password/change_email_password_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_admin_role_list/get_admin_role_list_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_admin_user_role/get_admin_user_role_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_user_list/manage_user_list_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_office/get_office_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_pending_rider/get_pending_rider_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_pending_rider_details/get_pending_rider_detail_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_fare_rate/manage_fare_rate_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_ride_shift/manage_ride_shift_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/update_office_setting/update_office_setting_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/user_detail/user_detail_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/user_list/user_list_bloc.dart';

import 'package:safari_yatri/features/admin/core/repositories/office_repository.dart';
import 'package:safari_yatri/features/admin/core/repositories/user_repository.dart';

Future<void> setupAdminDependencies() async {
  // Repositories
  sl.registerLazySingleton<OfficeRepository>(() => OfficeRepositoryI(sl()));

  sl.registerLazySingleton<UserRepository>(() => UserRepositoryI(sl()));

  //blocs
  sl.registerLazySingleton<GetOfficeBloc>(() => GetOfficeBloc(repo: sl()));

  sl.registerLazySingleton<UserListBloc>(() => UserListBloc(repo: sl()));
  sl.registerLazySingleton<UserDetailBloc>(() => UserDetailBloc(repo: sl()));

  sl.registerLazySingleton<UpdateOfficeSettingBloc>(
    () => UpdateOfficeSettingBloc(repo: sl()),
  );
  sl.registerFactory(() => ManageUserListBloc(repo: sl()));

  sl.registerLazySingleton(() => ChangeEmailPasswordBloc(repo: sl()));
  sl.registerLazySingleton(() => GetPendingRiderBloc(repo: sl()));
  sl.registerLazySingleton(() => GetPendingRiderDetailBloc(repo: sl()));
  sl.registerFactory(() => ManageRideShiftBloc(repo: sl()));
  sl.registerFactory(() => ManageFareRateBloc(repo: sl()));

  sl.registerLazySingleton(() => GetAdminRoleListBloc(repo: sl()));
  sl.registerLazySingleton(() => GetAdminUserRoleBloc(repo: sl()));
  
}
