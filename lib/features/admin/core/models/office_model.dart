class OfficeModel {
  final String officeName;
  final String officeShortName;
  final String officeAddress;
  final String officePhone;
  final String officeEmail;
  final int otpLifeInSecond;
  final double riderRenewalCharge;
  final int riderRenewalFrequencyInDays;
  final double riderPointRateInPercent;
  final double passengerPointRateInPercent;
  final String fiscalYear;
  final bool autoLoginAfterOtpVerification;
  final int otpLimitPerDay;
  final int passengerCartLifeInMinute;

  OfficeModel({
    required this.officeName,
    required this.officeShortName,
    required this.officeAddress,
    required this.officePhone,
    required this.officeEmail,
    required this.otpLifeInSecond,
    required this.riderRenewalCharge,
    required this.riderRenewalFrequencyInDays,
    required this.riderPointRateInPercent,
    required this.passengerPointRateInPercent,
    required this.fiscalYear,
    required this.autoLoginAfterOtpVerification,
    required this.otpLimitPerDay,
    required this.passengerCartLifeInMinute,
  });

  Map<String, dynamic> toMap() {
    return {
      'OtpLimitPerDay': otpLimitPerDay,
      'OfficeName': officeName,
      'OfficeShortName': officeShortName,
      'OfficeAddress': officeAddress,
      'OfficePhone': officePhone,
      'OfficeEmail': officeEmail,
      'OtpLifeInSecond': otpLifeInSecond,
      'RiderRenewalCharge': riderRenewalCharge,
      'RiderRenewalFrequencyInDays': riderRenewalFrequencyInDays,
      'RiderPointRateInPercent': riderPointRateInPercent,
      'PassengerPointRateInPercent': passengerPointRateInPercent,
      'FiscalYear': fiscalYear,
      'AutoLoginAfterOtpVerification': autoLoginAfterOtpVerification,
      "PassengerCartLifeInMinute": passengerCartLifeInMinute,
    };
  }

  factory OfficeModel.fromMap(Map<String, dynamic> map) {
    return OfficeModel(
      otpLimitPerDay: map['OtpLimitPerDay'],
      officeName: map['OfficeName'],
      officeShortName: map['OfficeShortName'],
      officeAddress: map['OfficeAddress'],
      officePhone: map['OfficePhone'],
      officeEmail: map['OfficeEmail'],
      otpLifeInSecond: map['OtpLifeInSecond'],
      riderRenewalCharge: map['RiderRenewalCharge']?.toDouble(),
      riderRenewalFrequencyInDays: map['RiderRenewalFrequencyInDays'],
      riderPointRateInPercent: map['RiderPointRateInPercent'],
      passengerPointRateInPercent: map['PassengerPointRateInPercent'],
      fiscalYear: map['FiscalYear'],
      autoLoginAfterOtpVerification: map['AutoLoginAfterOtpVerification'],
      passengerCartLifeInMinute: map['PassengerCartLifeInMinute'],
    );
  }

  OfficeModel copyWith({
    String? officeName,
    String? officeShortName,
    String? officeAddress,
    String? officePhone,
    String? officeEmail,
    int? otpLifeInSecond,
    double? riderRenewalCharge,
    int? riderRenewalFrequencyInDays,
    double? riderPointRateInPercent,
    double? passengerPointRateInPercent,
    String? fiscalYear,
    bool? autoLoginAfterOtpVerification,
    String? officeEmailPassword,
    int? otpLimitPerDay,
    int? passengerCartLifeInMinute,
  }) {
    return OfficeModel(
      officeName: officeName ?? this.officeName,
      officeShortName: officeShortName ?? this.officeShortName,
      officeAddress: officeAddress ?? this.officeAddress,
      officePhone: officePhone ?? this.officePhone,
      officeEmail: officeEmail ?? this.officeEmail,
      otpLifeInSecond: otpLifeInSecond ?? this.otpLifeInSecond,
      riderRenewalCharge: riderRenewalCharge ?? this.riderRenewalCharge,
      riderRenewalFrequencyInDays:
          riderRenewalFrequencyInDays ?? this.riderRenewalFrequencyInDays,
      riderPointRateInPercent:
          riderPointRateInPercent ?? this.riderPointRateInPercent,
      passengerPointRateInPercent:
          passengerPointRateInPercent ?? this.passengerPointRateInPercent,
      fiscalYear: fiscalYear ?? this.fiscalYear,
      autoLoginAfterOtpVerification:
          autoLoginAfterOtpVerification ?? this.autoLoginAfterOtpVerification,
      otpLimitPerDay: otpLimitPerDay ?? this.otpLimitPerDay,
      passengerCartLifeInMinute:
          passengerCartLifeInMinute ?? this.passengerCartLifeInMinute,
    );
  }
}
