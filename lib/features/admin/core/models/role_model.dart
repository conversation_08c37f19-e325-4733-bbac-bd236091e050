class GetAdminRoleModel {
  final int roleId;
  final String roleName;

  GetAdminRoleModel({required this.roleId, required this.roleName});

  factory GetAdminRoleModel.fromMap(Map<String, dynamic> map) {
    return GetAdminRoleModel(roleId: map['RoleId'], roleName: map['RoleName']);
  }
}

///For admin roles
class AdminRoleUpdateModel {
  final int roleId;
  final bool selected;

  AdminRoleUpdateModel({required this.roleId, required this.selected});

  Map<String, dynamic> toMap() {
    return {'RoleId': roleId, 'Selected': selected};
  }
}
