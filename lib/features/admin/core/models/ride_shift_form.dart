class RideShiftForm {
  final String shiftName;
  final String startTime;
  final String endTime;
  final int vehicleTypeId;
  RideShiftForm({
    required this.shiftName,
    required this.startTime,
    required this.endTime,
    required this.vehicleTypeId,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      "ShiftName": shiftName,
      "StartTime": startTime,
      "EndTime": endTime,
      "VehicleTypeId": vehicleTypeId,
    };
  }
}
