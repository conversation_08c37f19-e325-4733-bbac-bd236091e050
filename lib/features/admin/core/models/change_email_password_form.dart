


class ChangeEmailPasswordForm {
  final String oldPassword;
  final String password;
  final String confirmPassword;
  ChangeEmailPasswordForm({
    required this.oldPassword,
    required this.password,
    required this.confirmPassword,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'oldPassword': oldPassword,
      'password': password,
      'confirmPassword': confirmPassword,
    };
  }

}
