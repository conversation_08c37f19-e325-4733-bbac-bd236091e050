import 'dart:convert';

class UserModel {
  final String? profilePicture;
  final String? address;
  final String userName;
  final String phoneNo;
  final String gender;
  final String emailAddress;
  final double homeLatitude;
  final double homeLongitude;
  final double currentLatitude;
  final double currentLongitude;
  final int userId;
  final String loginId;
  final String loginStatus;
  final String userType;
  final String registeredDate;
  final String registeredTime;
  final String? otpSentDate;
  final String? otpSentTime;
  final String? activationDate;
  final String? activationTime;
  final double walletBalance;

  // Rider specific fields
  final String? riderVerifiedDate;
  final String? riderVerifiedTime;
  final bool? isAvailable;
  final String? riderAppliedDate;
  final String? riderAppliedTime;
  final int? riderVerifiedBy;

  UserModel({
    this.profilePicture,
    this.address,
    required this.userName,
    required this.phoneNo,
    required this.gender,
    required this.emailAddress,
    required this.homeLatitude,
    required this.homeLongitude,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.userId,
    required this.loginId,
    required this.loginStatus,
    required this.userType,
    required this.registeredDate,
    required this.registeredTime,
    this.otpSentDate,
    this.otpSentTime,
    this.activationDate,
    this.activationTime,
    required this.walletBalance,
    this.riderVerifiedDate,
    this.riderVerifiedTime,
    this.isAvailable,
    this.riderAppliedDate,
    this.riderAppliedTime,
    this.riderVerifiedBy,
  });

  Map<String, dynamic> toMap() {
    return {
      'ProfilePicture': profilePicture,
      'UserAddress': address,
      'UserName': userName,
      'PhoneNo': phoneNo,
      'Gender': gender,
      'EmailAddress': emailAddress,
      'HomeLatitude': homeLatitude,
      'HomeLongitude': homeLongitude,
      'CurrentLatitude': currentLatitude,
      'CurrentLongitude': currentLongitude,
      'UserId': userId,
      'LoginId': loginId,
      'LoginStatus': loginStatus,
      'UserType': userType,
      'RegisteredDate': registeredDate,
      'RegisteredTime': registeredTime,
      'OtpSentDate': otpSentDate,
      'OtpSentTime': otpSentTime,
      'ActivationDate': activationDate,
      'ActivationTime': activationTime,
      'WalletBalance': walletBalance,
      'RiderVerifiedDate': riderVerifiedDate,
      'RiderVerifiedTime': riderVerifiedTime,
      'IsAvailable': isAvailable,
      'RiderAppliedDate': riderAppliedDate,
      'RiderAppliedTime': riderAppliedTime,
      'RiderVerifiedBy': riderVerifiedBy,
    };
  }

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      profilePicture: map['ProfilePicture'],
      address: map['UserAddress'],
      userName: map['UserName'] ?? '',
      phoneNo: map['PhoneNo'] ?? '',
      gender: map['Gender'] ?? '',
      emailAddress: map['EmailAddress'] ?? '',
      homeLatitude: map['HomeLatitude']?.toDouble() ?? 0.0,
      homeLongitude: map['HomeLongitude']?.toDouble() ?? 0.0,
      currentLatitude: map['CurrentLatitude']?.toDouble() ?? 0.0,
      currentLongitude: map['CurrentLongitude']?.toDouble() ?? 0.0,
      userId: map['UserId']?.toInt() ?? 0,
      loginId: map['LoginId'] ?? '',
      loginStatus: map['LoginStatus'] ?? '',
      userType: map['UserType'] ?? '',
      registeredDate: map['RegisteredDate'] ?? '',
      registeredTime: map['RegisteredTime'] ?? '',
      otpSentDate: map['OtpSentDate'],
      otpSentTime: map['OtpSentTime'],
      activationDate: map['ActivationDate'],
      activationTime: map['ActivationTime'],
      walletBalance: map['WalletBalance']?.toDouble() ?? 0.0,
      riderVerifiedDate: map['RiderVerifiedDate'],
      riderVerifiedTime: map['RiderVerifiedTime'],
      isAvailable: map['IsAvailable'],
      riderAppliedDate: map['RiderAppliedDate'],
      riderAppliedTime: map['RiderAppliedTime'],
      riderVerifiedBy: map['RiderVerifiedBy']?.toInt(),
    );
  }

  String toJson() => json.encode(toMap());

  factory UserModel.fromJson(String source) =>
      UserModel.fromMap(json.decode(source));

  bool get isRider => userType.toLowerCase() == 'rider';
  bool get isPassenger => userType.toLowerCase() == 'passenger';
  bool get isAdmin => userType.toLowerCase() == 'admin';
  bool get isPendingRider =>
      isRider && riderVerifiedBy == null && riderAppliedDate != null;
}
