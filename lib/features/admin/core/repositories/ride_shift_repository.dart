import 'package:dartz/dartz.dart';
import 'package:safari_yatri/common/extensions/mid_night_cached_expiration.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/network/api_service.dart';
import 'package:safari_yatri/core/models/ride_shift_model.dart';
import 'package:safari_yatri/core/services/app_cached_keys.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';
import 'package:safari_yatri/features/admin/core/models/ride_shift_form.dart';
import '../models/fare_rate_insert_request_model.dart';
import '../models/fare_rate_model.dart';

abstract interface class RideShiftRepository {
  FutureEither<List<RideShiftModel>> getRideShiftList(
    bool includeBlocked, {
    bool forceRefresh = false,
  });

  FutureEither<RideShiftModel> getRideShiftById(int id);

  FutureEither<void> updateRideShift(RideShiftModel rideShift);

  FutureEither<void> updateFareRate(FareRateModel fareRateItem);

  FutureEither<void> insertRideShift(RideShiftForm request);

  FutureEither<void> insertFareRate(FareRateInsertRequestModel request);
}

class RideShiftRepositoryI implements RideShiftRepository {
  final ApiService _apiService;

  final CoreLocalDataSource _local = CoreLocalDataSource();

  RideShiftRepositoryI({required ApiService apiService})
    : _apiService = apiService;

  //##-------------------RIDE SHIFT-------------------##
  @override
  FutureEither<List<RideShiftModel>> getRideShiftList(
    bool includeBlocked, {
    bool forceRefresh = true,
  }) async {
   final lastFetchedTime =  _local.getTimestamp(AppCachedKeys.rideShift);
    if (!forceRefresh && lastFetchedTime?.isExpiredByMidnight==false) {
      final cachedRideShifts = _local.getModelList<RideShiftModel>(
        AppCachedKeys.rideShift,
        (data) => RideShiftModel.fromMap(data),
      );
      if (cachedRideShifts.isNotEmpty) {
        return Right(cachedRideShifts);
      }
    }

    final failureOrRideShifts = await _apiService.get<List<RideShiftModel>>(
      'RideShift/GetList/$includeBlocked',
      fromJson: (data) {
        if (data is List) {
          return data.map((item) => RideShiftModel.fromMap(item)).toList();
        }
        return [];
      },
    );

    return await failureOrRideShifts.fold(
      (failure) {
        return Left(failure);
      },
      (rideShifts) async {
        if (!includeBlocked) {
          await _local.setTimestamp(AppCachedKeys.rideShift, DateTime.now());
          await _local.saveModelList(
            AppCachedKeys.rideShift,
            rideShifts.map((e) => e.toMap()).toList(),
          );
        }
        return Right(rideShifts);
      },
    );
  }

  //##-------------------GET RIDE SHIFT BY ID-------------------##
  @override
  FutureEither<RideShiftModel> getRideShiftById(int id) async {
    return await _apiService.get<RideShiftModel>(
      'RideShift/GetItem/$id',
      fromJson: (data) => RideShiftModel.fromMap(data),
    );
  }

  //##-------------------UPDATE RIDESHIFT BY ID-------------------##
  @override
  FutureEither<void> updateRideShift(RideShiftModel rideShift) async {
    return await _apiService.put<void>(
      'RideShift/Update/${rideShift.shiftId}',
      data: rideShift.toMap(),
      fromJson: (_) {},
    );
  }

  //##------------------UPDATE FARE RATE---------------------------##
  @override
  FutureEither<void> updateFareRate(FareRateModel fareRateItem) async {
    return await _apiService.put<void>(
      'RideShift/UpdateFareRate',
      data: fareRateItem.toMap(),
      fromJson: (_) {}, // no response parsing needed
    );
  }

  //##------------------INSERT RIDE SHIFT---------------------------##
  @override
  FutureEither<void> insertRideShift(RideShiftForm request) async {
    return await _apiService.post<void>(
      'RideShift/Insert',
      data: request.toMap(),
      fromJson: (_) {}, // no response parsing needed
    );
  }

  //##------------------INSERT FARE RATE---------------------------##

  @override
  FutureEither<void> insertFareRate(FareRateInsertRequestModel request) async {
    return await _apiService.post<void>(
      'RideShift/InsertFareRate',
      data: request.toMap(),
      fromJson: (_) {}, // no response parsing needed
    );
  }
}
