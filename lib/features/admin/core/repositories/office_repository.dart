import 'package:dartz/dartz.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/network/api_service.dart';
import 'package:safari_yatri/core/services/app_cached_keys.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';
import 'package:safari_yatri/features/admin/core/models/change_email_password_form.dart';
import 'package:safari_yatri/features/admin/core/models/office_model.dart';

abstract interface class OfficeRepository {
  Future<Either<Failure, OfficeModel>> getOfficeInfo();
  Future<Either<Failure, String>> updateOfficeInfo(OfficeModel office);
  Future<Either<Failure, String>> changeEmailPassword(
    ChangeEmailPasswordForm form,
  );
}

class OfficeRepositoryI implements OfficeRepository {
  final ApiService _apiService;
  final CoreLocalDataSource _local = CoreLocalDataSource();

  OfficeRepositoryI(this._apiService);

  @override
  Future<Either<Failure, OfficeModel>> getOfficeInfo() async {
    // final cachedTime = _local.getTimestamp(AppCachedKeys.officeData);
    // final officeModel = _local.getModel<OfficeModel>(
    //   AppCachedKeys.officeData,
    //   (json) => OfficeModel.fromMap(json),
    // );

    // if (officeModel != null &&
    //     cachedTime != null &&
    //     !cachedTime.isExpiredByMidnight) {
    //   return Right(officeModel);
    // }

    final result = await _apiService.get<OfficeModel>(
      'Office/GetItem',
      fromJson: (data) => OfficeModel.fromMap(data),
    );

    return result.fold((failure) => Left(failure), (model) async {
      await _local.saveModel(AppCachedKeys.officeData, model.toMap());
      await _local.setTimestamp(AppCachedKeys.officeData, DateTime.now());
      return Right(model);
    });
  }

  @override
  Future<Either<Failure, String>> updateOfficeInfo(OfficeModel office) async {
    final result = await _apiService.put<String>(
      'Office/Update',
      data: office.toMap(),
      fromJson: (data) => "Success",
    );

    return result.fold((failure) => Left(failure), (success) async {
      await _local.clear(AppCachedKeys.officeData);
      return Right(success);
    });
  }

  @override
  Future<Either<Failure, String>> changeEmailPassword(
    ChangeEmailPasswordForm form,
  ) async {
    final result = await _apiService.put<String>(
      queryParameters: form.toMap(),
      'Office/ChangeEmailPassword',
      fromJson: (data) => "Success",
    );

    return result.fold((failure) => Left(failure), (success) async {
      await _local.clear(AppCachedKeys.officeData);
      return Right(success);
    });
  }
}
