import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/network/api_service.dart';
import 'package:safari_yatri/features/admin/core/models/pending_riders_detail.dart';
import 'package:safari_yatri/features/admin/core/models/rider_model.dart';
import 'package:safari_yatri/features/admin/core/models/role_model.dart';
import 'package:safari_yatri/features/admin/core/models/user_model.dart';

/// User repository
abstract interface class UserRepository {
  FutureEither<List<UserModel>> getUserList(
    String userType,
    String loginStatus,
  );
  FutureEither<List<RiderApplication>> getPendingRiderList();
  FutureEither<RidersDetailsModel> getPendingRiderDetails(int userId);
  FutureEither<UserModel> getUserById(int id);
  FutureEither<void> insertUser(UserModel user);
  FutureEither<void> updateUser(UserModel user);
  FutureEither<void> archiveUser(int id);
  FutureEither<void> activateLogin(int id);
  FutureEither<void> disableLogin(int id);
  FutureEither<void> resetPassword(int id);
  FutureEither<List<GetAdminRoleModel>> getAdminUserRoles(int id);
  FutureEither<List<GetAdminRoleModel>> getAdminRolesList();

  FutureEither<String> updateRoles(int id, List<AdminRoleUpdateModel> roles);
  FutureEither<String> changeUserType(int id, String userType);
}

class UserRepositoryI implements UserRepository {
  final ApiService _apiService;

  UserRepositoryI(this._apiService);

  @override
  FutureEither<List<UserModel>> getUserList(
    String userType,
    String loginStatus,
  ) async {
    return await _apiService.get<List<UserModel>>(
      'UserList/GetList/$userType/$loginStatus',
      fromJson: (data) {
        if (data is List) {
          return data.map((item) => UserModel.fromMap(item)).toList();
        }
        return [];
      },
    );
  }

  /// Get pending rider list
  @override
  FutureEither<List<RiderApplication>> getPendingRiderList() async {
    return await _apiService.get<List<RiderApplication>>(
      'UserList/GetPendingRiderList',
      fromJson: (data) {
        if (data is List) {
          return data.map((item) => RiderApplication.fromMap(item)).toList();
        }
        return [];
      },
    );
  }

  /// Get pending rider details

  @override
  FutureEither<RidersDetailsModel> getPendingRiderDetails(int userId) async {
    return await _apiService.get(
      'UserList/GetRiderDetail/$userId',
      fromJson: (data) {
        return RidersDetailsModel.fromJson(data);
      },
    );
  }

  /// Get user details by ID
  @override
  FutureEither<UserModel> getUserById(int id) async {
    return await _apiService.get<UserModel>(
      'UserList/GetItem/$id',
      fromJson: (data) => UserModel.fromMap(data),
    );
  }

  @override
  FutureEither<void> insertUser(UserModel user) async {
    return await _apiService.post<void>(
      'UserList/Insert',
      data: user.toMap(),
      fromJson: (_) {},
    );
  }

  @override
  FutureEither<void> updateUser(UserModel user) async {
    return await _apiService.put<void>(
      'UserList/Update',
      data: user.toMap(),
      fromJson: (_) {},
    );
  }

  @override
  FutureEither<void> archiveUser(int id) async {
    return await _apiService.delete<void>(
      'UserList/Archive/$id',
      fromJson: (_) {},
    );
  }

  @override
  FutureEither<void> activateLogin(int id) async {
    return await _apiService.put<void>(
      'UserList/ActivateLogin?id=$id',
      fromJson: (_) {},
    );
  }

  @override
  FutureEither<void> disableLogin(int id) async {
    return await _apiService.put<void>(
      'UserList/DisableLogin/$id',
      fromJson: (_) {},
    );
  }

  @override
  FutureEither<void> resetPassword(int id) async {
    return await _apiService.put<void>(
      'UserList/ResetPassword/$id',
      fromJson: (_) {},
    );
  }

  @override
  FutureEither<List<GetAdminRoleModel>> getAdminUserRoles(int id) async {
    return await _apiService.get<List<GetAdminRoleModel>>(
      'UserList/GetAdminRoles/$id',
      fromJson: (data) {
        if (data is List) {
          return data.map((e) => GetAdminRoleModel.fromMap(e)).toList();
        }
        return [];
      },
    );
  }

  @override
  FutureEither<String> updateRoles(
    int id,
    List<AdminRoleUpdateModel> roles,
  ) async {
    return await _apiService.put<String>(
      'UserList/UpdateRoles/$id',
      data: roles.map((e) => e.toMap()).toList(),
    );
  }

  @override
  FutureEither<String> changeUserType(int id, String userType) async {
    return await _apiService.put<String>(
      'UserList/ChangeUserType?userId=$id&userType=$userType',
    );
  }

  @override
  FutureEither<List<GetAdminRoleModel>> getAdminRolesList() async {
    return await _apiService.get<List<GetAdminRoleModel>>(
      'RoleList/GetList',
      fromJson: (data) {
        if (data is List) {
          return data.map((e) => GetAdminRoleModel.fromMap(e)).toList();
        }
        return [];
      },
    );
  }
}
