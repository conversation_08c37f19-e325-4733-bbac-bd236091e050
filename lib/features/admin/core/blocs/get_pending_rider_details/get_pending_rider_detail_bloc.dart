import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/pending_riders_detail.dart';
import 'package:safari_yatri/features/admin/core/repositories/user_repository.dart';

part 'get_pending_rider_detail_event.dart';
part 'get_pending_rider_detail_state.dart';
part 'get_pending_rider_detail_bloc.freezed.dart';

class GetPendingRiderDetailBloc
    extends Bloc<GetPendingRiderDetailEvent, GetPendingRiderDetailState> {
  final UserRepository _userRepository;

  // In-memory cache
  final Map<int, RidersDetailsModel> _cache = {};

  GetPendingRiderDetailBloc({required UserRepository repo})
      : _userRepository = repo,
        super(GetPendingRiderDetailState.initial()) {
    on<_Get>(_onGet);
  }

  Future<void> _onGet(
    _Get event,
    Emitter<GetPendingRiderDetailState> emit,
  ) async {
    final cachedData = _cache[event.riderId];

    if (cachedData != null) {
      emit(GetPendingRiderDetailState.loaded(cachedData));
      return;
    }

    emit(GetPendingRiderDetailState.loading());

    final result = await _userRepository.getPendingRiderDetails(event.riderId);

    result.fold(
      (failure) => emit(GetPendingRiderDetailState.failure(failure)),
      (data) {
        _cache[event.riderId] = data; // Store in cache
        emit(GetPendingRiderDetailState.loaded(data));
      },
    );
  }
}
