import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/office_model.dart';
import 'package:safari_yatri/features/admin/core/repositories/office_repository.dart';

part 'update_office_setting_event.dart';
part 'update_office_setting_state.dart';
part 'update_office_setting_bloc.freezed.dart';

class UpdateOfficeSettingBloc
    extends Bloc<UpdateOfficeSettingEvent, UpdateOfficeSettingState> {
  final OfficeRepository _officeRepository;
  UpdateOfficeSettingBloc({required OfficeRepository repo})
    : _officeRepository = repo,
      super(UpdateOfficeSettingState.initial()) {
    on<_Update>(_onUpdate);
  }

  Future<void> _onUpdate(
    _Update event,
    Emitter<UpdateOfficeSettingState> emit,
  ) async {
    emit(UpdateOfficeSettingState.loading());
    // log(jsonEncode(event.office.toMap()));

    final result = await _officeRepository.updateOfficeInfo(event.office);

    result.fold(
      (failure) => emit(UpdateOfficeSettingState.failure(failure)),
      (data) =>
          emit(UpdateOfficeSettingState.loaded("Office updated successfully")),
    );
  }
}
