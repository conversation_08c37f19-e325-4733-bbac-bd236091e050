import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/rider_model.dart';
import 'package:safari_yatri/features/admin/core/repositories/user_repository.dart';

part 'get_pending_rider_event.dart';
part 'get_pending_rider_state.dart';
part 'get_pending_rider_bloc.freezed.dart';

class GetPendingRiderBloc
    extends Bloc<GetPendingRiderEvent, GetPendingRiderState> {
  List<RiderApplication> _pendingRiders = [];

  final UserRepository _userRepository;
  GetPendingRiderBloc({required UserRepository repo})
    : _userRepository = repo,
      super(const GetPendingRiderState.initial()) {
    on<_Get>(_onGetPendingRiderEvent);
    on<_RemoveLocally>(_onRemoveLocally);
  }

  Future<void> _onGetPendingRiderEvent(
    GetPendingRiderEvent event,
    Emitter<GetPendingRiderState> emit,
  ) async {
    emit(const GetPendingRiderState.loading());

    final result = await _userRepository.getPendingRiderList();

    result.fold((failure) => emit(GetPendingRiderState.failure(failure)), (
      users,
    ) {
      _pendingRiders = users;
      emit(GetPendingRiderState.loaded(_pendingRiders));
    });
  }

  Future<void> _onRemoveLocally(
    _RemoveLocally event,
    Emitter<GetPendingRiderState> emit,
  ) async {
    emit(const GetPendingRiderState.initial());
    _pendingRiders.removeWhere((element) => element.userId == event.userId);
    emit(GetPendingRiderState.loaded(_pendingRiders));
  }
}
