// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserListEvent {
  String get userType;
  String get loginStatus;
  bool get forceFetch;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserListEvent &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.loginStatus, loginStatus) ||
                other.loginStatus == loginStatus) &&
            (identical(other.forceFetch, forceFetch) ||
                other.forceFetch == forceFetch));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, userType, loginStatus, forceFetch);

  @override
  String toString() {
    return 'UserListEvent(userType: $userType, loginStatus: $loginStatus, forceFetch: $forceFetch)';
  }
}

/// Adds pattern-matching-related methods to [UserListEvent].
extension UserListEventPatterns on UserListEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetUserList value)? getUserList,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GetUserList() when getUserList != null:
        return getUserList(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetUserList value) getUserList,
  }) {
    final _that = this;
    switch (_that) {
      case _GetUserList():
        return getUserList(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetUserList value)? getUserList,
  }) {
    final _that = this;
    switch (_that) {
      case _GetUserList() when getUserList != null:
        return getUserList(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String userType, String loginStatus, bool forceFetch)?
    getUserList,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GetUserList() when getUserList != null:
        return getUserList(_that.userType, _that.loginStatus, _that.forceFetch);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      String userType,
      String loginStatus,
      bool forceFetch,
    )
    getUserList,
  }) {
    final _that = this;
    switch (_that) {
      case _GetUserList():
        return getUserList(_that.userType, _that.loginStatus, _that.forceFetch);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String userType, String loginStatus, bool forceFetch)?
    getUserList,
  }) {
    final _that = this;
    switch (_that) {
      case _GetUserList() when getUserList != null:
        return getUserList(_that.userType, _that.loginStatus, _that.forceFetch);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _GetUserList implements UserListEvent {
  const _GetUserList({
    required this.userType,
    required this.loginStatus,
    this.forceFetch = false,
  });

  @override
  final String userType;
  @override
  final String loginStatus;
  @override
  @JsonKey()
  final bool forceFetch;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GetUserList &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.loginStatus, loginStatus) ||
                other.loginStatus == loginStatus) &&
            (identical(other.forceFetch, forceFetch) ||
                other.forceFetch == forceFetch));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, userType, loginStatus, forceFetch);

  @override
  String toString() {
    return 'UserListEvent.getUserList(userType: $userType, loginStatus: $loginStatus, forceFetch: $forceFetch)';
  }
}
