import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/role_model.dart';
import 'package:safari_yatri/features/admin/core/repositories/user_repository.dart';

part 'get_admin_role_list_event.dart';
part 'get_admin_role_list_state.dart';
part 'get_admin_role_list_bloc.freezed.dart';

class GetAdminRoleListBloc
    extends Bloc<GetAdminRoleListEvent, GetAdminRoleListState> {
  final UserRepository _repository;
  List<GetAdminRoleModel> _roles = [];

  GetAdminRoleListBloc({required UserRepository repo})
    : _repository = repo,
      super(const GetAdminRoleListState.initial()) {
    on<_Get>(_onGetAdminRoles);
  }

  Future<void> _onGetAdminRoles(
    _Get event,
    Emitter<GetAdminRoleListState> emit,
  ) async {
    if (_roles.isNotEmpty && !event.forceFetch) {
      emit(GetAdminRoleListState.loaded(_roles));
      return;
    }
    emit(GetAdminRoleListState.loading());

    final result = await _repository.getAdminRolesList();
    result.fold((l) => emit(GetAdminRoleListState.failure(l)), (r) {
      _roles = r;
      emit(GetAdminRoleListState.loaded(r));
    });
  }
}
