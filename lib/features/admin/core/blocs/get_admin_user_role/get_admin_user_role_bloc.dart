import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/role_model.dart';
import 'package:safari_yatri/features/admin/core/repositories/user_repository.dart';

part 'get_admin_user_role_event.dart';
part 'get_admin_user_role_state.dart';
part 'get_admin_user_role_bloc.freezed.dart';

///it will get admin user role for users

class GetAdminUserRoleBloc
    extends Bloc<GetAdminUserRoleEvent, GetAdminUserRoleState> {
  final UserRepository _repository;
  GetAdminUserRoleBloc({required UserRepository repo})
    : _repository = repo,
      super(GetAdminUserRoleState.initial()) {
    on<_Get>(_onGetAdminUserRole);
  }

  Future<void> _onGetAdminUserRole(
    _Get event,
    Emitter<GetAdminUserRoleState> emit,
  ) async {
    emit(GetAdminUserRoleState.loading());

    final result = await _repository.getAdminUserRoles(event.userId);

    result.fold(
      (l) => emit(GetAdminUserRoleState.failure(l)),
      (r) => emit(GetAdminUserRoleState.loaded(r)),
    );
  }
}
