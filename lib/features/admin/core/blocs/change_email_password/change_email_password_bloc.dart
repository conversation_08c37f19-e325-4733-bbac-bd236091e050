import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/change_email_password_form.dart';
import 'package:safari_yatri/features/admin/core/repositories/office_repository.dart';

part 'change_email_password_event.dart';
part 'change_email_password_state.dart';
part 'change_email_password_bloc.freezed.dart';

class ChangeEmailPasswordBloc
    extends Bloc<ChangeEmailPasswordEvent, ChangeEmailPasswordState> {
  final OfficeRepository _officeRepository;
  ChangeEmailPasswordBloc({required OfficeRepository repo})
    : _officeRepository = repo,
      super(ChangeEmailPasswordState.initial()) {
    on<_Update>(_onUpdate);
  }

  Future<void> _onUpdate(
    _Update event,
    Emitter<ChangeEmailPasswordState> emit,
  ) async {
    emit(ChangeEmailPasswordState.loading());

    final result = await _officeRepository.changeEmailPassword(event.form);

    result.fold(
      (failure) => emit(ChangeEmailPasswordState.failure(failure)),
      (data) =>
          emit(ChangeEmailPasswordState.loaded("Office updated successfully")),
    );
  }
}
