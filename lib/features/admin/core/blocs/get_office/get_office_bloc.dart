import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/office_model.dart';
import 'package:safari_yatri/features/admin/core/repositories/office_repository.dart';

part 'get_office_event.dart';
part 'get_office_state.dart';
part 'get_office_bloc.freezed.dart';

class GetOfficeBloc extends Bloc<GetOfficeEvent, GetOfficeState> {
  final OfficeRepository _officeRepository;
  
  GetOfficeBloc({required OfficeRepository repo})
    : _officeRepository = repo,
      super(const GetOfficeState.initial()) {
    on<_Get>(_onGetOffice);
  }

  Future<void> _onGetOffice(_Get event, Emitter<GetOfficeState> emit) async {
    emit(const GetOfficeState.loading()); 

    final result = await _officeRepository.getOfficeInfo();

    result.fold(
      (failure) => emit(GetOfficeState.failure(failure)),
      (office) => emit(GetOfficeState.loaded(office)),
    );
  }
}
