import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/constant/string_constant.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/role_model.dart';
import 'package:safari_yatri/features/admin/core/models/user_model.dart';
import 'package:safari_yatri/features/admin/core/repositories/user_repository.dart';

part 'manage_user_list_event.dart';
part 'manage_user_list_state.dart';
part 'manage_user_list_bloc.freezed.dart';

class ManageUserListBloc
    extends Bloc<ManageUserListEvent, ManageUserListState> {
  final UserRepository _userListRepository;

  ManageUserListBloc({required UserRepository repo})
    : _userListRepository = repo,
      super(const ManageUserListState.initial()) {
    on<_InsertUser>(_onInsertUser);
    on<_UpdateUser>(_onUpdateUser);
    on<_ArchiveUser>(_onArchiveUser);
    on<_ActivateLogin>(_onActivateLogin);
    on<_DisableLogin>(_onDisableLogin);
    on<_ResetPassword>(_onResetPassword);
    on<_ChangeUserType>(_onChangeUserType);
    on<_UpdateRoles>(_onUpdateRoles);

    on<_AcceptForRider>(_onAcceptForRider);
    on<_RejectForRider>(_onRejectForRider);
  }
  Future<void> _onAcceptForRider(
    _AcceptForRider event,
    Emitter<ManageUserListState> emit,
  ) async {
    await _handleRequest(
      emit,
      () => _userListRepository.changeUserType(event.userId, kRiderRole),
      successMessage: "Approved successful",
    );
  }

  Future<void> _onRejectForRider(
    _RejectForRider event,
    Emitter<ManageUserListState> emit,
  ) async {
    await _handleRequest(
      emit,
      () => _userListRepository.changeUserType(event.userId, kPassengerRole),
      successMessage: "Rejected successful",
    );
  }

  Future<void> _onInsertUser(
    _InsertUser event,
    Emitter<ManageUserListState> emit,
  ) async {
    await _handleRequest(
      emit,
      () => _userListRepository.insertUser(event.user),
      successMessage: "User inserted",
    );
  }

  Future<void> _onUpdateUser(
    _UpdateUser event,
    Emitter<ManageUserListState> emit,
  ) async {
    await _handleRequest(
      emit,
      () => _userListRepository.updateUser(event.user),
      successMessage: "User updated",
    );
  }

  Future<void> _onArchiveUser(
    _ArchiveUser event,
    Emitter<ManageUserListState> emit,
  ) async {
    await _handleRequest(
      emit,
      () => _userListRepository.archiveUser(event.userId),
      successMessage: "User archived",
    );
  }

  Future<void> _onActivateLogin(
    _ActivateLogin event,
    Emitter<ManageUserListState> emit,
  ) async {
    await _handleRequest(
      emit,
      () => _userListRepository.activateLogin(event.userId),
      successMessage: "Login activated",
    );
  }

  Future<void> _onDisableLogin(
    _DisableLogin event,
    Emitter<ManageUserListState> emit,
  ) async {
    await _handleRequest(
      emit,
      () => _userListRepository.disableLogin(event.userId),
      successMessage: "Login disabled",
    );
  }

  Future<void> _onResetPassword(
    _ResetPassword event,
    Emitter<ManageUserListState> emit,
  ) async {
    await _handleRequest(
      emit,
      () => _userListRepository.resetPassword(event.userId),
      successMessage: "Password reset",
    );
  }

  Future<void> _onChangeUserType(
    _ChangeUserType event,
    Emitter<ManageUserListState> emit,
  ) async {
    await _handleRequest(
      emit,
      () => _userListRepository.changeUserType(event.userId, event.userType),
      successMessage: "User type changed successful",
    );
  }

  ///For admin roles
  Future<void> _onUpdateRoles(
    _UpdateRoles event,
    Emitter<ManageUserListState> emit,
  ) async {
    await _handleRequest(
      emit,
      () => _userListRepository.updateRoles(event.userId, event.roles),
      successMessage: "Roles updated",
    );
  }

  /// Generic function to handle repository calls
  Future<void> _handleRequest(
    Emitter<ManageUserListState> emit,
    FutureEither<void> Function() repoCall, {
    required String successMessage,
  }) async {
    emit(const ManageUserListState.loading());
    final result = await repoCall();
    result.fold(
      (failure) => emit(ManageUserListState.failure(failure)),
      (_) => emit(ManageUserListState.loaded(successMessage)),
    );
  }
}
