part of 'manage_user_list_bloc.dart';

@freezed
abstract class ManageUserListEvent with _$ManageUserListEvent {
  // Insert a new user
  const factory ManageUserListEvent.insertUser(UserModel user) = _InsertUser;

  // Update an existing user
  const factory ManageUserListEvent.updateUser(UserModel user) = _UpdateUser;

  // Archive user by ID
  const factory ManageUserListEvent.archiveUser(int userId) = _ArchiveUser;

  // Activate login for a user
  const factory ManageUserListEvent.activateLogin(int userId) = _ActivateLogin;

  // Disable login for a user
  const factory ManageUserListEvent.disableLogin(int userId) = _DisableLogin;

  // Reset password for a user
  const factory ManageUserListEvent.resetPassword(int userId) = _ResetPassword;

  // Change user type
  const factory ManageUserListEvent.changeUserType({
    required int userId,
    required String userType,
  }) = _ChangeUserType;

  // Change user type
  const factory ManageUserListEvent.acceptForRider({required int userId}) =
      _AcceptForRider;

  // Change user type
  const factory ManageUserListEvent.rejectForRider({required int userId}) =
      _RejectForRider;
  // Update roles for a user
  const factory ManageUserListEvent.updateRoles({
    required int userId,
    required List<AdminRoleUpdateModel> roles,
  }) = _UpdateRoles;
}
