// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_user_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ManageUserListEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ManageUserListEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageUserListEvent()';
  }
}

/// Adds pattern-matching-related methods to [ManageUserListEvent].
extension ManageUserListEventPatterns on ManageUserListEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InsertUser value)? insertUser,
    TResult Function(_UpdateUser value)? updateUser,
    TResult Function(_ArchiveUser value)? archiveUser,
    TResult Function(_ActivateLogin value)? activateLogin,
    TResult Function(_DisableLogin value)? disableLogin,
    TResult Function(_ResetPassword value)? resetPassword,
    TResult Function(_ChangeUserType value)? changeUserType,
    TResult Function(_AcceptForRider value)? acceptForRider,
    TResult Function(_RejectForRider value)? rejectForRider,
    TResult Function(_UpdateRoles value)? updateRoles,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InsertUser() when insertUser != null:
        return insertUser(_that);
      case _UpdateUser() when updateUser != null:
        return updateUser(_that);
      case _ArchiveUser() when archiveUser != null:
        return archiveUser(_that);
      case _ActivateLogin() when activateLogin != null:
        return activateLogin(_that);
      case _DisableLogin() when disableLogin != null:
        return disableLogin(_that);
      case _ResetPassword() when resetPassword != null:
        return resetPassword(_that);
      case _ChangeUserType() when changeUserType != null:
        return changeUserType(_that);
      case _AcceptForRider() when acceptForRider != null:
        return acceptForRider(_that);
      case _RejectForRider() when rejectForRider != null:
        return rejectForRider(_that);
      case _UpdateRoles() when updateRoles != null:
        return updateRoles(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InsertUser value) insertUser,
    required TResult Function(_UpdateUser value) updateUser,
    required TResult Function(_ArchiveUser value) archiveUser,
    required TResult Function(_ActivateLogin value) activateLogin,
    required TResult Function(_DisableLogin value) disableLogin,
    required TResult Function(_ResetPassword value) resetPassword,
    required TResult Function(_ChangeUserType value) changeUserType,
    required TResult Function(_AcceptForRider value) acceptForRider,
    required TResult Function(_RejectForRider value) rejectForRider,
    required TResult Function(_UpdateRoles value) updateRoles,
  }) {
    final _that = this;
    switch (_that) {
      case _InsertUser():
        return insertUser(_that);
      case _UpdateUser():
        return updateUser(_that);
      case _ArchiveUser():
        return archiveUser(_that);
      case _ActivateLogin():
        return activateLogin(_that);
      case _DisableLogin():
        return disableLogin(_that);
      case _ResetPassword():
        return resetPassword(_that);
      case _ChangeUserType():
        return changeUserType(_that);
      case _AcceptForRider():
        return acceptForRider(_that);
      case _RejectForRider():
        return rejectForRider(_that);
      case _UpdateRoles():
        return updateRoles(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InsertUser value)? insertUser,
    TResult? Function(_UpdateUser value)? updateUser,
    TResult? Function(_ArchiveUser value)? archiveUser,
    TResult? Function(_ActivateLogin value)? activateLogin,
    TResult? Function(_DisableLogin value)? disableLogin,
    TResult? Function(_ResetPassword value)? resetPassword,
    TResult? Function(_ChangeUserType value)? changeUserType,
    TResult? Function(_AcceptForRider value)? acceptForRider,
    TResult? Function(_RejectForRider value)? rejectForRider,
    TResult? Function(_UpdateRoles value)? updateRoles,
  }) {
    final _that = this;
    switch (_that) {
      case _InsertUser() when insertUser != null:
        return insertUser(_that);
      case _UpdateUser() when updateUser != null:
        return updateUser(_that);
      case _ArchiveUser() when archiveUser != null:
        return archiveUser(_that);
      case _ActivateLogin() when activateLogin != null:
        return activateLogin(_that);
      case _DisableLogin() when disableLogin != null:
        return disableLogin(_that);
      case _ResetPassword() when resetPassword != null:
        return resetPassword(_that);
      case _ChangeUserType() when changeUserType != null:
        return changeUserType(_that);
      case _AcceptForRider() when acceptForRider != null:
        return acceptForRider(_that);
      case _RejectForRider() when rejectForRider != null:
        return rejectForRider(_that);
      case _UpdateRoles() when updateRoles != null:
        return updateRoles(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(UserModel user)? insertUser,
    TResult Function(UserModel user)? updateUser,
    TResult Function(int userId)? archiveUser,
    TResult Function(int userId)? activateLogin,
    TResult Function(int userId)? disableLogin,
    TResult Function(int userId)? resetPassword,
    TResult Function(int userId, String userType)? changeUserType,
    TResult Function(int userId)? acceptForRider,
    TResult Function(int userId)? rejectForRider,
    TResult Function(int userId, List<AdminRoleUpdateModel> roles)? updateRoles,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InsertUser() when insertUser != null:
        return insertUser(_that.user);
      case _UpdateUser() when updateUser != null:
        return updateUser(_that.user);
      case _ArchiveUser() when archiveUser != null:
        return archiveUser(_that.userId);
      case _ActivateLogin() when activateLogin != null:
        return activateLogin(_that.userId);
      case _DisableLogin() when disableLogin != null:
        return disableLogin(_that.userId);
      case _ResetPassword() when resetPassword != null:
        return resetPassword(_that.userId);
      case _ChangeUserType() when changeUserType != null:
        return changeUserType(_that.userId, _that.userType);
      case _AcceptForRider() when acceptForRider != null:
        return acceptForRider(_that.userId);
      case _RejectForRider() when rejectForRider != null:
        return rejectForRider(_that.userId);
      case _UpdateRoles() when updateRoles != null:
        return updateRoles(_that.userId, _that.roles);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(UserModel user) insertUser,
    required TResult Function(UserModel user) updateUser,
    required TResult Function(int userId) archiveUser,
    required TResult Function(int userId) activateLogin,
    required TResult Function(int userId) disableLogin,
    required TResult Function(int userId) resetPassword,
    required TResult Function(int userId, String userType) changeUserType,
    required TResult Function(int userId) acceptForRider,
    required TResult Function(int userId) rejectForRider,
    required TResult Function(int userId, List<AdminRoleUpdateModel> roles)
    updateRoles,
  }) {
    final _that = this;
    switch (_that) {
      case _InsertUser():
        return insertUser(_that.user);
      case _UpdateUser():
        return updateUser(_that.user);
      case _ArchiveUser():
        return archiveUser(_that.userId);
      case _ActivateLogin():
        return activateLogin(_that.userId);
      case _DisableLogin():
        return disableLogin(_that.userId);
      case _ResetPassword():
        return resetPassword(_that.userId);
      case _ChangeUserType():
        return changeUserType(_that.userId, _that.userType);
      case _AcceptForRider():
        return acceptForRider(_that.userId);
      case _RejectForRider():
        return rejectForRider(_that.userId);
      case _UpdateRoles():
        return updateRoles(_that.userId, _that.roles);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(UserModel user)? insertUser,
    TResult? Function(UserModel user)? updateUser,
    TResult? Function(int userId)? archiveUser,
    TResult? Function(int userId)? activateLogin,
    TResult? Function(int userId)? disableLogin,
    TResult? Function(int userId)? resetPassword,
    TResult? Function(int userId, String userType)? changeUserType,
    TResult? Function(int userId)? acceptForRider,
    TResult? Function(int userId)? rejectForRider,
    TResult? Function(int userId, List<AdminRoleUpdateModel> roles)?
    updateRoles,
  }) {
    final _that = this;
    switch (_that) {
      case _InsertUser() when insertUser != null:
        return insertUser(_that.user);
      case _UpdateUser() when updateUser != null:
        return updateUser(_that.user);
      case _ArchiveUser() when archiveUser != null:
        return archiveUser(_that.userId);
      case _ActivateLogin() when activateLogin != null:
        return activateLogin(_that.userId);
      case _DisableLogin() when disableLogin != null:
        return disableLogin(_that.userId);
      case _ResetPassword() when resetPassword != null:
        return resetPassword(_that.userId);
      case _ChangeUserType() when changeUserType != null:
        return changeUserType(_that.userId, _that.userType);
      case _AcceptForRider() when acceptForRider != null:
        return acceptForRider(_that.userId);
      case _RejectForRider() when rejectForRider != null:
        return rejectForRider(_that.userId);
      case _UpdateRoles() when updateRoles != null:
        return updateRoles(_that.userId, _that.roles);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _InsertUser implements ManageUserListEvent {
  const _InsertUser(this.user);

  final UserModel user;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InsertUser &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  @override
  String toString() {
    return 'ManageUserListEvent.insertUser(user: $user)';
  }
}

/// @nodoc

class _UpdateUser implements ManageUserListEvent {
  const _UpdateUser(this.user);

  final UserModel user;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateUser &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  @override
  String toString() {
    return 'ManageUserListEvent.updateUser(user: $user)';
  }
}

/// @nodoc

class _ArchiveUser implements ManageUserListEvent {
  const _ArchiveUser(this.userId);

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ArchiveUser &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.archiveUser(userId: $userId)';
  }
}

/// @nodoc

class _ActivateLogin implements ManageUserListEvent {
  const _ActivateLogin(this.userId);

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ActivateLogin &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.activateLogin(userId: $userId)';
  }
}

/// @nodoc

class _DisableLogin implements ManageUserListEvent {
  const _DisableLogin(this.userId);

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DisableLogin &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.disableLogin(userId: $userId)';
  }
}

/// @nodoc

class _ResetPassword implements ManageUserListEvent {
  const _ResetPassword(this.userId);

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ResetPassword &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.resetPassword(userId: $userId)';
  }
}

/// @nodoc

class _ChangeUserType implements ManageUserListEvent {
  const _ChangeUserType({required this.userId, required this.userType});

  final int userId;
  final String userType;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ChangeUserType &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userType, userType) ||
                other.userType == userType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId, userType);

  @override
  String toString() {
    return 'ManageUserListEvent.changeUserType(userId: $userId, userType: $userType)';
  }
}

/// @nodoc

class _AcceptForRider implements ManageUserListEvent {
  const _AcceptForRider({required this.userId});

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AcceptForRider &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.acceptForRider(userId: $userId)';
  }
}

/// @nodoc

class _RejectForRider implements ManageUserListEvent {
  const _RejectForRider({required this.userId});

  final int userId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RejectForRider &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  @override
  String toString() {
    return 'ManageUserListEvent.rejectForRider(userId: $userId)';
  }
}

/// @nodoc

class _UpdateRoles implements ManageUserListEvent {
  const _UpdateRoles({
    required this.userId,
    required final List<AdminRoleUpdateModel> roles,
  }) : _roles = roles;

  final int userId;
  final List<AdminRoleUpdateModel> _roles;
  List<AdminRoleUpdateModel> get roles {
    if (_roles is EqualUnmodifiableListView) return _roles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_roles);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateRoles &&
            (identical(other.userId, userId) || other.userId == userId) &&
            const DeepCollectionEquality().equals(other._roles, _roles));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    const DeepCollectionEquality().hash(_roles),
  );

  @override
  String toString() {
    return 'ManageUserListEvent.updateRoles(userId: $userId, roles: $roles)';
  }
}
