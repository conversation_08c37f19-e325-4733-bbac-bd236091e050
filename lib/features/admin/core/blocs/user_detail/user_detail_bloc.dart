import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/user_model.dart';
import 'package:safari_yatri/features/admin/core/repositories/user_repository.dart';

part 'user_detail_event.dart';
part 'user_detail_state.dart';
part 'user_detail_bloc.freezed.dart';


class UserDetailBloc extends Bloc<UserDetailEvent, UserDetailState> {
  final UserRepository _userRepository;

  UserDetailBloc({required UserRepository repo})
    : _userRepository = repo,
      super(const UserDetailState.initial()) {
    on<_GetUserById>(_onGetUserById);
  }

  Future<void> _onGetUserById(
    _GetUserById event,
    Emitter<UserDetailState> emit,
  ) async {
    emit(const UserDetailState.loading());

    final result = await _userRepository.getUserById(event.userId);

    result.fold(
      (failure) => emit(UserDetailState.failure(failure)),
      (user) => emit(UserDetailState.loaded(user)),
    );
  }
}
