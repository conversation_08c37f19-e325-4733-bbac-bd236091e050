import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/models/ride_shift_model.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/ride_shift_form.dart';
import 'package:safari_yatri/features/admin/core/repositories/ride_shift_repository.dart';

part 'manage_ride_shift_event.dart';
part 'manage_ride_shift_state.dart';
part 'manage_ride_shift_bloc.freezed.dart';

class ManageRideShiftBloc
    extends Bloc<ManageRideShiftEvent, ManageRideShiftState> {
  final RideShiftRepository _repository;
  ManageRideShiftBloc({required RideShiftRepository repo})
    : _repository = repo,
      super(ManageRideShiftState.initial()) {
    on<_Insert>(_onInsertRideShift);
    on<_Update>(_onUpdateRideShift);
  }

  Future<void> _onInsertRideShift(
    _Insert event,
    Emitter<ManageRideShiftState> emit,
  ) async {
    emit(ManageRideShiftState.loading());
    final result = await _repository.insertRideShift(event.rideShift);
    emit(
      result.fold(
        (l) => ManageRideShiftState.failure(l),
        (r) => ManageRideShiftState.loaded("Ride shift added successful"),
      ),
    );
  }

  Future<void> _onUpdateRideShift(
    _Update event,
    Emitter<ManageRideShiftState> emit,
  ) async {
    emit(ManageRideShiftState.loading());
    final result = await _repository.updateRideShift(event.rideShift);
    emit(
      result.fold(
        (l) => ManageRideShiftState.failure(l),
        (r) => ManageRideShiftState.loaded("Ride shift added successful"),
      ),
    );
  }
}
