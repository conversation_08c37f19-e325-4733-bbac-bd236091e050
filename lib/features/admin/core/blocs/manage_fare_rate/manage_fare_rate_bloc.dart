import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/fare_rate_insert_request_model.dart';
import 'package:safari_yatri/features/admin/core/models/fare_rate_model.dart';
import 'package:safari_yatri/features/admin/core/repositories/ride_shift_repository.dart';

part 'manage_fare_rate_event.dart';
part 'manage_fare_rate_state.dart';
part 'manage_fare_rate_bloc.freezed.dart';

class ManageFareRateBloc
    extends Bloc<ManageFareRateEvent, ManageFareRateState> {
  final RideShiftRepository _repository;
  ManageFareRateBloc({required RideShiftRepository repo})
    : _repository = repo,
      super(ManageFareRateState.initial()) {
    on<_Insert>(_onInsert);
    on<_Update>(_onUpdate);
  }

  Future<void> _onInsert(
    _Insert event,
    Emitter<ManageFareRateState> emit,
  ) async {
    emit(ManageFareRateState.loading());

    final result = await _repository.insertFareRate(event.rate);
    result.fold((f) => emit(ManageFareRateState.failure(f)), (data) {
      emit(ManageFareRateState.loaded("Fare rate added successfully"));
    });
  }

  Future<void> _onUpdate(
    _Update event,
    Emitter<ManageFareRateState> emit,
  ) async {
    emit(ManageFareRateState.loading());
    final result = await _repository.updateFareRate(event.fareRate);
    result.fold((f) => emit(ManageFareRateState.failure(f)), (data) {
      emit(ManageFareRateState.loaded("Fare rate updated successfully"));
    });
  }
}
