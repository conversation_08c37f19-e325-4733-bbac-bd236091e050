import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_admin_role_list/get_admin_role_list_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_admin_user_role/get_admin_user_role_bloc.dart';
import 'package:safari_yatri/features/admin/core/models/role_model.dart';

class AdminRoleSelectorWidget extends StatefulWidget {
  final int userId;
  final void Function(List<AdminRoleUpdateModel>)? onChanged;
  final Future<bool> Function(List<AdminRoleUpdateModel>)? onUpdateRoles;

  const AdminRoleSelectorWidget({
    super.key,
    required this.userId,
    this.onChanged,
    this.onUpdateRoles,
  });

  @override
  State<AdminRoleSelectorWidget> createState() =>
      _AdminRoleSelectorWidgetState();
}

class _AdminRoleSelectorWidgetState extends State<AdminRoleSelectorWidget> {
  final Map<int, bool> _selectedRoles = {};
  final Map<int, bool> _originalRoles = {};
  bool _hasChanges = false;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    sl<GetAdminUserRoleBloc>().add(GetAdminUserRoleEvent.get(widget.userId));
    sl<GetAdminRoleListBloc>().add(GetAdminRoleListEvent.get());
  }

  void _handleRoleTap(int roleId) {
    setState(() {
      _selectedRoles[roleId] = !(_selectedRoles[roleId] ?? false);
      _hasChanges = _checkForChanges();
    });
    widget.onChanged?.call(
      _selectedRoles.entries
          .map((e) => AdminRoleUpdateModel(roleId: e.key, selected: e.value))
          .toList(),
    );
    setState(() {
      _hasChanges = _checkForChanges();
    });
  }

  bool _checkForChanges() {
    for (final entry in _selectedRoles.entries) {
      if ((_originalRoles[entry.key] ?? false) != entry.value) {
        return true;
      }
    }
    return false;
  }

  Future<void> _handleUpdateRoles() async {
    if (!_hasChanges || _isUpdating) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final rolesToUpdate =
          _selectedRoles.entries
              .map(
                (e) => AdminRoleUpdateModel(roleId: e.key, selected: e.value),
              )
              .toList();

      bool success = false;
      if (widget.onUpdateRoles != null) {
        success = await widget.onUpdateRoles!(rolesToUpdate);
      }

      if (success) {
        // Update original roles to match current selection
        _originalRoles.clear();
        _originalRoles.addAll(_selectedRoles);

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 8),
                  Text('User roles updated successfully'),
                ],
              ),
              backgroundColor: Theme.of(context).colorScheme.primary,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }

        // Refresh the user roles from server
        sl<GetAdminUserRoleBloc>().add(
          GetAdminUserRoleEvent.get(widget.userId),
        );
      } else {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.error, color: Colors.white),
                  SizedBox(width: 8),
                  Text('Failed to update user roles'),
                ],
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('Error: ${e.toString()}')),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
          _hasChanges = _checkForChanges();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return BlocBuilder<GetAdminUserRoleBloc, GetAdminUserRoleState>(
      builder: (context, state) {
        state.maybeWhen(
          loaded: (data) {
            // Store original roles for comparison
            _originalRoles.clear();
            _selectedRoles.clear();
            for (final role in data) {
              _originalRoles[role.roleId] = true;
              _selectedRoles[role.roleId] = true;
            }
            _hasChanges = false;
          },
          orElse: () {},
        );

        return BlocBuilder<GetAdminRoleListBloc, GetAdminRoleListState>(
          builder: (context, state) {
            return state.maybeWhen(
              failure: (f) => _buildErrorState(f.message, colorScheme),
              loaded: (data) => _buildLoadedState(data, colorScheme),
              orElse: () => _buildLoadingState(colorScheme),
            );
          },
        );
      },
    );
  }

  Widget _buildErrorState(String message, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.error.withAlpha(100), width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.error_outline, color: colorScheme.error, size: 48),
          const SizedBox(height: 12),
          Text(
            'Error Loading Roles',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: colorScheme.onErrorContainer,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: colorScheme.onErrorContainer.withAlpha(180),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withAlpha(100), width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(color: colorScheme.primary),
          const SizedBox(height: 16),
          Text(
            'Loading roles...',
            style: TextStyle(
              fontSize: 16,
              color: colorScheme.onSurface.withAlpha(180),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(List<dynamic> data, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withAlpha(100), width: 1),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withAlpha(25),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(colorScheme),
          const SizedBox(height: 20),
          _buildRoleGrid(data, colorScheme),
          const SizedBox(height: 24),
          _buildUpdateButton(colorScheme),
        ],
      ),
    );
  }

  Widget _buildHeader(ColorScheme colorScheme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.admin_panel_settings,
            color: colorScheme.onPrimaryContainer,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Admin Role Management',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: colorScheme.onSurface,
              ),
            ),
            Text(
              'Select roles for this user',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withAlpha(180),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRoleGrid(List<dynamic> data, ColorScheme colorScheme) {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children:
          data.map((role) {
            final isSelected = _selectedRoles[role.roleId] ?? false;
            return _buildRoleChip(role, isSelected, colorScheme);
          }).toList(),
    );
  }

  Widget _buildRoleChip(
    dynamic role,
    bool isSelected,
    ColorScheme colorScheme,
  ) {
    return Material(
      elevation: isSelected ? 4 : 1,
      borderRadius: BorderRadius.circular(12),
      shadowColor: colorScheme.shadow.withAlpha(50),
      child: InkWell(
        onTap: () => _handleRoleTap(role.roleId),
        borderRadius: BorderRadius.circular(12),
        splashColor: colorScheme.primary.withAlpha(50),
        highlightColor: colorScheme.primary.withAlpha(25),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            gradient:
                isSelected
                    ? LinearGradient(
                      colors: [
                        colorScheme.primaryContainer,
                        colorScheme.primaryContainer.withAlpha(200),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                    : null,
            color: isSelected ? null : colorScheme.surfaceContainerHigh,
            border: Border.all(
              color:
                  isSelected
                      ? colorScheme.primary
                      : colorScheme.outline.withAlpha(100),
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: isSelected ? colorScheme.primary : colorScheme.surface,
                  border: Border.all(
                    color:
                        isSelected ? colorScheme.primary : colorScheme.outline,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                child:
                    isSelected
                        ? Icon(
                          Icons.check,
                          size: 14,
                          color: colorScheme.onPrimary,
                        )
                        : null,
              ),
              const SizedBox(width: 10),
              Text(
                role.roleName,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  color:
                      isSelected
                          ? colorScheme.onPrimaryContainer
                          : colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUpdateButton(ColorScheme colorScheme) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: (_hasChanges && !_isUpdating) ? _handleUpdateRoles : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          disabledBackgroundColor: colorScheme.surfaceContainerHighest,
          disabledForegroundColor: colorScheme.onSurface.withAlpha(100),
          elevation: _hasChanges ? 3 : 0,
          shadowColor: colorScheme.shadow.withAlpha(100),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child:
            _isUpdating
                ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          colorScheme.onPrimary,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Updating...',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                )
                : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _hasChanges ? Icons.update : Icons.check_circle_outline,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _hasChanges ? 'Update User Roles' : 'No Changes Made',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
      ),
    );
  }
}
