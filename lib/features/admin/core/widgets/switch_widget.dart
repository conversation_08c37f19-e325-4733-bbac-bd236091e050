import 'package:flutter/material.dart';

class OptionalSwitch extends StatelessWidget {
  final bool? value;
  final void Function(bool)? onChanged;
  final Color? activeColor;
  final Color? inactiveThumbColor;
  final Color? inactiveTrackColor;
  final WidgetStateProperty<Color?>? thumbColor;
  final WidgetStateProperty<Color?>? trackColor;
  final FocusNode? focusNode;
  final bool autofocus;

  const OptionalSwitch({
    super.key,
    this.value,
    this.onChanged,
    this.activeColor,
    this.inactiveThumbColor,
    this.inactiveTrackColor,
    this.thumbColor,
    this.trackColor,
    this.focusNode,
    this.autofocus = false,
  });

  @override
  Widget build(BuildContext context) {
    return Switch(
      value: value ?? false,
      onChanged: onChanged,
      activeColor: activeColor,
      inactiveThumbColor: inactiveThumbColor,
      inactiveTrackColor: inactiveTrackColor,
      thumbColor: thumbColor,
      trackColor: trackColor,
      focusNode: focusNode,
      autofocus: autofocus,
    );
  }
}
