import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_user_list/manage_user_list_bloc.dart';

class ManageUserBlocListener extends StatelessWidget {
  final Widget child;
  final VoidCallback? onSuccess;
  final ManageUserListBloc cudUserListBloc;
  const ManageUserBlocListener({
    super.key,
    required this.child,
    required this.cudUserListBloc,
    this.onSuccess,
  });

  @override
  Widget build(BuildContext context) {
    return BlocListener<ManageUserListBloc, ManageUserListState>(
      bloc: cudUserListBloc,
      listener: (context, state) {
        state.whenOrNull(
          loading: () {
            AppLoadingDialog.show(context);
          },
          loaded: (message) {
            CustomToast.showSuccess(message);
            AppLoadingDialog.hide(context);
            onSuccess?.call();
          },
          failure: (failure) {
            CustomToast.showError(failure.message);
            AppLoadingDialog.hide(context);
          },
        );
      },
      child: child,
    );
  }
}
