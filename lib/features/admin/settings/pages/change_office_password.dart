
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';

import '../../../../common/widgets/custom_toast.dart';
import '../../../../core/utils/app_loading_dialogs.dart';
import '../../core/blocs/change_email_password/change_email_password_bloc.dart';
import '../../core/models/change_email_password_form.dart';
class ChangePasswordDialog extends StatefulWidget {
  const ChangePasswordDialog({super.key});

  @override
  State<ChangePasswordDialog> createState() => _ChangePasswordDialogState();
}

class _ChangePasswordDialogState extends State<ChangePasswordDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _oldPasswordController;
  late final TextEditingController _passwordController;
  late final TextEditingController _confirmPasswordController;

  @override
  void initState() {
    super.initState();
    _oldPasswordController = TextEditingController();
    _passwordController = TextEditingController();
    _confirmPasswordController = TextEditingController();
  }

  @override
  void dispose() {
    _oldPasswordController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _onChangeEmailPassword() {
    if (_formKey.currentState!.validate()) {
      final form = ChangeEmailPasswordForm(
        oldPassword: _oldPasswordController.text,
        password: _passwordController.text,
        confirmPassword: _confirmPasswordController.text,
      );
      sl<ChangeEmailPasswordBloc>().add(ChangeEmailPasswordEvent.update(form));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ChangeEmailPasswordBloc, ChangeEmailPasswordState>(
      bloc: sl<ChangeEmailPasswordBloc>(),
      listener: (context, state) {
        state.whenOrNull(
          loading: () => AppLoadingDialog.show(context),
          loaded: (message) {
            AppLoadingDialog.hide(context);
            CustomToast.showSuccess(message);
            Navigator.of(context).pop();
          },
          failure: (failure) {
            AppLoadingDialog.hide(context);
            CustomToast.showError(failure.message);
          },
        );
      },
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            16,
          ), // Rounded corners for the dialog
        ),
        title: Text(
          'Change Office Email Password',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        content: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              TextFormField(
                controller: _oldPasswordController,
                decoration: InputDecoration(
                  labelText: 'Old Password',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  prefixIcon: const Icon(Icons.vpn_key), // Added icon
                ),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a new password';
                  }
                  if (value.length < 6) {
                    return 'Password must be at least 6 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(
                  labelText: 'New Password',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      10,
                    ), // Rounded text field borders
                  ),
                  prefixIcon: const Icon(Icons.vpn_key), // Added icon
                ),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a new password';
                  }
                  if (value.length < 6) {
                    return 'Password must be at least 6 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _confirmPasswordController,
                decoration: InputDecoration(
                  labelText: 'Confirm New Password',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      10,
                    ), // Rounded text field borders
                  ),
                  prefixIcon: const Icon(Icons.vpn_key_outlined), // Added icon
                ),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please confirm your new password';
                  }
                  if (value != _passwordController.text) {
                    return 'Passwords do not match';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: const Text('Cancel'),
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.lock_reset),
            label: const Text('Update Password'),
            onPressed: _onChangeEmailPassword,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              elevation: 3,
            ),
          ),
        ],
      ),
    );
  }
}
