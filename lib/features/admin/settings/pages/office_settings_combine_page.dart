import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_office/get_office_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/update_office_setting/update_office_setting_bloc.dart';
import 'package:safari_yatri/features/admin/core/models/office_model.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

import '../widgets/menu_list_tile.dart';

class OfficeSettingsCombinePage extends StatefulWidget {
  const OfficeSettingsCombinePage({super.key});

  @override
  State<OfficeSettingsCombinePage> createState() =>
      _OfficeSettingsCombinePageState();
}

class _OfficeSettingsCombinePageState extends State<OfficeSettingsCombinePage> {
  void _onPressEditSetting(OfficeModel model) {
    context.pushNamed(AppRoutesName.adminOfficeSettingsEdit, extra: model);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GetOfficeBloc, GetOfficeState>(
      builder: (context, state) {
        return state.maybeWhen(
          loaded: (office) {
            return ViewOfficeSettingsContent(
              settings: office,
              onEditPressed: () => _onPressEditSetting(office),
            );
          },
          failure:
              (failure) => ErrorWidgetWithRetry(
                failure: failure,
                onRetry: () {
                  sl<GetOfficeBloc>().add(const GetOfficeEvent.get());
                },
              ),
          orElse: () => const Center(child: CircularProgressIndicator()),
        );
      },
    );
  }
}

class ViewOfficeSettingsContent extends StatelessWidget {
  final OfficeModel settings;
  final VoidCallback onEditPressed;

  const ViewOfficeSettingsContent({
    super.key,
    required this.settings,
    required this.onEditPressed,
  });

  @override
  Widget build(BuildContext context) {
    // // Determine if the screen is wide enough for a two-column layout
    // final bool isLargeScreen = MediaQuery.of(context).size.width > 800;

    return Scaffold(
      // appBar: AppBar(
      //   title: const Text('Office Settings'),
      //   centerTitle: true,
      //   backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      //   elevation: 0,
      //   shape: const RoundedRectangleBorder(
      //     borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
      //   ),
      // ),
      body: Container(
        // Add a subtle background color to the body
        color: Theme.of(
          context,
        ).colorScheme.surfaceContainerHighest.withAlpha(60),
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: RefreshIndicator(
            onRefresh: () async {
              sl<GetOfficeBloc>().add(const GetOfficeEvent.get());
            },
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // buildProfileOption('General Settings', LucideIcons.settings),
                  buildProfileOption('Account', LucideIcons.user),
                  buildProfileOption(
                    'Notifications',
                    LucideIcons.bell,
                    onTap: () {
                      context.pushNamed(AppRoutesName.notification);
                    },
                  ),
                  buildProfileOption(
                    'Office Management',
                    LucideIcons.building2,
                    onTap: () {
                      context.pushNamed(AppRoutesName.officeManagement);
                    },
                  ),
                  buildProfileOption(
                    'Ride Shift Management',
                    LucideIcons.fileText,
                    onTap: () {
                      context.pushNamed(AppRoutesName.rideShiftManagement);
                    },
                  ),
                  buildProfileOption('Security', LucideIcons.shieldEllipsis),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class EditOfficeSettingsContent extends StatefulWidget {
  final OfficeModel settings;

  const EditOfficeSettingsContent({super.key, required this.settings});

  @override
  State<EditOfficeSettingsContent> createState() =>
      _EditOfficeSettingsContentState();
}

class _EditOfficeSettingsContentState extends State<EditOfficeSettingsContent> {
  late final TextEditingController _officeNameController;
  late final TextEditingController _officeShortNameController;
  late final TextEditingController _officeAddressController;
  late final TextEditingController _officePhoneController;
  late final TextEditingController _officeEmailController;
  late final TextEditingController _otpLifeController;
  late final TextEditingController _fiscalYearController;
  late final TextEditingController _riderRenewalChargeController;
  late final TextEditingController _riderRenewalFrequencyController;
  late final TextEditingController _riderPointRateController;
  late final TextEditingController _passengerPointRateController;
  late final TextEditingController _otpLimitPerDayController;
  late final TextEditingController _passengerCartLifeInMinuteController;
  late bool _autoLoginAfterOtp;

  @override
  void initState() {
    super.initState();
    _otpLimitPerDayController = TextEditingController(
      text: widget.settings.otpLimitPerDay.toString(),
    );

    _officeNameController = TextEditingController(
      text: widget.settings.officeName,
    );
    _officeShortNameController = TextEditingController(
      text: widget.settings.officeShortName,
    );
    _officeAddressController = TextEditingController(
      text: widget.settings.officeAddress,
    );
    _officePhoneController = TextEditingController(
      text: widget.settings.officePhone,
    );
    _officeEmailController = TextEditingController(
      text: widget.settings.officeEmail,
    );
    _otpLifeController = TextEditingController(
      text: widget.settings.otpLifeInSecond.toString(),
    );
    _fiscalYearController = TextEditingController(
      text: widget.settings.fiscalYear,
    );
    _riderRenewalChargeController = TextEditingController(
      text: widget.settings.riderRenewalCharge.toString(),
    );
    _riderRenewalFrequencyController = TextEditingController(
      text: widget.settings.riderRenewalFrequencyInDays.toString(),
    );
    _riderPointRateController = TextEditingController(
      text: widget.settings.riderPointRateInPercent.toString(),
    );
    _passengerPointRateController = TextEditingController(
      text: widget.settings.passengerPointRateInPercent.toString(),
    );

    _passengerCartLifeInMinuteController = TextEditingController(
      text: widget.settings.passengerCartLifeInMinute.toString(),
    );
    _autoLoginAfterOtp = widget.settings.autoLoginAfterOtpVerification;
  }

  @override
  /*************  ✨ Windsurf Command ⭐  *************/
  /// Disposes all the [TextEditingController]s used in this page.
  ///
  /// This is called when the widget is removed from the tree.
  /// *****  47a557db-5ba1-437f-83c6-d953ec9df7a6  ******
  void dispose() {
    _officeNameController.dispose();
    _officeShortNameController.dispose();
    _officeAddressController.dispose();
    _officePhoneController.dispose();
    _officeEmailController.dispose();
    _otpLifeController.dispose();
    _fiscalYearController.dispose();
    _riderRenewalChargeController.dispose();
    _riderRenewalFrequencyController.dispose();
    _riderPointRateController.dispose();
    _otpLimitPerDayController.dispose();
    _passengerPointRateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Office Settings'),
        // centerTitle: true,
        // backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        // elevation: 0,
        // shape: const RoundedRectangleBorder(
        //   borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
        // ),
      ),
      body: Container(
        color: Theme.of(
          context,
        ).colorScheme.surfaceContainerHighest.withOpacity(0.2),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Office Information Card
                _buildSettingsCard(
                  context,
                  title: 'Office Information',
                  children: [
                    _buildTextField(
                      'Office Name',
                      _officeNameController,
                      icon: Icons.business,
                    ),
                    _buildTextField(
                      'Short Name',
                      _officeShortNameController,
                      icon: Icons.short_text,
                    ),
                    _buildTextField(
                      'Address',
                      _officeAddressController,
                      icon: Icons.location_on,
                    ),
                    _buildTextField(
                      'Phone',
                      _officePhoneController,
                      icon: Icons.phone,
                      isNumeric: true,
                    ),
                    _buildTextField(
                      'Email',
                      _officeEmailController,
                      icon: Icons.email,
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // System Settings Card
                _buildSettingsCard(
                  context,
                  title: 'System Settings',
                  children: [
                    _buildTextField(
                      'OTP Limit Per Day',
                      _otpLimitPerDayController,
                      isNumeric: true,
                      icon: Icons.numbers,
                    ),
                    _buildTextField(
                      'OTP Life (seconds)',
                      _otpLifeController,
                      isNumeric: true,
                      icon: Icons.timer,
                    ),

                    _buildTextField(
                      'Passenger Cart Life (minute)',
                      _passengerCartLifeInMinuteController,
                      isNumeric: true,
                      icon: Icons.timer,
                    ),
                    _buildTextField(
                      'Fiscal Year',
                      _fiscalYearController,
                      icon: Icons.calendar_today,
                    ),
                    SwitchListTile(
                      title: Text(
                        'Auto Login After OTP',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      value: _autoLoginAfterOtp,
                      onChanged: (value) {
                        setState(() {
                          _autoLoginAfterOtp = value;
                        });
                      },
                      activeColor:
                          Theme.of(
                            context,
                          ).colorScheme.primary, // Styled switch
                      contentPadding: EdgeInsets.zero, // Remove default padding
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Rider Settings Card
                _buildSettingsCard(
                  context,
                  title: 'Rider Settings',
                  children: [
                    _buildTextField(
                      'Renewal Charge',
                      _riderRenewalChargeController,
                      isNumeric: true,
                      prefix: 'Rs ',
                      icon: Icons.attach_money,
                    ),
                    _buildTextField(
                      'Renewal Frequency (days)',
                      _riderRenewalFrequencyController,
                      isNumeric: true,
                      icon: Icons.calendar_month,
                    ),
                    _buildTextField(
                      'Rider Point Rate (%)',
                      _riderPointRateController,
                      isNumeric: true,
                      icon: Icons.star,
                    ),
                    _buildTextField(
                      'Passenger Point Rate (%)',
                      _passengerPointRateController,
                      isNumeric: true,
                      icon: Icons.person,
                    ),
                    const SizedBox(height: 30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton.icon(
                          onPressed: () => context.pop(),
                          icon: const Icon(Icons.cancel_outlined),
                          label: const Text('Cancel'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.error,
                            foregroundColor:
                                Theme.of(context).colorScheme.onError,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 12,
                            ),
                            elevation: 4,
                          ),
                        ),
                        const SizedBox(width: 20),
                        BlocListener<
                          UpdateOfficeSettingBloc,
                          UpdateOfficeSettingState
                        >(
                          listener: (context, state) {
                            state.whenOrNull(
                              loading: () => AppLoadingDialog.show(context),
                              loaded: (message) {
                                AppLoadingDialog.hide(context);
                                CustomToast.showSuccess(message);
                                sl<GetOfficeBloc>().add(
                                  const GetOfficeEvent.get(),
                                );
                                context.pop();
                              },
                              failure: (failure) {
                                AppLoadingDialog.hide(context);
                                CustomToast.showError(failure.message);
                              },
                            );
                          },
                          child: ElevatedButton.icon(
                            onPressed: _onUpdateOfficeSettings,
                            icon: const Icon(Icons.save),
                            label: const Text('Save Settings'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              foregroundColor:
                                  Theme.of(context).colorScheme.onPrimary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 12,
                              ),
                              elevation: 4,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build a consistent card style (reused from ViewOfficeSettingsContent)
  Widget _buildSettingsCard(
    BuildContext context, {
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const Divider(height: 24, thickness: 1),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
    String label,
    TextEditingController controller, {
    bool isNumeric = false,
    String? prefix,
    IconData? icon, // Added icon parameter
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(
              10,
            ), // Rounded text field borders
          ),
          prefixText: prefix,
          prefixIcon:
              icon != null ? Icon(icon) : null, // Display icon if provided
          contentPadding: const EdgeInsets.symmetric(
            vertical: 14,
            horizontal: 12,
          ), // Adjusted padding
        ),
        keyboardType: isNumeric ? TextInputType.number : TextInputType.text,
      ),
    );
  }

  void _onUpdateOfficeSettings() {
    final updatedOffice = OfficeModel(
      passengerCartLifeInMinute:
          int.tryParse(_passengerCartLifeInMinuteController.text) ?? 0,
      otpLimitPerDay: int.tryParse(_otpLimitPerDayController.text) ?? 0,
      officeName: _officeNameController.text,
      officeShortName: _officeShortNameController.text,
      officeAddress: _officeAddressController.text,
      officePhone: _officePhoneController.text,
      officeEmail: _officeEmailController.text,
      otpLifeInSecond:
          int.tryParse(_otpLifeController.text) ?? 0, // Use tryParse for safety
      fiscalYear: _fiscalYearController.text,
      autoLoginAfterOtpVerification: _autoLoginAfterOtp,
      riderRenewalCharge:
          double.tryParse(_riderRenewalChargeController.text) ?? 0.0,
      riderRenewalFrequencyInDays:
          int.tryParse(_riderRenewalFrequencyController.text) ?? 0,
      riderPointRateInPercent:
          double.tryParse(_riderPointRateController.text) ?? 0.0,
      passengerPointRateInPercent:
          double.tryParse(_passengerPointRateController.text) ?? 0.0,
    );
    sl<UpdateOfficeSettingBloc>().add(
      UpdateOfficeSettingEvent.update(updatedOffice),
    );
  }
}
