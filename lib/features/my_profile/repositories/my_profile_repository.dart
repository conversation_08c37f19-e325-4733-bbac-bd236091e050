import 'package:dartz/dartz.dart';
import 'package:image_picker/image_picker.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/network/api_service.dart';
import 'package:safari_yatri/core/services/app_cached_keys.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';
import 'package:safari_yatri/core/utils/image_utils.dart';
import 'package:safari_yatri/features/my_profile/model/update_user_data_model.dart';
import 'package:safari_yatri/features/role/services/remote_role_service.dart';
import '../model/my_profile_model.dart';

abstract interface class MyProfileRepository {
  //get user profile details
  FutureEither<GetUserProfileDetailsModel> getProfileDetails();

  FutureEither<String> updateProfileDetails({
    required UpdateUserDataModel updateUserData,
  });

  FutureEither<String> terminateUserAccount();

  FutureEither<String> uploadProfieImage(XFile fileData);

  FutureEither<String> setHomeLocation({
    required double lat,
    required double lng,
  });
}

class MyProfileRepositoryImpl implements MyProfileRepository {
  final ApiService _apiService;
  final CoreLocalDataSource _coreLocalDataSource = CoreLocalDataSource();

  MyProfileRepositoryImpl({required ApiService apiService})
    : _apiService = apiService;

  //##--------------GET: GET DETAILS DATA----------------##
  @override
  FutureEither<GetUserProfileDetailsModel> getProfileDetails() async {
    final response = await _apiService.get('MyProfile/GetDetails');
    return await response.fold((failure) => Left(failure), (data) async {
      final myProfileData = GetUserProfileDetailsModel.fromMap(data);
      await _coreLocalDataSource.saveModel(
        AppCachedKeys.userProfileData,
        myProfileData.toMap(),
      );

      await RemoteRoleService.instance.setRiderProfileRole(
        myProfileData.userType,
      );
      return Right(myProfileData);
    });
  }

  //##--------------PUT: UPDATE USER PROFILE DATA----------------##
  @override
  FutureEither<String> updateProfileDetails({
    required UpdateUserDataModel updateUserData,
  }) async {
    final response = await _apiService.put<String>(
      'MyProfile/Update',
      data: {
        "UserName": updateUserData.userName,
        "Gender": updateUserData.gender,
        "UserAddress": updateUserData.userAddress,
        "EmailAddress": updateUserData.emailAddress,
      },
    );

    return response.fold(
      (failure) => Left(failure),
      (data) => Right("User Data Updated Successfully!"),
    );
  }

  //##--------------DELETE: TERMINATE USER ACCOUNT----------------##
  @override
  FutureEither<String> terminateUserAccount() async {
    final profile = _coreLocalDataSource.getModel(
      AppCachedKeys.userProfileData,
      (json) => GetUserProfileDetailsModel.fromMap(json),
    );
    if (profile == null) {
      return Left(
        UnexpectedFailure(message: 'Please Login. Profile not found!'),
      );
    }

    final response = await _apiService.delete<String>(
      'MyProfile/TerminateAccount',
    );
    return response.fold((failure) => Left(failure), (data) => Right(data));
  }

  //##--------------PUT: UPLOAD USER PROFILE IMAGE----------------##
  @override
  FutureEither<String> uploadProfieImage(XFile fileData) async {
    final response = await _apiService.put<String>(
      'MyProfile/UploadProfilePicture',
      data: {
        "FileData": await ImageUtility.getCompressedImageDataBase64WithMimeType(
          fileData,
        ),
        "ClientFileName": fileData.path.split("/").last,
      },
    );

    return response.fold((failure) => left(failure), (data) => Right(data));
  }

  //##--------------PUT: SET USER HOME LOCATION----------------##
  @override
  FutureEither<String> setHomeLocation({
    required double lat,
    required double lng,
  }) async {
    final response = await _apiService.put<String>(
      'MyProfile/SetMyHomeLocation',
      queryParameters: {'lat': lat, 'lng': lng},
    );

    return response.fold(
      (failure) => Left(failure),
      (data) => Right("Home location set successfully."),
    );
  }
}
