import 'dart:convert';

// ignore_for_file: public_member_api_docs, sort_constructors_first

class UpdateUserDataModel {
  final String userName;
  final String gender;
  final String emailAddress;
  final String userAddress;
  UpdateUserDataModel({
    required this.userName,
    required this.gender,
    required this.userAddress,
    required this.emailAddress,
  });

  Map<String, dynamic> toMap() {
    return {
      'UserName': userName,
      'Gender': gender,
      'EmailAddress': emailAddress,
      'UserAddress': userAddress,
    };
  }

  factory UpdateUserDataModel.fromMap(Map<String, dynamic> map) {
    return UpdateUserDataModel(
      userName: map['UserName'] as String,
      gender: map['Gender'] as String,
      emailAddress: map['EmailAddress'] as String,
      userAddress: map['UserAddress'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory UpdateUserDataModel.fromJson(String source) =>
      UpdateUserDataModel.fromMap(json.decode(source) as Map<String, dynamic>);
}
