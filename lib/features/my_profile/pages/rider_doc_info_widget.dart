import 'package:flutter/material.dart';

import '../../drawer/widget/document_status_card.dart';

class DocumentInfo extends StatelessWidget {
  const DocumentInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: Text(
            "Document Info",
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
        ),

        DocumentStatusCard(
          title: "Driver's License",
          expiryDate: '2026-12-15',
          status: DocumentStatus.verified,
        ),
        <PERSON><PERSON><PERSON><PERSON>(height: 12),
        DocumentStatusCard(
          title: 'Vehicle Registration',
          expiryDate: '2025-08-20',
          status: DocumentStatus.verified,
        ),
        Si<PERSON><PERSON><PERSON>(height: 12),
        DocumentStatusCard(
          title: 'Insurance Certificate',
          expiryDate: '2025-03-10',
          status: DocumentStatus.pending,
        ),
        Si<PERSON><PERSON><PERSON>(height: 12),
        DocumentStatusCard(
          title: 'Route Permit',
          expiryDate: '2025-12-31',
          status: DocumentStatus.verified,
        ),
      ],
    );
  }
}
