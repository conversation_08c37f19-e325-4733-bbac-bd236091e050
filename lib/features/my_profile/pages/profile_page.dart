import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/image_utils.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_form_field.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/update_my_profile/update_my_profile_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/upload_profile_image/upload_profile_image_bloc.dart';
import 'package:safari_yatri/features/my_profile/model/update_user_data_model.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../core/widget/custom_appbar.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  bool isEditing = false;
  XFile? profileImage;

  // Text controllers
  TextEditingController userNameController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController emailAddressController = TextEditingController();
  TextEditingController genderController = TextEditingController();
  TextEditingController userAddressController = TextEditingController();

  @override
  void dispose() {
    userNameController.dispose();
    phoneNumberController.dispose();
    emailAddressController.dispose();
    genderController.dispose();
    userAddressController.dispose();
    super.dispose();
  }

  bool forceFetchCalled = false;

  void forceFetchProfile() {
    if (forceFetchCalled) return;
    forceFetchCalled = true;
    sl<MyProfileBloc>().add(MyProfileEvent.get(true));
  }

  void _updateProfile() {
    UpdateUserDataModel userDataModel = UpdateUserDataModel(
      userName: userNameController.text,
      userAddress: userAddressController.text,
      emailAddress: emailAddressController.text,
      gender: genderController.text,
    );

    sl<UpdateMyProfileBloc>().add(
      UpdateMyProfileEvent.updateMyProfile(userDataModel),
    );
  }

  String _formatDateTime(String date, String time) {
    try {
      DateTime dateTime = DateTime.parse('$date $time');
      return DateFormat('MMM dd, yyyy - hh:mm a').format(dateTime);
    } catch (e) {
      return '$date $time';
    }
  }

  String _formatCurrency(double amount) {
    return NumberFormat.currency(
      symbol: 'Rs. ',
      decimalDigits: 2,
    ).format(amount);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: Text('My Profile'),
        actions: [
          Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              color: T.c(context).primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () {
                setState(() {
                  isEditing = !isEditing;
                });
              },
              icon: Icon(
                isEditing ? LucideIcons.x : LucideIcons.pencil,
                color: T.c(context).primary,
                size: 16,
              ),
            ),
          ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<UploadProfileImageBloc, UploadProfileImageState>(
            listener: (context, state) {
              state.whenOrNull(
                loading: () {
                  CustomToast.showInfo("Uploading image...");
                },
                failure: (failure) {
                  CustomToast.showError(failure.message);
                },
                loaded: (data) {
                  CustomToast.showSuccess('Profile picture updated');
                  forceFetchProfile();
                  setState(() {
                    profileImage = null;
                  });
                  context.pop();
                },
              );
            },
          ),
          BlocListener<UpdateMyProfileBloc, UpdateMyProfileState>(
            listener: (context, state) {
              state.whenOrNull(
                loading: () => AppLoadingDialog.show(context),
                failure: (failure) {
                  AppLoadingDialog.hide(context);
                  CustomToast.showError(failure.message);
                },
                loaded: (data) {
                  AppLoadingDialog.hide(context);
                  CustomToast.showSuccess('Profile updated successfully');
                  forceFetchProfile();
                  setState(() {
                    isEditing = false;
                  });
                  context.pop();
                },
              );
            },
          ),
        ],
        child: BlocBuilder<MyProfileBloc, MyProfileState>(
          builder: (context, state) {
            return state.when(
              initial: () => const SizedBox(),
              loading:
                  () => Center(
                    child: CircularProgressIndicator(
                      color: T.c(context).primary,
                    ),
                  ),
              loaded: (data) {
                // Populate controllers with data
                userNameController.text = data.userName;
                phoneNumberController.text = data.phoneNo;
                emailAddressController.text = data.emailAddress ?? "";
                userAddressController.text = data.address ?? 'No Address';
                genderController.text = data.gender;

                return RefreshIndicator(
                  color: T.c(context).primary,
                  onRefresh: () async {
                    sl<MyProfileBloc>().add(MyProfileEvent.get(true));
                  },
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppStyles.space12,
                    ),
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      spacing: AppStyles.space16,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: AppStyles.space16),

                        // Profile Picture Section
                        _buildProfilePictureSection(data),

                        // Account Status Section
                        _buildAccountStatusSection(data),

                        // Personal Information Section
                        _buildPersonalInfoSection(data),

                        // Location Section
                        _buildLocationSection(data),

                        // Account Details Section
                        // _buildAccountDetailsSection(data),

                        // Wallet Section
                        _buildWalletSection(data),

                        const SizedBox(height: AppStyles.space24),
                      ],
                    ),
                  ),
                );
              },
              failure: (failure) {
                return ErrorWidgetWithRetry(
                  failure: failure,
                  onRetry:
                      () => sl<MyProfileBloc>().add(MyProfileEvent.get(true)),
                );
              },
            );
          },
        ),
      ),
      bottomNavigationBar:
          isEditing
              ? Padding(
                padding: const EdgeInsets.fromLTRB(
                  AppStyles.space12,
                  0,
                  AppStyles.space12,
                  AppStyles.space16,
                ),
                child: CustomButtonPrimary(
                  title: 'Save Changes',
                  onPressed: _updateProfile,
                ),
              )
              : null,
    );
  }

  Widget _buildProfilePictureSection(data) {
    return Center(
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          CircleAvatar(
            radius: 48,
            backgroundColor: T.c(context).surfaceContainerHigh,
            child: ClipOval(
              child: SizedBox(
                width: 96,
                height: 96,
                child:
                    profileImage != null
                        ? ImageUtility.displayImageFromXFile(profileImage)
                        : ImageUtility.displayImageFromBase64(
                          data.profilePicture,
                        ),
              ),
            ),
          ),
          Positioned(
            bottom: -1,
            right: -1,
            child: GestureDetector(
              onTap: () async {
                profileImage = await ImageUtility.showImageSourceDialog(
                  context,
                );
                if (profileImage != null) {
                  sl<UploadProfileImageBloc>().add(
                    UploadProfileImageEvent.uploadProfileImage(profileImage!),
                  );
                }
                setState(() {});
              },
              child: Container(
                height: 28,
                width: 28,
                decoration: BoxDecoration(
                  color: T.c(context).surface,
                  shape: BoxShape.circle,
                  border: Border.all(color: T.c(context).outline),
                ),
                child: Icon(
                  Icons.camera_alt,
                  size: 14,
                  color: T.c(context).onSurface,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountStatusSection(data) {
    return _buildSection(
      title: 'Account Status',
      icon: LucideIcons.shield,
      children: [
        _buildInfoTile(
          icon: LucideIcons.user,
          title: 'User Type',
          value: data.userType.toUpperCase(),
          color: _getStatusColor(data.userType),
        ),
        _buildInfoTile(
          icon: LucideIcons.activity,
          title: 'Login Status',
          value: data.loginStatus.toUpperCase(),
          color: _getStatusColor(data.loginStatus),
        ),
        _buildInfoTile(
          icon: LucideIcons.calendar,
          title: 'Member Since',
          value: _formatDateTime(data.registeredDate, data.registeredTime),
        ),
        _buildInfoTile(
          icon: LucideIcons.check,
          title: 'Account Activated',
          value: _formatDateTime(data.activationDate, data.activationTime),
        ),
      ],
    );
  }

  Widget _buildPersonalInfoSection(data) {
    return _buildSection(
      title: 'Personal Information',
      icon: LucideIcons.user,
      children: [
        CustomFormFieldWidget(
          label: 'Full Name',
          prefixIcon: LucideIcons.user,
          controller: userNameController,
          enabled: isEditing,
        ),
        CustomFormFieldWidget(
          label: 'Phone Number',
          prefixIcon: LucideIcons.phone,
          controller: phoneNumberController,
          enabled: false,
        ),
        CustomFormFieldWidget(
          label: 'Email Address',
          prefixIcon: LucideIcons.mail,
          controller: emailAddressController,
          enabled: isEditing,
        ),
        CustomFormFieldWidget(
          label: 'Gender',
          prefixIcon: LucideIcons.users,
          controller: genderController,
          enabled: isEditing,
        ),
        CustomFormFieldWidget(
          label: 'Current Address',
          prefixIcon: LucideIcons.mapPin,
          controller: userAddressController,
          enabled: isEditing,
        ),
      ],
    );
  }

  Widget _buildLocationSection(data) {
    return _buildSection(
      title: 'Location Settings',
      icon: LucideIcons.mapPin,
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: T.c(context).surfaceContainerLow,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildLocationButton(
                    icon: LucideIcons.house,
                    label: 'Home',
                    subLabel: 'Set Address',
                    onTap:
                        isEditing
                            ? () {
                              context.pushNamed(
                                AppRoutesName.setHomeLocation,
                                extra: {
                                  'initialLocation': LatLng(
                                    data.homeLatitude,
                                    data.homeLongitude,
                                  ),
                                },
                              );
                            }
                            : null,
                  ),
                  Container(
                    width: 1,
                    height: 48,
                    color: T.c(context).outline,
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                  ),
                  _buildLocationButton(
                    icon: LucideIcons.briefcase,
                    label: 'Work',
                    subLabel: 'Set Address',
                    onTap:
                        isEditing
                            ? () {
                              CustomToast.showInfo(
                                'This feature is currently not available',
                              );
                            }
                            : null,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildLocationInfo(
                      'Home Location',
                      '${data.homeLatitude.toStringAsFixed(6)}, ${data.homeLongitude.toStringAsFixed(6)}',
                      LucideIcons.house,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildLocationInfo(
                      'Current Location',
                      '${data.currentLatitude.toStringAsFixed(6)}, ${data.currentLongitude.toStringAsFixed(6)}',
                      LucideIcons.navigation,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAccountDetailsSection(data) {
    return _buildSection(
      title: 'Account Details',
      icon: LucideIcons.settings,
      children: [
        _buildInfoTile(
          icon: LucideIcons.hash,
          title: 'User ID',
          value: data.userId.toString(),
        ),
        _buildInfoTile(
          icon: LucideIcons.key,
          title: 'Login ID',
          value: data.loginId,
        ),
        _buildInfoTile(
          icon: LucideIcons.messageSquare,
          title: 'Last OTP Sent',
          value: _formatDateTime(data.otpSentDate, data.otpSentTime),
        ),
      ],
    );
  }

  Widget _buildWalletSection(data) {
    return _buildSection(
      title: 'Wallet',
      icon: LucideIcons.wallet,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                T.c(context).primary,
                T.c(context).primary.withOpacity(0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Wallet Balance',
                    style: T
                        .t(context)
                        .bodyMedium
                        ?.copyWith(
                          color: T.c(context).onPrimary.withOpacity(0.8),
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatCurrency(data.walletBalance),
                    style: T
                        .t(context)
                        .headlineMedium
                        ?.copyWith(
                          color: T.c(context).onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: T.c(context).onPrimary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  LucideIcons.wallet,
                  color: T.c(context).onPrimary,
                  size: 24,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: T.c(context).primary),
            const SizedBox(width: 8),
            Text(
              title,
              style: T
                  .t(context)
                  .titleLarge
                  ?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: T.c(context).onSurface,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildInfoTile({
    required IconData icon,
    required String title,
    required String value,
    Color? color,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, size: 16, color: T.c(context).onSurfaceVariant),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: T
                      .t(context)
                      .bodySmall
                      ?.copyWith(color: T.c(context).onSurfaceVariant),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: T
                      .t(context)
                      .bodyMedium
                      ?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: color ?? T.c(context).onSurface,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationInfo(String title, String coordinates, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: T.c(context).surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: T.c(context).outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 14, color: T.c(context).onSurfaceVariant),
              const SizedBox(width: 4),
              Text(
                title,
                style: T
                    .t(context)
                    .bodySmall
                    ?.copyWith(color: T.c(context).onSurfaceVariant),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            coordinates,
            style: T
                .t(context)
                .bodySmall
                ?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 10,
                  color: T.c(context).onSurface,
                ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'online':
        return Colors.green;
      case 'inactive':
      case 'offline':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      default:
        return T.c(context).onSurfaceVariant;
    }
  }

  Widget _buildLocationButton({
    required IconData icon,
    required String label,
    required String subLabel,
    required void Function()? onTap,
  }) {
    final bool isEnabled = onTap != null;

    return Opacity(
      opacity: isEnabled ? 1.0 : 0.5,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: T.c(context).surfaceContainerHigh,
              radius: 20,
              child: Icon(icon, size: 20, color: T.c(context).onSurface),
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  label,
                  style: T
                      .t(context)
                      .bodyMedium
                      ?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: T.c(context).onSurface,
                      ),
                ),
                Text(
                  subLabel,
                  style: T
                      .t(context)
                      .bodySmall
                      ?.copyWith(color: T.c(context).onSurfaceVariant),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
