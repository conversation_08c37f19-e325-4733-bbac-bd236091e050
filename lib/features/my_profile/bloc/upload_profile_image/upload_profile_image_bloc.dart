import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import '../../repositories/my_profile_repository.dart';
part 'upload_profile_image_event.dart';
part 'upload_profile_image_state.dart';
part 'upload_profile_image_bloc.freezed.dart';

class UploadProfileImageBloc
    extends Bloc<UploadProfileImageEvent, UploadProfileImageState> {
  final MyProfileRepository _myProfileRepository;

  UploadProfileImageBloc({required MyProfileRepository repo})
    : _myProfileRepository = repo,
      super(UploadProfileImageState.initial()) {
    on<_UploadProfileImage>(_onProfileImageUpload);
  }

  Future<void> _onProfileImageUpload(
    _UploadProfileImage event,
    Emitter<UploadProfileImageState> emit,
  ) async {
    emit(UploadProfileImageState.loading());

    final result = await _myProfileRepository.uploadProfieImage(
      event.profileImage,
    );

    result.fold(
      (failure) => emit(UploadProfileImageState.failure(failure)),
      (data) => emit(UploadProfileImageState.loaded(data)),
    );
  }
}
