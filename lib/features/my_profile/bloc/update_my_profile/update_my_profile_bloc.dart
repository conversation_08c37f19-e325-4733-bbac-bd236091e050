import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/my_profile/model/update_user_data_model.dart';
import 'package:safari_yatri/features/my_profile/repositories/my_profile_repository.dart';
part 'update_my_profile_event.dart';
part 'update_my_profile_state.dart';
part 'update_my_profile_bloc.freezed.dart';

class UpdateMyProfileBloc
    extends Bloc<UpdateMyProfileEvent, UpdateMyProfileState> {
  final MyProfileRepository _myProfileRepository;

  UpdateMyProfileBloc({required MyProfileRepository repo})
    : _myProfileRepository = repo,
      super(UpdateMyProfileState.initial()) {
    on<_UpdateMyProfile>(_onMyProfileUpdate);
  }

  Future<void> _onMyProfileUpdate(
    _UpdateMyProfile event,
    Emitter<UpdateMyProfileState> emit,
  ) async {
    emit(UpdateMyProfileState.loading());
    final result = await _myProfileRepository.updateProfileDetails(
      updateUserData: event.updateUserData,
    );

    result.fold(
      (failure) => emit(UpdateMyProfileState.failure(failure)),
      (data) => emit(UpdateMyProfileState.loaded(data)),
    );
  }
}
