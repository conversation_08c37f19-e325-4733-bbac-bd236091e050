import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import '../../repositories/my_profile_repository.dart';
part 'terminate_account_event.dart';
part 'terminate_account_state.dart';
part 'terminate_account_bloc.freezed.dart';

class TerminateAccountBloc
    extends Bloc<TerminateAccountEvent, TerminateAccountState> {
  final MyProfileRepository _myProfileRepository;

  TerminateAccountBloc({required MyProfileRepository repo})
    : _myProfileRepository = repo,
      super(TerminateAccountState.initial()) {
    on<TerminateAccountEvent>(_onTerminateAccount);
  }

  Future<void> _onTerminateAccount(
    TerminateAccountEvent event,
    Emitter<TerminateAccountState> emit,
  ) async {
    emit(TerminateAccountState.loading());

    final result = await _myProfileRepository.terminateUserAccount();

    result.fold((failure) => emit(TerminateAccountState.failure(failure)), (
      data,
    ) {
      emit(TerminateAccountState.loaded(data));
    });
  }
}
