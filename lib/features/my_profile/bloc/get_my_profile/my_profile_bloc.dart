import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/services/app_cached_keys.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/my_profile/repositories/my_profile_repository.dart';
import '../../model/my_profile_model.dart';
part 'my_profile_event.dart';
part 'my_profile_state.dart';
part 'my_profile_bloc.freezed.dart';

class MyProfileBloc extends Bloc<MyProfileEvent, MyProfileState> {
  final MyProfileRepository _myProfileRepository;
  final CoreLocalDataSource _coreLocalDataSource = CoreLocalDataSource();
  bool _isInitialize = false;
  GetUserProfileDetailsModel? _getProfileDetails;

  MyProfileBloc({required MyProfileRepository myProfileRepository})
    : _myProfileRepository = myProfileRepository,
      super(MyProfileState.initial()) {
    on<MyProfileEvent>(_onGetProfileDetails);
  }

  Future<void> _onGetProfileDetails(
    MyProfileEvent event,
    Emitter<MyProfileState> emit,
  ) async {
    bool isRefresh = event.refresh ?? false;
    if (!_isInitialize || isRefresh == true) {
      _isInitialize = true;
      emit(MyProfileState.loading());
    }

    if (_getProfileDetails != null && isRefresh == false) {
      emit(MyProfileState.loaded(_getProfileDetails!));
      return;
    }

    final userData = _coreLocalDataSource.getModel(
      AppCachedKeys.userProfileData,
      (json) => GetUserProfileDetailsModel.fromMap(json),
    );
    if (userData != null) {
      emit(MyProfileState.loaded(userData));
    }

    final result = await _myProfileRepository.getProfileDetails();

    result.fold(
      (failure) {
        if (userData != null) {
          ///we are not running if failed and showing old data
          return;
        }

        emit(MyProfileState.failure(failure));
      },
      (data) {
        _getProfileDetails = data;
        emit(MyProfileState.loaded(data));
      },
    );
  }
}
