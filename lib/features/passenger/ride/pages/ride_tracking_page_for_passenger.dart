import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/constant/string_constant.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/bottom_sheet.dart';
import 'package:safari_yatri/core/utils/dialog_utils.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/custom_pop_scope.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/cancel_request/cancel_request_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/pick_location_picking_status/pickup_location_picking_status_cubit.dart';
import 'package:safari_yatri/features/passenger/ride/pages/cancel_booking.dart';
import 'package:safari_yatri/features/passenger/ride/widgets/confirmation_code.dart';
import 'package:shimmer/shimmer.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_current_booking/get_my_current_booking_bloc.dart';

class RideTrackingPageForPassenger extends StatefulWidget {
  const RideTrackingPageForPassenger({super.key});
  @override
  State<RideTrackingPageForPassenger> createState() =>
      _RideTrackingPageForPassengerState();
}

class _RideTrackingPageForPassengerState
    extends State<RideTrackingPageForPassenger> {
  GoogleMapController? _mapController;
  LatLng _currentLocation = const LatLng(27.7172, 85.3240);
  late CurrentLocationBloc _currentLocationBloc;
  late GetMyCurrentBookingBloc _myCurrentBookingBloc;
  late CancelRequestBloc _cancelRequestBloc;

  @override
  void initState() {
    super.initState();
    _currentLocationBloc =
        sl<CurrentLocationBloc>()
          ..add(CurrentLocationEvent.getCurrentLocation());
    _myCurrentBookingBloc =
        sl<GetMyCurrentBookingBloc>()
          ..add(GetMyCurrentBookingEvent.get())
          ..add(GetMyCurrentBookingEvent.start());
    _cancelRequestBloc = sl<CancelRequestBloc>();
  }

  @override
  void dispose() {
    _myCurrentBookingBloc.add(GetMyCurrentBookingEvent.stop());
    _mapController?.dispose();
    super.dispose();
  }

  /// Shows a confirmation dialog before popping the screen.
  void _onPopInvoked(bool didPop, result) {
    if (didPop) return;

    DialogUtil.showAdaptiveDialog(
      context: context,
      title: 'You cannot exit this page right now.',
      content: 'Please wait until the ride is completed.',
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return CustomPopScope(
      onPopInvokedWithResult: _onPopInvoked,
      child: Scaffold(
        body: SafeArea(
          child: RefreshIndicator(
            onRefresh: () async {
              _currentLocationBloc.add(
                CurrentLocationEvent.getCurrentLocation(),
              );
              _myCurrentBookingBloc.add(GetMyCurrentBookingEvent.get());
            },
            child: BlocListener<CurrentLocationBloc, CurrentLocationState>(
              bloc: _currentLocationBloc,
              listener: (context, state) {
                state.whenOrNull(
                  loaded: (data) {
                    _currentLocation = LatLng(data.latitude, data.longitude);
                    _mapController?.animateCamera(
                      CameraUpdate.newCameraPosition(
                        CameraPosition(
                          target: _currentLocation,
                          zoom: kMapInitialZoom,
                        ),
                      ),
                    );
                  },
                );
              },
              child: Column(
                children: [
                  Expanded(
                    child: CustomGoogleMap(
                      initialCameraPosition: CameraPosition(
                        target: _currentLocation,
                        zoom: kMapInitialZoom,
                      ),
                      myLocationEnabled: true,
                      onMapCreated: (controller) {
                        _mapController = controller;
                      },
                    ),
                  ),
                  BlocConsumer<
                    GetMyCurrentBookingBloc,
                    GetMyCurrentBookingState
                  >(
                    bloc: _myCurrentBookingBloc,
                    listener: (context, state) {
                      state.whenOrNull(
                        loaded: (data) {
                          if (data.booking.serviceStatus == kServiceCompleted) {
                            context.goNamed(AppRoutesName.ratePassengerPage);
                          }
                        },
                      );
                    },
                    builder: (context, state) {
                      return state.maybeWhen(
                        loading: () => _buildShimmerUI(),
                        failure: (failure) => _buildErrorWidget(failure),
                        loaded: (data) {
                          return _buildTrackingPage(context, data);
                        },
                        orElse: () => const SizedBox(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Container _buildTrackingPage(
    BuildContext context,
    ({BookingModel booking, bool isFinishedRide}) data,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: T.c(context).surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: T.c(context).shadow.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Text(
            //   "Ride Details",
            //   style: textTheme.titleLarge?.copyWith(
            //     fontWeight: FontWeight.bold,
            //   ),
            // ),
            // const SizedBox(height: 16),
            // _buildInfoRow(
            //   Icons.person_outline,
            //   "Rider",
            //   data.booking.riderName,
            // ),
            const SizedBox(height: 12),
            // _buildInfoRow(
            //   Icons.payments_outlined,
            //   "Fare",
            //   CurrencyFormatter.format(
            //     data.booking.acceptedFareAmount,
            //   ),
            // ),
            // const SizedBox(height: 12),
            // _buildInfoRow(
            //   Icons.trip_origin_outlined,
            //   "Status",
            //   data.booking.serviceStatus,
            // ),
            // Text(da),
            data.booking.serviceConfirmationCode == null
                ? SizedBox()
                : ConfirmationCodeSection(
                  confirmationCode: data.booking.serviceConfirmationCode!,
                ),
            const SizedBox(height: 20),
            data.booking.serviceStatus != kServiceBooked
                ? const SizedBox()
                : _buildCancelButton(data),
          ],
        ),
      ),
    );
  }

  ErrorWidgetWithRetry _buildErrorWidget(Failure failure) {
    return ErrorWidgetWithRetry(
      failure: failure,
      onRetry: () {
        _myCurrentBookingBloc.add(GetMyCurrentBookingEvent.get());
      },
    );
  }

  BlocListener<CancelRequestBloc, CancelRequestState> _buildCancelButton(
    ({BookingModel booking, bool isFinishedRide}) data,
  ) {
    return BlocListener<CancelRequestBloc, CancelRequestState>(
      bloc: _cancelRequestBloc,
      listener: _cancelRequestListener,
      child: SizedBox(
        width: double.infinity,
        child: FilledButton(
          onPressed: () => _onCancelPressed(data.booking.bookingId),
          style: FilledButton.styleFrom(
            backgroundColor: Colors.red.withOpacity(0.1),
            foregroundColor: Colors.red.shade800,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
          child: const Text("Cancel Ride"),
        ),
      ),
    );
  }

  void _cancelRequestListener(BuildContext context, CancelRequestState state) {
    state.whenOrNull(
      loading: () => AppLoadingDialog.show(context),
      loaded: (data) {
        AppLoadingDialog.hide(context);
        CustomToast.showSuccess("Ride cancelled successfully");
        sl<PassengerRouteBloc>().add(PassengerRouteEvent.resetPassengerRoute());
        sl<PickupLocationPickingStatusCubit>().enable();

        context.goNamed(AppRoutesName.passengerHome);
      },
      failure: (failure) {
        AppLoadingDialog.hide(context);
        CustomToast.showError(failure.message);
      },
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor, size: 20),
        const SizedBox(width: 12),
        Text("$label: ", style: Theme.of(context).textTheme.bodyMedium),
        Expanded(
          child: Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerUI() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Card(
        margin: const EdgeInsets.all(12),
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(width: 120, height: 24, color: Colors.white),
              const SizedBox(height: 16),
              _buildShimmerRow(),
              const SizedBox(height: 12),
              _buildShimmerRow(),
              const SizedBox(height: 12),
              _buildShimmerRow(),
              const SizedBox(height: 20),
              Container(
                width: double.infinity,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerRow() {
    return Row(
      children: [
        Container(width: 20, height: 20, color: Colors.white),
        const SizedBox(width: 12),
        Container(width: 50, height: 16, color: Colors.white),
        const Spacer(),
        Container(width: 100, height: 16, color: Colors.white),
      ],
    );
  }

  void _onCancelPressed(int bookingId) {
    appShowModalBottomSheet(
      context,
      child: CancelRideBottomSheet(
        onReasonSelected: (reason) {
          // Close bottom sheet before showing dialog
          Navigator.pop(context);
          _cancelRequestBloc.add(
            CancelRequestEvent.cancelRequest(bookingId, reason),
          );
        },
      ),
    );
  }
}
