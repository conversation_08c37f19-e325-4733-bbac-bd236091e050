import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/booking_trip_widget.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_booking/get_my_booking_bloc.dart';

class PassengerTripPage extends StatefulWidget {
  const PassengerTripPage({super.key});

  @override
  State<PassengerTripPage> createState() => _PassengerTripPageState();
}

class _PassengerTripPageState extends State<PassengerTripPage> {
  late final GetMyBookingBloc _bookingBloc;

  @override
  void initState() {
    super.initState();
    _bookingBloc =
        sl<GetMyBookingBloc>()..add(GetMyBookingEvent.getActiveBookings());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: T.c(context).surfaceContainer,
      appBar: CustomAppBar(title: const Text('My Trips')),
      body: BlocBuilder<GetMyBookingBloc, GetMyBookingState>(
        bloc: _bookingBloc,
        builder: (context, state) {
          return state.maybeWhen(
            loading:
                () => const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                ),
            loaded: (bookings) {
              if (bookings.isEmpty) {
                return _buildEmptyState();
              }
              return _buildMyBookings(bookings);
            },
            failure: (message) => _buildErrorState(message.message),
            orElse: () => const SizedBox(),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.drive_eta_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text('No trips found', style: T.t(context).displaySmall),
          const SizedBox(height: 8),
          Text(
            'Your booking history will appear here',
            style: T.t(context).labelSmall,
          ),

          const SizedBox(height: 24),
          CustomButtonPrimary(
            width: MediaQuery.sizeOf(context).width * 0.8,
            title: 'Refresh Bookings',
            onPressed: () {
              _bookingBloc.add(GetMyBookingEvent.getActiveBookings());
            },
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 80, color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            'Something went wrong',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          CustomButtonPrimary(
            title: 'Try Again',
            onPressed: () {
              _bookingBloc.add(GetMyBookingEvent.getActiveBookings());
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMyBookings(List<BookingModel> bookings) {
    return RefreshIndicator(
      onRefresh: () async {
        _bookingBloc.add(GetMyBookingEvent.getActiveBookings());
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: bookings.length,
        itemBuilder: (context, index) {
          final booking = bookings[index];
          return TripBookingCart(booking: booking);
        },
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
