import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';

class RideInfoCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String value;
  final Color iconColor;

  const RideInfoCard({
    super.key,
    required this.icon,
    required this.title,
    required this.value,
    required this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainerHighest.withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: T.c(context).outline.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: iconColor, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: T
                      .t(context)
                      .bodyMedium
                      ?.copyWith(
                        color: T.c(context).onSurface.withOpacity(0.7),
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: T
                      .t(context)
                      .titleMedium
                      ?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: T.c(context).onSurface,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
