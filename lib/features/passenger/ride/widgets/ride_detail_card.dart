import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/constant/string_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/currency_formattor.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/features/booking/blocs/cancel_request/cancel_request_bloc.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/pick_location_picking_status/pickup_location_picking_status_cubit.dart';
import 'package:safari_yatri/features/passenger/ride/widgets/confirmation_code.dart';
import 'package:safari_yatri/features/passenger/ride/widgets/ride_info_card.dart';

class RideDetailsCard extends StatelessWidget {
  final BookingModel booking;
  final CancelRequestBloc cancelRequestBloc;
  final Function(int bookingId) onCancelPressed;

  const RideDetailsCard({
    super.key,
    required this.booking,
    required this.cancelRequestBloc,
    required this.onCancelPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: T.c(context).surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
        boxShadow: [
          BoxShadow(
            color: T.c(context).shadow.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -10),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDragHandle(context),
            const SizedBox(height: 20),
            _buildTitle(context),
            const SizedBox(height: 24),
            if (_shouldShowConfirmationCode())
              ...[
                ConfirmationCodeSection(
                  confirmationCode: booking.serviceConfirmationCode!,
                ),
                const SizedBox(height: 24),
              ],
            _buildRideInfoCards(context),
            const SizedBox(height: 24),
            if (booking.serviceStatus == kServiceBooked)
              _buildCancelButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDragHandle(BuildContext context) {
    return Center(
      child: Container(
        width: 40,
        height: 4,
        decoration: BoxDecoration(
          color: T.c(context).onSurface.withOpacity(0.3),
          borderRadius: BorderRadius.circular(2),
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      "Ride Details",
      style: T.t(context).headlineSmall?.copyWith(
        fontWeight: FontWeight.bold,
        color: T.c(context).onSurface,
      ),
    );
  }

  Widget _buildRideInfoCards(BuildContext context) {
    return Column(
      children: [
        RideInfoCard(
          icon: Icons.person_outline,
          title: "Rider",
          value: booking.riderName,
          iconColor: T.c(context).primary,
        ),
        const SizedBox(height: 16),
        RideInfoCard(
          icon: Icons.payments_outlined,
          title: "Fare",
          value: CurrencyFormatter.format(booking.acceptedFareAmount),
          iconColor: Colors.green,
        ),
        if (booking.passengerCount > 1) ...[
          const SizedBox(height: 16),
          RideInfoCard(
            icon: Icons.group_outlined,
            title: "Passengers",
            value: booking.passengerCount.toString(),
            iconColor: Colors.blue,
          ),
        ],
      ],
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return BlocListener<CancelRequestBloc, CancelRequestState>(
      bloc: cancelRequestBloc,
      listener: (context, state) => _handleCancelRequestState(context, state),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () => onCancelPressed(booking.bookingId),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.cancel_outlined),
              const SizedBox(width: 8),
              Text(
                "Cancel Ride",
                style: T.t(context).titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  bool _shouldShowConfirmationCode() {
    return booking.serviceStatus == kServiceStarted &&
        booking.serviceConfirmationCode != null &&
        booking.serviceConfirmationCode!.isNotEmpty;
  }

  void _handleCancelRequestState(BuildContext context, CancelRequestState state) {
    state.whenOrNull(
      loading: () => AppLoadingDialog.show(context),
      loaded: (data) {
        AppLoadingDialog.hide(context);
        CustomToast.showSuccess("Ride cancelled successfully");
        sl<PassengerRouteBloc>().add(PassengerRouteEvent.resetPassengerRoute());
        sl<PickupLocationPickingStatusCubit>().enable();
        context.goNamed(AppRoutesName.passengerHome);
      },
      failure: (failure) {
        AppLoadingDialog.hide(context);
        CustomToast.showError(failure.message);
      },
    );
  }
}