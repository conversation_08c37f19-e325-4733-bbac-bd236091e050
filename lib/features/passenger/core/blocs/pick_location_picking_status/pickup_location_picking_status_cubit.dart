import 'package:flutter_bloc/flutter_bloc.dart' show Cubit;

part 'pickup_location_picking_status_state.dart';

///pick up location kaha bata pick garney ho testko status xa k
///vanna le aba hamilaii kina chaiyo vanda tyo [PassengerHome]
///
///sectin maa k hudaii xa pick hudainxa vane tyo bottomsheet laii remove
///garney navaye tehii rakhne banaune anii aru like pick garna dine nadien
///lagayat ka kura haru xa
///
class PickupLocationPickingStatusCubit
    extends Cubit<PickupLocationPickingStatusState> {
  PickupLocationPickingStatusCubit()
    : super(PickupLocationPickingStatusState.picked);

  void startPicking() {
    if (state == PickupLocationPickingStatusState.disable) return;
    emit(PickupLocationPickingStatusState.picking);
  }

  void stopPicking() {
    if (state == PickupLocationPickingStatusState.disable) return;

    emit(PickupLocationPickingStatusState.picked);
  }

  void disable() {
    emit(PickupLocationPickingStatusState.disable);
  }

  void enable() {
    emit(PickupLocationPickingStatusState.enable);
  }
}
