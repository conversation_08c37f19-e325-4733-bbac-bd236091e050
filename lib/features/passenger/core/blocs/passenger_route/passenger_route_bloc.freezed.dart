// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'passenger_route_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PassengerRouteEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is PassengerRouteEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PassengerRouteEvent()';
  }
}

/// Adds pattern-matching-related methods to [PassengerRouteEvent].
extension PassengerRouteEventPatterns on PassengerRouteEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PickUpLocation value)? pickUpLocation,
    TResult Function(_DestinationLocation value)? destinationLocation,
    TResult Function(_UpdateDestinationLocation value)?
    updateDestinationLocation,
    TResult Function(_GetRoutes value)? getRoutes,
    TResult Function(_InitRoutes value)? initRoutes,
    TResult Function(_ResetPassengerRoute value)? resetPassengerRoute,
    TResult Function(_RemoveDestionationLocation value)?
    removeDestinationLocation,
    TResult Function(_UpdatePickupLocationWithPlaceId value)?
    updatePickUpLocationWithPlaceId,
    TResult Function(_AddOrUpdateDropoffLocationWithPlaceId value)?
    addOrUpdateDropoffLocationWithPlaceId,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PickUpLocation() when pickUpLocation != null:
        return pickUpLocation(_that);
      case _DestinationLocation() when destinationLocation != null:
        return destinationLocation(_that);
      case _UpdateDestinationLocation() when updateDestinationLocation != null:
        return updateDestinationLocation(_that);
      case _GetRoutes() when getRoutes != null:
        return getRoutes(_that);
      case _InitRoutes() when initRoutes != null:
        return initRoutes(_that);
      case _ResetPassengerRoute() when resetPassengerRoute != null:
        return resetPassengerRoute(_that);
      case _RemoveDestionationLocation() when removeDestinationLocation != null:
        return removeDestinationLocation(_that);
      case _UpdatePickupLocationWithPlaceId()
          when updatePickUpLocationWithPlaceId != null:
        return updatePickUpLocationWithPlaceId(_that);
      case _AddOrUpdateDropoffLocationWithPlaceId()
          when addOrUpdateDropoffLocationWithPlaceId != null:
        return addOrUpdateDropoffLocationWithPlaceId(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PickUpLocation value) pickUpLocation,
    required TResult Function(_DestinationLocation value) destinationLocation,
    required TResult Function(_UpdateDestinationLocation value)
    updateDestinationLocation,
    required TResult Function(_GetRoutes value) getRoutes,
    required TResult Function(_InitRoutes value) initRoutes,
    required TResult Function(_ResetPassengerRoute value) resetPassengerRoute,
    required TResult Function(_RemoveDestionationLocation value)
    removeDestinationLocation,
    required TResult Function(_UpdatePickupLocationWithPlaceId value)
    updatePickUpLocationWithPlaceId,
    required TResult Function(_AddOrUpdateDropoffLocationWithPlaceId value)
    addOrUpdateDropoffLocationWithPlaceId,
  }) {
    final _that = this;
    switch (_that) {
      case _PickUpLocation():
        return pickUpLocation(_that);
      case _DestinationLocation():
        return destinationLocation(_that);
      case _UpdateDestinationLocation():
        return updateDestinationLocation(_that);
      case _GetRoutes():
        return getRoutes(_that);
      case _InitRoutes():
        return initRoutes(_that);
      case _ResetPassengerRoute():
        return resetPassengerRoute(_that);
      case _RemoveDestionationLocation():
        return removeDestinationLocation(_that);
      case _UpdatePickupLocationWithPlaceId():
        return updatePickUpLocationWithPlaceId(_that);
      case _AddOrUpdateDropoffLocationWithPlaceId():
        return addOrUpdateDropoffLocationWithPlaceId(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PickUpLocation value)? pickUpLocation,
    TResult? Function(_DestinationLocation value)? destinationLocation,
    TResult? Function(_UpdateDestinationLocation value)?
    updateDestinationLocation,
    TResult? Function(_GetRoutes value)? getRoutes,
    TResult? Function(_InitRoutes value)? initRoutes,
    TResult? Function(_ResetPassengerRoute value)? resetPassengerRoute,
    TResult? Function(_RemoveDestionationLocation value)?
    removeDestinationLocation,
    TResult? Function(_UpdatePickupLocationWithPlaceId value)?
    updatePickUpLocationWithPlaceId,
    TResult? Function(_AddOrUpdateDropoffLocationWithPlaceId value)?
    addOrUpdateDropoffLocationWithPlaceId,
  }) {
    final _that = this;
    switch (_that) {
      case _PickUpLocation() when pickUpLocation != null:
        return pickUpLocation(_that);
      case _DestinationLocation() when destinationLocation != null:
        return destinationLocation(_that);
      case _UpdateDestinationLocation() when updateDestinationLocation != null:
        return updateDestinationLocation(_that);
      case _GetRoutes() when getRoutes != null:
        return getRoutes(_that);
      case _InitRoutes() when initRoutes != null:
        return initRoutes(_that);
      case _ResetPassengerRoute() when resetPassengerRoute != null:
        return resetPassengerRoute(_that);
      case _RemoveDestionationLocation() when removeDestinationLocation != null:
        return removeDestinationLocation(_that);
      case _UpdatePickupLocationWithPlaceId()
          when updatePickUpLocationWithPlaceId != null:
        return updatePickUpLocationWithPlaceId(_that);
      case _AddOrUpdateDropoffLocationWithPlaceId()
          when addOrUpdateDropoffLocationWithPlaceId != null:
        return addOrUpdateDropoffLocationWithPlaceId(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(LatLng position)? pickUpLocation,
    TResult Function(LatLng position)? destinationLocation,
    TResult Function(LatLng position, int index)? updateDestinationLocation,
    TResult Function()? getRoutes,
    TResult Function()? initRoutes,
    TResult Function()? resetPassengerRoute,
    TResult Function(int index)? removeDestinationLocation,
    TResult Function(String placeId)? updatePickUpLocationWithPlaceId,
    TResult Function(String placeId, int? index)?
    addOrUpdateDropoffLocationWithPlaceId,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PickUpLocation() when pickUpLocation != null:
        return pickUpLocation(_that.position);
      case _DestinationLocation() when destinationLocation != null:
        return destinationLocation(_that.position);
      case _UpdateDestinationLocation() when updateDestinationLocation != null:
        return updateDestinationLocation(_that.position, _that.index);
      case _GetRoutes() when getRoutes != null:
        return getRoutes();
      case _InitRoutes() when initRoutes != null:
        return initRoutes();
      case _ResetPassengerRoute() when resetPassengerRoute != null:
        return resetPassengerRoute();
      case _RemoveDestionationLocation() when removeDestinationLocation != null:
        return removeDestinationLocation(_that.index);
      case _UpdatePickupLocationWithPlaceId()
          when updatePickUpLocationWithPlaceId != null:
        return updatePickUpLocationWithPlaceId(_that.placeId);
      case _AddOrUpdateDropoffLocationWithPlaceId()
          when addOrUpdateDropoffLocationWithPlaceId != null:
        return addOrUpdateDropoffLocationWithPlaceId(
          _that.placeId,
          _that.index,
        );
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(LatLng position) pickUpLocation,
    required TResult Function(LatLng position) destinationLocation,
    required TResult Function(LatLng position, int index)
    updateDestinationLocation,
    required TResult Function() getRoutes,
    required TResult Function() initRoutes,
    required TResult Function() resetPassengerRoute,
    required TResult Function(int index) removeDestinationLocation,
    required TResult Function(String placeId) updatePickUpLocationWithPlaceId,
    required TResult Function(String placeId, int? index)
    addOrUpdateDropoffLocationWithPlaceId,
  }) {
    final _that = this;
    switch (_that) {
      case _PickUpLocation():
        return pickUpLocation(_that.position);
      case _DestinationLocation():
        return destinationLocation(_that.position);
      case _UpdateDestinationLocation():
        return updateDestinationLocation(_that.position, _that.index);
      case _GetRoutes():
        return getRoutes();
      case _InitRoutes():
        return initRoutes();
      case _ResetPassengerRoute():
        return resetPassengerRoute();
      case _RemoveDestionationLocation():
        return removeDestinationLocation(_that.index);
      case _UpdatePickupLocationWithPlaceId():
        return updatePickUpLocationWithPlaceId(_that.placeId);
      case _AddOrUpdateDropoffLocationWithPlaceId():
        return addOrUpdateDropoffLocationWithPlaceId(
          _that.placeId,
          _that.index,
        );
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(LatLng position)? pickUpLocation,
    TResult? Function(LatLng position)? destinationLocation,
    TResult? Function(LatLng position, int index)? updateDestinationLocation,
    TResult? Function()? getRoutes,
    TResult? Function()? initRoutes,
    TResult? Function()? resetPassengerRoute,
    TResult? Function(int index)? removeDestinationLocation,
    TResult? Function(String placeId)? updatePickUpLocationWithPlaceId,
    TResult? Function(String placeId, int? index)?
    addOrUpdateDropoffLocationWithPlaceId,
  }) {
    final _that = this;
    switch (_that) {
      case _PickUpLocation() when pickUpLocation != null:
        return pickUpLocation(_that.position);
      case _DestinationLocation() when destinationLocation != null:
        return destinationLocation(_that.position);
      case _UpdateDestinationLocation() when updateDestinationLocation != null:
        return updateDestinationLocation(_that.position, _that.index);
      case _GetRoutes() when getRoutes != null:
        return getRoutes();
      case _InitRoutes() when initRoutes != null:
        return initRoutes();
      case _ResetPassengerRoute() when resetPassengerRoute != null:
        return resetPassengerRoute();
      case _RemoveDestionationLocation() when removeDestinationLocation != null:
        return removeDestinationLocation(_that.index);
      case _UpdatePickupLocationWithPlaceId()
          when updatePickUpLocationWithPlaceId != null:
        return updatePickUpLocationWithPlaceId(_that.placeId);
      case _AddOrUpdateDropoffLocationWithPlaceId()
          when addOrUpdateDropoffLocationWithPlaceId != null:
        return addOrUpdateDropoffLocationWithPlaceId(
          _that.placeId,
          _that.index,
        );
      case _:
        return null;
    }
  }
}

/// @nodoc

class _PickUpLocation implements PassengerRouteEvent {
  const _PickUpLocation({required this.position});

  final LatLng position;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PickUpLocation &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @override
  int get hashCode => Object.hash(runtimeType, position);

  @override
  String toString() {
    return 'PassengerRouteEvent.pickUpLocation(position: $position)';
  }
}

/// @nodoc

class _DestinationLocation implements PassengerRouteEvent {
  const _DestinationLocation({required this.position});

  final LatLng position;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DestinationLocation &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @override
  int get hashCode => Object.hash(runtimeType, position);

  @override
  String toString() {
    return 'PassengerRouteEvent.destinationLocation(position: $position)';
  }
}

/// @nodoc

class _UpdateDestinationLocation implements PassengerRouteEvent {
  const _UpdateDestinationLocation({
    required this.position,
    required this.index,
  });

  final LatLng position;
  final int index;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateDestinationLocation &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.index, index) || other.index == index));
  }

  @override
  int get hashCode => Object.hash(runtimeType, position, index);

  @override
  String toString() {
    return 'PassengerRouteEvent.updateDestinationLocation(position: $position, index: $index)';
  }
}

/// @nodoc

class _GetRoutes implements PassengerRouteEvent {
  const _GetRoutes();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _GetRoutes);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PassengerRouteEvent.getRoutes()';
  }
}

/// @nodoc

class _InitRoutes implements PassengerRouteEvent {
  const _InitRoutes();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _InitRoutes);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PassengerRouteEvent.initRoutes()';
  }
}

/// @nodoc

class _ResetPassengerRoute implements PassengerRouteEvent {
  const _ResetPassengerRoute();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ResetPassengerRoute);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PassengerRouteEvent.resetPassengerRoute()';
  }
}

/// @nodoc

class _RemoveDestionationLocation implements PassengerRouteEvent {
  const _RemoveDestionationLocation({required this.index});

  final int index;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RemoveDestionationLocation &&
            (identical(other.index, index) || other.index == index));
  }

  @override
  int get hashCode => Object.hash(runtimeType, index);

  @override
  String toString() {
    return 'PassengerRouteEvent.removeDestinationLocation(index: $index)';
  }
}

/// @nodoc

class _UpdatePickupLocationWithPlaceId implements PassengerRouteEvent {
  const _UpdatePickupLocationWithPlaceId(this.placeId);

  final String placeId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdatePickupLocationWithPlaceId &&
            (identical(other.placeId, placeId) || other.placeId == placeId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, placeId);

  @override
  String toString() {
    return 'PassengerRouteEvent.updatePickUpLocationWithPlaceId(placeId: $placeId)';
  }
}

/// @nodoc

class _AddOrUpdateDropoffLocationWithPlaceId implements PassengerRouteEvent {
  const _AddOrUpdateDropoffLocationWithPlaceId({
    required this.placeId,
    this.index,
  });

  final String placeId;
  final int? index;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AddOrUpdateDropoffLocationWithPlaceId &&
            (identical(other.placeId, placeId) || other.placeId == placeId) &&
            (identical(other.index, index) || other.index == index));
  }

  @override
  int get hashCode => Object.hash(runtimeType, placeId, index);

  @override
  String toString() {
    return 'PassengerRouteEvent.addOrUpdateDropoffLocationWithPlaceId(placeId: $placeId, index: $index)';
  }
}
