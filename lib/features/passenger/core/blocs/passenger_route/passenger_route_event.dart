part of 'passenger_route_bloc.dart';

@freezed
abstract class PassengerRouteEvent with _$PassengerRouteEvent {
  const factory PassengerRouteEvent.pickUpLocation({required LatLng position}) =
      _PickUpLocation;
  const factory PassengerRouteEvent.destinationLocation({
    required LatLng position,
  }) = _DestinationLocation;

  const factory PassengerRouteEvent.updateDestinationLocation({
    required LatLng position,
    required int index,
  }) = _UpdateDestinationLocation;

  const factory PassengerRouteEvent.getRoutes() = _GetRoutes;
  const factory PassengerRouteEvent.initRoutes() = _InitRoutes;
  const factory PassengerRouteEvent.resetPassengerRoute() =
      _ResetPassengerRoute;
  const factory PassengerRouteEvent.removeDestinationLocation({
    required int index,
  }) = _RemoveDestionationLocation;

  const factory PassengerRouteEvent.updatePickUpLocationWithPlaceId(
    String placeId,
  ) = _UpdatePickupLocationWithPlaceId;

  const factory PassengerRouteEvent.addOrUpdateDropoffLocationWithPlaceId({
    required String placeId,
    int? index,
  }) = _AddOrUpdateDropoffLocationWithPlaceId;
}
