// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'passenger_route_bloc.dart';

typedef PassengerRouteState = BaseState<PassengerRouteLoaded>;

class PassengerRouteLoaded {
  final LatLngWithAddress? currentPassengerLocationWithAddress;
  final LatLngWithAddress pickupLocation;
  final List<LatLngWithAddress> dropoffLocations;
  final LatLng? currentLocation;
  final List<LatLng> polylinePoints;
  final DirectionRouteModel? directionRoute;

  PassengerRouteLoaded({
     this.directionRoute,
    required this.currentPassengerLocationWithAddress,
    required this.pickupLocation,
    required this.dropoffLocations,
    this.currentLocation,
    required this.polylinePoints,
  });
}
