import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/core/widget/ripple_animation.dart';
import 'package:safari_yatri/features/booking/blocs/add_to_cart_booking/add_to_cart_booking_bloc.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';

class RideRequestPageMapWidget extends StatefulWidget {
  const RideRequestPageMapWidget({
    super.key,
    required this.maxRiderSearchDiameter,
    required this.currentLocationBloc,
  });

  final int maxRiderSearchDiameter;
  final CurrentLocationBloc currentLocationBloc;

  @override
  State<RideRequestPageMapWidget> createState() =>
      _RideRequestPageMapWidgetState();
}

class _RideRequestPageMapWidgetState extends State<RideRequestPageMapWidget> {
  GoogleMapController? _mapController;
  LatLng _currentLatLng = const LatLng(27.7172, 85.3240);

  Timer? _zoomTimer;
  int _zoomTickCounter = 0;
  late int _currentSearchDistance;
  int currentSearchCount = 0;
  bool _isCurrentLocationLoaded = false;
  bool _zoomLimitReached = false;

  static const double _minZoom = 16.0;
  static const double _maxZoom = 21.0;
  static const double _zoomStep = 0.002;
  static const int _zoomTickThreshold = 180;

  CameraPosition _lastCameraPosition = CameraPosition(
    target: LatLng(27.7172, 85.3240),
    zoom: kMapInitialZoom,
  );

  @override
  void initState() {
    super.initState();
    _currentSearchDistance = kInitialRiderSearchDiameterInMeter;
  }

  void _startSearchTimer() {
    if (_zoomTimer == null && _isCurrentLocationLoaded) {
      _zoomTimer = Timer.periodic(const Duration(milliseconds: 250), (_) {
        if (!_zoomLimitReached) zoomOutSlightly();

        _zoomTickCounter++;
        if (_zoomTickCounter >= _zoomTickThreshold) {
          raiseSearchingDistanceInDatabase();
          _zoomTickCounter = 0;
        }
      });
    }
  }

  void zoomOutSlightly() {
    if (_mapController == null) return;

    final currentZoom = _lastCameraPosition.zoom;
    final newZoom = (currentZoom - _zoomStep).clamp(_minZoom, _maxZoom);

    if (newZoom == _minZoom) {
      _zoomLimitReached = true;
      return;
    }

    final updatedPosition = CameraPosition(
      target: _lastCameraPosition.target,
      zoom: newZoom,
    );

    _mapController!.animateCamera(
      CameraUpdate.newCameraPosition(updatedPosition),
    );
    _lastCameraPosition = updatedPosition;
  }

  void raiseSearchingDistanceInDatabase() {
    currentSearchCount++;
    _currentSearchDistance += kRiderSearchDiameterIncrement;

    if (_currentSearchDistance > widget.maxRiderSearchDiameter) {
      // Keep timer running, but do not raise distance anymore
      return;
    }

    sl<AddToCartBookingBloc>().add(
      AddToCartBookingEvent.expandSearchDiameter(
        maxDriverSearchingAreaInMeter: _currentSearchDistance,
      ),
    );
  }

  @override
  void dispose() {
    _zoomTimer?.cancel();
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      child: BlocListener<CurrentLocationBloc, CurrentLocationState>(
        bloc: widget.currentLocationBloc,
        listener: (context, state) {
          state.maybeWhen(
            loaded: (data) {
              _currentLatLng = LatLng(data.latitude, data.longitude);
              _lastCameraPosition = CameraPosition(
                target: _currentLatLng,
                zoom: kMapInitialZoom,
              );
              _mapController?.moveCamera(
                CameraUpdate.newCameraPosition(_lastCameraPosition),
              );

              _isCurrentLocationLoaded = true;
              _startSearchTimer();
              setState(() {});
            },
            orElse: () {},
          );
        },
        child: SizedBox(
          height: MediaQuery.sizeOf(context).height * 0.6,
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Stack(
                children: [
                  CustomGoogleMap(
                    onMapCreated: (controller) {
                      _mapController = controller;
                      if (_isCurrentLocationLoaded) _startSearchTimer();
                    },
                    initialCameraPosition: _lastCameraPosition,
                    onCameraMove: (position) {
                      _lastCameraPosition = position;
                    },
                  ),
                  Positioned(
                    bottom: constraints.maxHeight * 0.5 - 20,
                    left: 0,
                    right: 0,
                    child: LayeredRippleEffect(
                      child: SizedBox(
                        height: 50,
                        width: 50,
                        child: SvgPicture.asset(ImageConstant.mapDestination1),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
