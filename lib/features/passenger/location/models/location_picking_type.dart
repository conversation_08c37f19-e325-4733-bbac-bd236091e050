enum LocationType {
  pickup,
  destination,
}

class LocationPickingType {
  final LocationType type;
  final int? index;

  const LocationPickingType.pickup()
      : type = LocationType.pickup,
        index = null;

  const LocationPickingType.destination(this.index)
      : type = LocationType.destination;

  bool get isPickup => type == LocationType.pickup;
  bool get isDestination => type == LocationType.destination;
}
