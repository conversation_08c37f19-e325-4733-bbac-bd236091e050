import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/common/widgets/shimmer_widget.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart'; // Your T class
import 'package:safari_yatri/core/theme/app_colors.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/location/blocs/places/places_bloc.dart';
import 'package:safari_yatri/features/location/models/place_model.dart'; // Assuming PlacePrediction is here
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/passenger/location/models/location_picking_type.dart';

class EnterRouteBottomSheetPage extends StatefulWidget {
  const EnterRouteBottomSheetPage({
    super.key,
    required this.type,
    required this.shouldHidePickupLocation,
  });

  final LocationType type;
  final bool shouldHidePickupLocation;

  @override
  State<EnterRouteBottomSheetPage> createState() =>
      _EnterRouteBottomSheetPageState();
}

class _EnterRouteBottomSheetPageState extends State<EnterRouteBottomSheetPage> {
  final TextEditingController _pickupController = TextEditingController();
  final TextEditingController _destinationController = TextEditingController();
  final FocusNode _pickUpFocusNode = FocusNode();
  final FocusNode _destinationFocusNode = FocusNode();

  int? _getLastDestinationLocationIndex;
  Timer? _debounce;
  List<PlacePrediction> _places = [];
  bool _showPlaces = false;

  late String _initialPickUpLocation = '';
  late String _initialDestinationLocation = '';
  String? _lastPickupSearchText;
  String? _lastDestinationSearchText;

  @override
  void initState() {
    super.initState();

    if (widget.type == LocationType.pickup) {
      _pickUpFocusNode.requestFocus();
    } else {
      _destinationFocusNode.requestFocus();
    }

    _pickUpFocusNode.addListener(_handleFocusChange);
    _destinationFocusNode.addListener(_handleFocusChange);

    _pickupController.addListener(_onPickupTextChanged);
    _destinationController.addListener(_onDestinationTextChanged);
  }

  void _handleFocusChange() {
    if (mounted) {
      setState(() {});
    }
  }

  void _onPickupTextChanged() {
    final currentText = _pickupController.text;
    _showPlaces = currentText.isNotEmpty && _pickUpFocusNode.hasFocus;
    if (currentText != _lastPickupSearchText) {
      _debounceTextChange(currentText, isPickup: true);
      _lastPickupSearchText = currentText;
    }
  }

  void _onDestinationTextChanged() {
    final currentText = _destinationController.text;
    _showPlaces = currentText.isNotEmpty && _destinationFocusNode.hasFocus;
    if (currentText != _lastDestinationSearchText) {
      _debounceTextChange(currentText, isPickup: false);
      _lastDestinationSearchText = currentText;
    }
  }

  void _debounceTextChange(String text, {required bool isPickup}) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      if (text == _initialPickUpLocation ||
          text == _initialDestinationLocation) {
        return;
      }
      sl<PlacesBloc>().add(PlacesEvent.searchPlaces(text));
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final state = sl<PassengerRouteBloc>().state;

    state.whenOrNull(
      loaded: (data) {
        if (_pickupController.text != data.pickupLocation.address) {
          _pickupController.text = data.pickupLocation.address;
          _initialPickUpLocation = data.pickupLocation.address;
        }
        final dropoffAddress =
            widget.shouldHidePickupLocation
                ? ""
                : data.dropoffLocations.lastOrNull?.address ?? "";
        if (_destinationController.text != dropoffAddress) {
          _destinationController.text = dropoffAddress;
          _initialDestinationLocation = dropoffAddress;
        }
        if (data.dropoffLocations.isNotEmpty) {
          _getLastDestinationLocationIndex = data.dropoffLocations.length - 1;
        }
      },
    );
  }

  void _updateRoute(PassengerRouteLoaded data) {
    if (_pickupController.text != data.pickupLocation.address) {
      _pickupController.text = data.pickupLocation.address;
      _initialPickUpLocation = data.pickupLocation.address;
    }
    final dropoffAddress = data.dropoffLocations.lastOrNull?.address ?? "";
    if (_destinationController.text != dropoffAddress) {
      _destinationController.text = dropoffAddress;
      _initialDestinationLocation = dropoffAddress;
    }
    if (data.dropoffLocations.isNotEmpty) {
      _getLastDestinationLocationIndex = data.dropoffLocations.length - 1;
    }
  }

  @override
  void dispose() {
    _pickUpFocusNode.removeListener(_handleFocusChange);
    _destinationFocusNode.removeListener(_handleFocusChange);
    _pickupController.removeListener(_onPickupTextChanged);
    _destinationController.removeListener(_onDestinationTextChanged);
    _debounce?.cancel();
    _pickUpFocusNode.dispose();
    _destinationFocusNode.dispose();
    _pickupController.dispose();
    _destinationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // context is available here!
    return BlocListener<PassengerRouteBloc, PassengerRouteState>(
      listener: (context, state) {
        state.whenOrNull(
          loaded: (data) {
            _updateRoute(data);
          },
        );
      },
      child: BlocConsumer<PlacesBloc, PlacesState>(
        listener: (context, state) {
          state.whenOrNull(
            loaded: (predictions) {
              if (mounted) {
                setState(() {
                  _places = predictions.cast<PlacePrediction>();
                });
              }
            },
            loading: () {
              if (mounted) {
                setState(() {
                  _places = [];
                });
              }
            },
            failure: (failure) {
              if (mounted) {
                setState(() {
                  _places = [];
                });
                CustomToast.showError(failure.message);
              }
            },
          );
        },
        builder: (context, placesState) {
          return Scaffold(
            backgroundColor:
                T.c(context).surfaceContainerLowest, // FIX: Add (context)
            resizeToAvoidBottomInset: true,
            bottomNavigationBar: SafeArea(
              child: SizedBox(
                height: 80,
                child: Align(
                  alignment: Alignment.bottomRight,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 16, bottom: 16),
                    child: CustomButtonPrimary(
                      width: null,
                      onPressed: () {
                        context.pop();
                      },
                      title: "Continue",
                    ),
                  ),
                ),
              ),
            ),
            body: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppStyles.space16,
                ),
                child: Stack(
                  children: [
                    SingleChildScrollView(
                      padding: const EdgeInsets.only(bottom: 100),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Center(
                            child: Text(
                              "Enter your route",
                              style: T
                                  .t(context)
                                  .displaySmall
                                  ?.copyWith(
                                    // FIX: Add (context)
                                    color:
                                        T
                                            .c(context)
                                            .onSurface, // FIX: Add (context)
                                  ),
                            ),
                          ),
                          const Gap(24),
                          if (!widget.shouldHidePickupLocation)
                            _buildCustomTextField(
                              controller: _pickupController,
                              hintText: "Pickup location",
                              focusNode: _pickUpFocusNode,
                              isPickUp: true,
                            ),
                          const Gap(12),
                          _buildCustomTextField(
                            controller: _destinationController,
                            hintText: "To",
                            focusNode: _destinationFocusNode,
                            isPickUp: false,
                          ),
                          const Gap(16),
                          GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap:
                                _pickUpFocusNode.hasFocus
                                    ? _onNavigatePickupDestinationDraggleLocation
                                    : _onNavigateFinalDestinationDraggleLocation,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: const [
                                  Icon(
                                    Icons.location_on_outlined,
                                    color:
                                        Colors
                                            .blue, // This is a hardcoded color
                                  ),
                                  Gap(8),
                                  Text(
                                    "Choose on map",
                                    style: TextStyle(
                                      color:
                                          Colors
                                              .blue, // This is a hardcoded color
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          if (_showPlaces) ...[
                            const Gap(16),
                            if (placesState.maybeWhen(
                              loading: () => true,
                              orElse: () => false,
                            ))
                              _buildPredictionLoading()
                            else if (_places.isNotEmpty)
                              _buildPlacePredictions(),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Padding _buildPredictionLoading() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        children: List.generate(
          3,
          (index) => const Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: ShimmerWidget.rectangular(height: 40),
          ),
        ),
      ),
    );
  }

  ListView _buildPlacePredictions() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _places.length,
      itemBuilder: (context, index) {
        final place = _places[index];
        return InkWell(
          onTap: () {
            setState(() {
              if (_pickUpFocusNode.hasFocus) {
                _pickupController.text = place.description;
                sl<PassengerRouteBloc>().add(
                  PassengerRouteEvent.updatePickUpLocationWithPlaceId(
                    place.placeId,
                  ),
                );
                _pickUpFocusNode.unfocus();
                context.pop();
                return;
              } else if (_destinationFocusNode.hasFocus) {
                _destinationController.text = place.description;
                sl<PassengerRouteBloc>().add(
                  PassengerRouteEvent.addOrUpdateDropoffLocationWithPlaceId(
                    placeId: place.placeId,
                    index:
                        widget.shouldHidePickupLocation
                            ? null
                            : _getLastDestinationLocationIndex,
                  ),
                );
                _destinationFocusNode.unfocus();
                context.pop();
                return;
              }
              _showPlaces = false;
              _places = [];
            });
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(place.description),
          ),
        );
      },
    );
  }

  void _onNavigatePickupDestinationDraggleLocation() {
    context.pushNamed(
      AppRoutesName.draggableLocation,
      extra: {"type": LocationPickingType.pickup()},
    );
  }

  void _onNavigateFinalDestinationDraggleLocation() {
    context.pushNamed(
      AppRoutesName.draggableLocation,
      extra: {
        "type": LocationPickingType.destination(
          widget.shouldHidePickupLocation
              ? null
              : _getLastDestinationLocationIndex,
        ),
      },
    );
  }

  Widget _buildCustomTextField({
    required TextEditingController controller,
    required String hintText,
    required FocusNode focusNode,
    required bool isPickUp,
  }) {
    final bool hasFocus = focusNode.hasFocus;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 250),
      child: TextField(
        controller: controller,
        style: TextStyle(
          color: T.c(context).onSurfaceVariant,
        ), // FIX: Add (context)

        focusNode: focusNode,
        maxLines: 1,
        scrollPhysics: const NeverScrollableScrollPhysics(),
        onChanged: (value) {
          if (value.isEmpty) {
            setState(() {
              _showPlaces = false;
              _places = [];
            });
            return;
          }
          if (value == _initialPickUpLocation ||
              value == _initialDestinationLocation) {
            return;
          }

          if (focusNode == _pickUpFocusNode) {
            _showPlaces = value.isNotEmpty && hasFocus;
            if (value != _lastPickupSearchText) {
              _debounceTextChange(value, isPickup: true);
              _lastPickupSearchText = value;
            }
          } else if (focusNode == _destinationFocusNode) {
            _showPlaces = value.isNotEmpty && hasFocus;
            if (value != _lastDestinationSearchText) {
              _debounceTextChange(value, isPickup: false);
              _lastDestinationSearchText = value;
            }
          }
        },
        decoration: InputDecoration(
          hintText: hintText,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: AppColors.borderColor,
              width: 2,
            ), // AppColors is a hardcoded color
          ),
          filled: true,
          fillColor: T.c(context).surfaceContainerHighest, // FIX: Add (context)

          prefixIcon: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder:
                (child, anim) => ScaleTransition(scale: anim, child: child),
            child:
                !hasFocus
                    ? Icon(
                      Icons.radio_button_checked,
                      key: const ValueKey('focusedIcon'),
                      color:
                          isPickUp
                              ? AppColors
                                  .pickUpLocationColor // AppColors is a hardcoded color
                              : AppColors
                                  .destinationLocationColor, // AppColors is a hardcoded color
                    )
                    : const Icon(Icons.search, key: ValueKey('defaultIcon')),
          ),
          suffixIcon:
              hasFocus && controller.text.isNotEmpty
                  ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      controller.clear();
                      if (focusNode == _pickUpFocusNode) {
                        _lastPickupSearchText = null;
                        // Optionally clear Pickup location from bloc here if needed
                      } else if (focusNode == _destinationFocusNode) {
                        _lastDestinationSearchText = null;
                        // Optionally remove destination location from bloc here if needed
                      }
                      setState(() {
                        _showPlaces = false;
                        _places = [];
                      });
                    },
                  )
                  : null,
        ),
      ),
    );
  }
}
