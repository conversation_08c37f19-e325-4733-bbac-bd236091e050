// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:flutter/services.dart';

import 'package:safari_yatri/common/blocs/get_vehicle_type/get_vehicle_type_bloc.dart';
import 'package:safari_yatri/common/blocs/selected_vehicle_type/selected_vehicle_type_cubit.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/models/get_vehile_type.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_colors.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/format_distance.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/booking/blocs/add_to_cart_booking/add_to_cart_booking_bloc.dart';
import 'package:safari_yatri/features/booking/models/booking_add_to_cart.dart';

import '../widgets/time_picking_widget.dart';

class FareOfferPage extends StatefulWidget {
  const FareOfferPage({super.key});

  @override
  State<FareOfferPage> createState() => _FareOfferPageState();
}

class _FareOfferPageState extends State<FareOfferPage> {
  final _formKey = GlobalKey<FormState>();
  final _fareController = TextEditingController();
  final double _initialPrefixPadding = 120;

  double prefixIconPadding = 120;
  DateTime? _selectedTime;
  bool hasFareError = false;
  bool _isPressedDoneButton = false;

  BookingAddToCartModel? bookingCart;
  GetVehicleType? selectedVehicleType;

  @override
  void initState() {
    super.initState();
    sl<GetVehicleTypeBloc>().add(const GetVehicleTypeEvent.get());
  }

  @override
  void dispose() {
    _fareController.dispose();
    super.dispose();
  }

  Future<void> _showMaterialTimePicker() async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder:
          (_) => ScheduleTripBottomSheet(
            onScheduleConfirmed: (pickedDateTime) {
              setState(() => _selectedTime = pickedDateTime);
              sl<AddToCartBookingBloc>().add(
                AddToCartBookingEvent.scheduleTripDateTime(
                  scheduleDataTime: _selectedTime!,
                ),
              );
              Navigator.of(context).pop();
            },
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return BlocBuilder<GetVehicleTypeBloc, GetVehicleTypeState>(
      builder: (context, vehicleTypeState) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          vehicleTypeState.maybeWhen(
            loaded: (vehicleTypes) {
              if (vehicleTypes.isNotEmpty) {
                sl<SelectedVehicleTypeCubit>().selectedVehicle(
                  vehicleTypes.first,
                );
              }
            },
            orElse: () {},
          );
        });

        return BlocBuilder<SelectedVehicleTypeCubit, SelectedVehicleTypeState>(
          builder: (context, selectedState) {
            selectedVehicleType = selectedState.vehicleType;
            return Padding(
              padding: EdgeInsets.only(bottom: bottomInset),
              child: Padding(
                padding: const EdgeInsets.all(AppStyles.space12),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildTopBar(isDark),
                      const Gap(AppStyles.space12),
                      _buildFareSummarySection(isDark),
                      const Gap(AppStyles.space12),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppStyles.space48,
                        ),
                        child: _buildFareTextField(),
                      ),
                      const Gap(AppStyles.space16),
                      _buildScheduleTimerPicker(),
                      const Gap(AppStyles.space20),
                      _buildDoneButton(),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildTopBar(bool isDark) {
    return Row(
      children: [
        const Expanded(child: SizedBox()),
        Expanded(
          flex: 2,
          child: Text(
            L.t.fareOfferScreenTitle,
            style: T
                .t(context)
                .headlineMedium
                ?.copyWith(
                  color: T
                      .c(context)
                      .onSurfaceVariant
                      .withAlpha(isDark ? 179 : 153),
                ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          child: Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                height: 30,
                width: 30,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: T.c(context).surfaceContainer,
                ),
                child: const Icon(Icons.close, size: 18),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFareSummarySection(bool isDark) {
    return BlocBuilder<AddToCartBookingBloc, AddToCartBookingState>(
      builder: (context, state) {
        bookingCart = state.whenOrNull(loaded: (data) => data.data);
        if (bookingCart == null) return const SizedBox();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFareRow(
              L.t.fareOfferScreenSuggestedFare,
              bookingCart!.systemFareAmount.toString(),
              isDark,
            ),
            _buildFareRow(
              L.t.fareOfferScreenDistance,
              formatDistance(bookingCart!.totalDistanceInMeter),
              isDark,
            ),
          ],
        );
      },
    );
  }

  Widget _buildFareRow(String label, String value, bool isDark) {
    return Row(
      children: [
        Text(
          label,
          style: T
              .t(context)
              .titleMedium
              ?.copyWith(
                color: T
                    .c(context)
                    .onSurfaceVariant
                    .withAlpha(isDark ? 179 : 153),
              ),
        ),
        const Gap(AppStyles.space8),
        Text(
          value,
          style: T
              .t(context)
              .titleMedium
              ?.copyWith(color: AppColors.brandGreen),
        ),
      ],
    );
  }

  Widget _buildScheduleTimerPicker() {
    return GestureDetector(
      onTap: _showMaterialTimePicker,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        decoration: BoxDecoration(
          color: T.c(context).surfaceContainer,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _selectedTime == null
                  ? L.t.fareOfferScreenScheduleTime
                  : DateFormat('EEE, MMM d • hh:mm a').format(_selectedTime!),
              style: T
                  .t(context)
                  .titleMedium
                  ?.copyWith(
                    color: T
                        .c(context)
                        .onSurfaceVariant
                        .withAlpha(T.isDark(context) ? 179 : 153),
                  ),
            ),
            const Icon(Icons.access_time, size: 20, color: Colors.teal),
          ],
        ),
      ),
    );
  }

  Widget _buildFareTextField() {
    final negotiationBottomValue =
        selectedVehicleType?.negotiationBottomValue ?? 20.0;

    return TextFormField(
      controller: _fareController,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      onChanged: (value) {
        setState(() {
          prefixIconPadding = _initialPrefixPadding - (value.length * 12.5);
          if (value.isEmpty) prefixIconPadding = _initialPrefixPadding;
          if (prefixIconPadding < 0) prefixIconPadding = 0;
          hasFareError = !(_formKey.currentState?.validate() ?? true);
        });
      },
      keyboardType: TextInputType.number,
      style: TextStyle(
        fontSize: 40,
        fontWeight: FontWeight.bold,
        color: hasFareError ? Colors.red : T.c(context).primary,
      ),
      maxLength: 6,
      decoration: InputDecoration(
        counterText: '',
        prefixIconConstraints: const BoxConstraints(),
        prefixIcon: Padding(
          padding: EdgeInsetsDirectional.only(start: prefixIconPadding),
          child: Padding(
            padding: const EdgeInsets.only(right: 10),
            child: Text(
              'रु',
              style: TextStyle(
                fontSize: 40,
                color: hasFareError ? Colors.red : Colors.grey,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        border: const UnderlineInputBorder(),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(
            color: hasFareError ? Colors.red : Colors.grey,
          ),
        ),
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(
            color: hasFareError ? Colors.red : Colors.grey,
          ),
        ),
        errorStyle: const TextStyle(color: Colors.redAccent, fontSize: 14),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return L.t.fareOfferScreenEnterFareError;
        }
        final fare = int.tryParse(value);
        if (fare == null || fare < 0) {
          return L.t.fareOfferScreenInvalidFareError;
        }
        if (fare < negotiationBottomValue) {
          return L.t.fareOfferScreenMinimumFareError(negotiationBottomValue);
        }
        final system3x = bookingCart!.systemFareAmount * 3;
        if (fare > system3x) {
          return "Maximum fare is $system3x";
        }
        return null;
      },
    );
  }

  Widget _buildDoneButton() {
    return BlocListener<AddToCartBookingBloc, AddToCartBookingState>(
      listener: (context, state) {
        if (!_isPressedDoneButton) return;
        state.whenOrNull(
          loading: () => AppLoadingDialog.show(context),
          loaded: (data) {
            AppLoadingDialog.hide(context);
            Navigator.of(context).pop();
            context.pushNamed(
              AppRoutesName.rideRequestPage,
              extra: data.data?.toMap(),
            );
          },
          failure: (failure) {
            _isPressedDoneButton = false;
            AppLoadingDialog.hide(context);
            CustomToast.showError(failure.message);
          },
        );
      },
      child: CustomButtonPrimary(
        title: L.t.fareOfferScreenDone,
        onPressed: () {
          if (_formKey.currentState?.validate() ?? false) {
            final offeredFare = double.tryParse(_fareController.text);
            if (offeredFare == null) {
              return CustomToast.showError(L.t.fareOfferScreenInvalidFareError);
            }

            _isPressedDoneButton = true;
            sl<AddToCartBookingBloc>().add(
              AddToCartBookingEvent.create(
                passengerOfferedFare: offeredFare,
                maxRiderSearchDistanceInMeter:
                    kInitialRiderSearchDiameterInMeter,
              ),
            );
          }
        },
      ),
    );
  }
}
