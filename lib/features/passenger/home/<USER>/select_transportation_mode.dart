import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_styles.dart';

class SelectTransportMode extends StatelessWidget {
  final String image;
  final String modeText;
  final String seat;
  const SelectTransportMode({
    super.key,
    required this.image,
    required this.modeText,
    required this.seat,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      width: 90,
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        // border: Border.all(),
        color: AppColors.darkPrimary,
        borderRadius: AppStyles.radiusMd,
      ),
      child: Column(
        // spacing: AppStyles.space8,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Image.asset(image, height: 30),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Text(
                modeText,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.darkOnSurface,
                ),
              ),
              Icon(LucideIcons.userRound600, size: 14, color: Colors.grey[300]),
              Text(
                seat,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[300],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
