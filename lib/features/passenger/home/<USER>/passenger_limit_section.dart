// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:go_router/go_router.dart';
// import 'package:safari_yatri/core/di/dependency_injection.dart';
// import 'package:safari_yatri/core/theme/app_styles.dart';
// import 'package:safari_yatri/core/widget/custom_button.dart';
// import 'package:safari_yatri/features/booking/blocs/add_to_cart_booking/add_to_cart_booking_bloc.dart';

// import '../../../../core/widget/custom_switch.dart';

// class PassengerLimitSelection extends StatefulWidget {
//   const PassengerLimitSelection({super.key});

//   @override
//   State<PassengerLimitSelection> createState() =>
//       _PassengerLimitSelectionState();
// }

// class _PassengerLimitSelectionState extends State<PassengerLimitSelection> {
//   double prefixIconPadding = 120;

//   // bool moreThanFourPassengers = true;
//   bool shareBookingMode = true;
//   bool isOptionApplied = false;

//   @override
//   Widget build(BuildContext context) {
//     final bottomInset = MediaQuery.of(context).viewInsets.bottom;

//     return Padding(
//       padding: EdgeInsets.only(bottom: bottomInset),
//       child: Padding(
//         padding: const EdgeInsets.all(AppStyles.space12),

//         child: Column(
//           spacing: AppStyles.space12,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Container(
//               padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
//               decoration: const BoxDecoration(
//                 border: Border(
//                   bottom: BorderSide(color: Color(0xFFE5E5E5), width: 1),
//                 ),
//               ),
//               child: Row(
//                 children: [
//                   const Expanded(
//                     child: Text(
//                       'Options',
//                       textAlign: TextAlign.center,
//                       style: TextStyle(
//                         fontSize: 18,
//                         fontWeight: FontWeight.w600,
//                         color: Colors.black,
//                       ),
//                     ),
//                   ),
//                   GestureDetector(
//                     onTap: () {
//                       context.pop();
//                     },
//                     child: const Icon(
//                       Icons.close,
//                       size: 24,
//                       color: Colors.black54,
//                     ),
//                   ),
//                 ],
//               ),
//             ),

//             // Options List
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
//               child: Column(
//                 children: [
//                   BlocBuilder<AddToCartBookingBloc, AddToCartBookingState>(
//                     builder: (context, state) {
//                       state.whenOrNull(
//                         loaded: (data) {
//                           shareBookingMode =
//                               data.data?.isSharedBookingMode ?? false;
//                         },
//                       );
//                       return _buildOptionRow(
//                         title: 'Share Booking Mode',
//                         value: shareBookingMode,
//                         onChanged: (value) {
//                           shareBookingMode = value;
//                           sl<AddToCartBookingBloc>().add(
//                             AddToCartBookingEvent.shareRidingMode(
//                               isShareRidingMode: shareBookingMode,
//                             ),
//                           );
//                         },
//                       );
//                     },
//                   ),
//                 ],
//               ),
//             ),

//             CustomButtonPrimary(
//               title: isOptionApplied ? 'Apply' : 'Done',
//               onPressed: () {
//                 context.pop();
//               },
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// Widget _buildOptionRow({
//   required String title,
//   required bool value,
//   required Function(bool) onChanged,
// }) {
//   return Row(
//     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//     children: [
//       Text(
//         title,
//         style: const TextStyle(
//           fontSize: 16,
//           fontWeight: FontWeight.w500,
//           color: Colors.black,
//         ),
//       ),
//       CustomSwitch(value: value, onChanged: onChanged),
//     ],
//   );
// }
