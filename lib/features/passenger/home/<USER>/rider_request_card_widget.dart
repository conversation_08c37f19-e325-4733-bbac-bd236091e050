// // import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:safari_yatri/features/booking/models/accepting_riders_model.dart';

import '../../../../core/widget/custom_button.dart';

class DriverCard extends StatelessWidget {
  final String name;
  final String? image;
  final double rating;
  final int rides;
  final String vehicle;
  final String distanceTime;
  final double fare;
  final bool isYourFare;
  final VoidCallback onAccept;
  final VoidCallback onDecline;

  const DriverCard({
    super.key,
    required this.name,
    required this.rating,
    required this.image,
    required this.rides,
    required this.vehicle,
    required this.distanceTime,
    required this.fare,
    this.isYourFare = false,
    required this.onAccept,
    required this.onDecline,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 3,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isYourFare)
              const Padding(
                padding: EdgeInsets.only(bottom: 8.0),
                child: Text(
                  "Your fare",
                  style: TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            Row(
              children: [
                CircleAvatar(radius: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Row(
                        children: [
                          const Icon(
                            Icons.star,
                            size: 14,
                            color: Colors.orange,
                          ),
                          const SizedBox(width: 4),
                          Text('$rating ($rides rides)'),
                        ],
                      ),
                      Text(vehicle),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // Text(distanceTime.split('•')[0].trim()),
                    // Text(distanceTime.split('•')[1].trim()),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'NPR${fare.toInt()}',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: CustomButtonOutline(
                    title: 'Decline',
                    onPressed: onDecline,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomButtonPrimary(
                    title: 'Accept',
                    onPressed: onAccept,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class AnimatedDriverCard extends StatefulWidget {
  final GetMyAcceptingRidersModel driver;
  final VoidCallback onAccept;
  final VoidCallback onDecline;
  
  const AnimatedDriverCard({
    super.key,
    required this.driver,
    required this.onAccept,
    required this.onDecline,
  });

  @override
  State<AnimatedDriverCard> createState() => _AnimatedDriverCardState();
}

class _AnimatedDriverCardState extends State<AnimatedDriverCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;

  // @override
  // void initState() {
  //   super.initState();
  //   _controller = AnimationController(
  //     vsync: this,
  //     duration: const Duration(milliseconds: 500),
  //   );
  //   _offsetAnimation = Tween<Offset>(
  //     begin: const Offset(0.0, 0.5),
  //     end: Offset.zero,
  //   ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));
  //   _controller.forward();
  // }

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _offsetAnimation = Tween<Offset>(
      // Start offscreen left (-1.0, 0.0) and move to on-screen (0.0, 0.0)
      begin: const Offset(-1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));
    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _offsetAnimation,
      child: DriverCard(
        name: widget.driver.riderName,
        rating: 4.8,
        rides: 999,
        image: null,
        vehicle: widget.driver.vehicleNo,
        distanceTime: "5M",
        fare: widget.driver.fareAmount,
        isYourFare: true,
        onAccept: widget.onAccept,
        onDecline: widget.onDecline,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
