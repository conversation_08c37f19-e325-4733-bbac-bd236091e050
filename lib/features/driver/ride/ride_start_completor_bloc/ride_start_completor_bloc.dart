import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'ride_start_completor_event.dart';
part 'ride_start_completor_state.dart';
part 'ride_start_completor_bloc.freezed.dart';

class RideStartCompletorBloc
    extends Bloc<RideStartCompletorEvent, RideStartCompletorState> {
  final BookingRepository _bookingRepository;

  RideStartCompletorBloc({required BookingRepository repo})
    : _bookingRepository = repo,
      super(
        RideStartCompletorState(
          buttonState: RideStartCompletorButtonState.imHere,
          status: RideStartCompletorStatus.initial,
        ),
      ) {
    on<_OriginToPickup>(_handleOriginToPickup);
    on<_StartRide>(_handleStartRide);
    on<_CompleteRide>(_handleCompleteRide);
  }

  Future<void> _handleOriginToPickup(
    _OriginToPickup event,
    Emitter<RideStartCompletorState> emit,
  ) async {
    await Future.delayed(const Duration(milliseconds: 300));
    emit(
      RideStartCompletorState(
        buttonState: RideStartCompletorButtonState.start,
        status: RideStartCompletorStatus.success,
      ),
    );
  }

  Future<void> _handleStartRide(
    _StartRide event,
    Emitter<RideStartCompletorState> emit,
  ) async {
    emit(
      RideStartCompletorState(
        buttonState: state.buttonState,
        status: RideStartCompletorStatus.loading,
      ),
    );

    final result = await _bookingRepository.serviceStart(
      bookingId: event.bookingId,
      confirmationCode: event.confirmationCode,
    );

    result.fold(
      (failure) {
        emit(
          RideStartCompletorState(
            buttonState: state.buttonState,
            status: RideStartCompletorStatus.failure,
            failure: failure,
          ),
        );
      },
      (_) {
        emit(
          RideStartCompletorState(
            buttonState: RideStartCompletorButtonState.complete,
            status: RideStartCompletorStatus.success,
          ),
        );
      },
    );
  }

  Future<void> _handleCompleteRide(
    _CompleteRide event,
    Emitter<RideStartCompletorState> emit,
  ) async {
    emit(
      RideStartCompletorState(
        buttonState: state.buttonState,
        status: RideStartCompletorStatus.loading,
      ),
    );

    final result = await _bookingRepository.serviceComplete(event.bookingId);

    result.fold(
      (failure) {
        emit(
          RideStartCompletorState(
            buttonState: state.buttonState,
            status: RideStartCompletorStatus.failure,
            failure: failure,
          ),
        );
      },
      (_) {
        emit(
          RideStartCompletorState(
            buttonState: RideStartCompletorButtonState.completed,
            status: RideStartCompletorStatus.success,
          ),
        );
      },
    );
  }
}
