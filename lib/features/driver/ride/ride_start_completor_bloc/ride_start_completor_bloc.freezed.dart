// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ride_start_completor_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RideStartCompletorEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is RideStartCompletorEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'RideStartCompletorEvent()';
  }
}

/// Adds pattern-matching-related methods to [RideStartCompletorEvent].
extension RideStartCompletorEventPatterns on RideStartCompletorEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_OriginToPickup value)? originToPickup,
    TResult Function(_StartRide value)? startRide,
    TResult Function(_CompleteRide value)? completeRide,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OriginToPickup() when originToPickup != null:
        return originToPickup(_that);
      case _StartRide() when startRide != null:
        return startRide(_that);
      case _CompleteRide() when completeRide != null:
        return completeRide(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_OriginToPickup value) originToPickup,
    required TResult Function(_StartRide value) startRide,
    required TResult Function(_CompleteRide value) completeRide,
  }) {
    final _that = this;
    switch (_that) {
      case _OriginToPickup():
        return originToPickup(_that);
      case _StartRide():
        return startRide(_that);
      case _CompleteRide():
        return completeRide(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_OriginToPickup value)? originToPickup,
    TResult? Function(_StartRide value)? startRide,
    TResult? Function(_CompleteRide value)? completeRide,
  }) {
    final _that = this;
    switch (_that) {
      case _OriginToPickup() when originToPickup != null:
        return originToPickup(_that);
      case _StartRide() when startRide != null:
        return startRide(_that);
      case _CompleteRide() when completeRide != null:
        return completeRide(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? originToPickup,
    TResult Function(int bookingId, String confirmationCode)? startRide,
    TResult Function(int bookingId)? completeRide,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _OriginToPickup() when originToPickup != null:
        return originToPickup();
      case _StartRide() when startRide != null:
        return startRide(_that.bookingId, _that.confirmationCode);
      case _CompleteRide() when completeRide != null:
        return completeRide(_that.bookingId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() originToPickup,
    required TResult Function(int bookingId, String confirmationCode) startRide,
    required TResult Function(int bookingId) completeRide,
  }) {
    final _that = this;
    switch (_that) {
      case _OriginToPickup():
        return originToPickup();
      case _StartRide():
        return startRide(_that.bookingId, _that.confirmationCode);
      case _CompleteRide():
        return completeRide(_that.bookingId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? originToPickup,
    TResult? Function(int bookingId, String confirmationCode)? startRide,
    TResult? Function(int bookingId)? completeRide,
  }) {
    final _that = this;
    switch (_that) {
      case _OriginToPickup() when originToPickup != null:
        return originToPickup();
      case _StartRide() when startRide != null:
        return startRide(_that.bookingId, _that.confirmationCode);
      case _CompleteRide() when completeRide != null:
        return completeRide(_that.bookingId);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _OriginToPickup implements RideStartCompletorEvent {
  const _OriginToPickup();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _OriginToPickup);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'RideStartCompletorEvent.originToPickup()';
  }
}

/// @nodoc

class _StartRide implements RideStartCompletorEvent {
  const _StartRide({required this.bookingId, required this.confirmationCode});

  final int bookingId;
  final String confirmationCode;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _StartRide &&
            (identical(other.bookingId, bookingId) ||
                other.bookingId == bookingId) &&
            (identical(other.confirmationCode, confirmationCode) ||
                other.confirmationCode == confirmationCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, bookingId, confirmationCode);

  @override
  String toString() {
    return 'RideStartCompletorEvent.startRide(bookingId: $bookingId, confirmationCode: $confirmationCode)';
  }
}

/// @nodoc

class _CompleteRide implements RideStartCompletorEvent {
  const _CompleteRide(this.bookingId);

  final int bookingId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CompleteRide &&
            (identical(other.bookingId, bookingId) ||
                other.bookingId == bookingId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, bookingId);

  @override
  String toString() {
    return 'RideStartCompletorEvent.completeRide(bookingId: $bookingId)';
  }
}
