// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'ride_start_completor_bloc.dart';

enum RideStartCompletorButtonState { imHere, start, complete, completed }

enum RideStartCompletorStatus { initial, loading, success, failure }

class RideStartCompletorState {
  final RideStartCompletorButtonState buttonState;
  final RideStartCompletorStatus status;
  final Failure? failure;
  RideStartCompletorState({
    required this.buttonState,
    required this.status,
    this.failure,
  });
}
