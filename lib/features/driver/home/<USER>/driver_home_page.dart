import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/common/widgets/location_permission_widget.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/booking/blocs/get_new_passenger/get_new_passenger_bloc.dart';
import 'package:safari_yatri/features/booking/models/new_passenger_view_model.dart';
import 'package:safari_yatri/features/driver/home/<USER>/driver_waiting_for_passenger_acceptance_progress_dialog.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';
import 'package:safari_yatri/features/location/blocs/location/location_bloc.dart';
import 'package:safari_yatri/features/location/blocs/location_permission/location_permission_bloc.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../../../common/blocs/ride_notify_alert/ride_notify_alert_bloc.dart';
import '../../../drawer/pages/driver_drawer_page.dart';
import '../../widget/status_toogle_button.dart';
import '../widgets/ride_request_list_widget.dart';

class DriverHomePage extends StatefulWidget {
  const DriverHomePage({super.key});
  @override
  State<DriverHomePage> createState() => _DriverHomePageState();
}

class _DriverHomePageState extends State<DriverHomePage> {
  List<NewPassengerModel> _lastPassengers = [];
  LatLng? _currentLocation;
  late CurrentLocationBloc _currentLocationBloc;
  GoogleMapController? _mapController;
  @override
  void initState() {
    super.initState();
    sl<GetNewPassengerBloc>().add(GetNewPassengerEvent.resume());

    sl<LocationPermissionBloc>().add(
      const LocationPermissionEvent.checkPermissionStatus(),
    );
    sl<LocationBloc>().add(const LocationEvent.startTracking());
    _currentLocationBloc = sl<CurrentLocationBloc>();
    _currentLocationBloc.add(const CurrentLocationEvent.getCurrentLocation());
  }

  @override
  void dispose() {
    super.dispose();
    sl<GetNewPassengerBloc>().add(GetNewPassengerEvent.stop());
  }

  @override
  Widget build(BuildContext context) {
    return LocationPermissionListenerWidget(
      child: Scaffold(
        appBar: CustomAppBar(
          title: StatusToggleButton(),
          centerTitle: true,
          actions: [
            IconButton(onPressed: () {}, icon: Icon(LucideIcons.settings)),
          ],
          leading: Builder(
            builder: (context) {
              return IconButton(
                onPressed: () {
                  Scaffold.of(context).openDrawer();
                },
                icon: Icon(LucideIcons.menu),
              );
            },
          ),
        ),

        drawer: DriverDrawer(),
        body: BlocListener<GetNewPassengerBloc, GetNewPassengerState>(
          child: _currentLocationBlocListener(),
          listener: (context, state) {
            state.whenOrNull(
              loaded: (newPassengers) {
                final newOnes = newPassengers.where(
                  (newP) =>
                      !_lastPassengers.any(
                        (oldP) =>
                            oldP.passengerId == newP.passengerId &&
                            oldP.bookingStartTime == newP.bookingStartTime,
                      ),
                );

                if (newOnes.isNotEmpty) {
                  sl<RideNotifyAlertBloc>().add(
                    const RideNotifyAlertEvent.rideNotifyAlert(),
                  );
                }

                _lastPassengers = newPassengers;
              },
            );
          },
        ),
      ),
    );
  }

  BlocConsumer<CurrentLocationBloc, CurrentLocationState>
  _currentLocationBlocListener() {
    return BlocConsumer<CurrentLocationBloc, CurrentLocationState>(
      bloc: _currentLocationBloc,
      listener: (context, state) {
        state.maybeWhen(
          loaded: (data) async {
            _currentLocation = LatLng(data.latitude, data.longitude);
            await _mapController?.animateCamera(
              CameraUpdate.newCameraPosition(
                CameraPosition(
                  target: _currentLocation!,
                  zoom: kMapInitialZoom,
                ),
              ),
            );
          },
          orElse: () {
            _currentLocation = LatLng(27.7172, 85.3240);
          },
        );
      },

      builder: (context, state) {
        return state.maybeWhen(
          failure:
              (f) => ErrorWidgetWithRetry(
                failure: f,
                onRetry:
                    () => _currentLocationBloc.add(
                      const CurrentLocationEvent.getCurrentLocation(),
                    ),
              ),
          orElse: () => Center(child: CircularProgressIndicator()),
          loaded:
              (data) => Stack(
                children: [_buildGoogleMap(), _buildDraggableScrollableSheet()],
              ),
        );
      },
    );
  }

  DraggableScrollableSheet _buildDraggableScrollableSheet() {
    return DraggableScrollableSheet(
      minChildSize: 0.2,
      maxChildSize: 0.95,
      initialChildSize: 0.2,

      builder: (context, controller) {
        return Container(
          decoration: BoxDecoration(
            color: ColorScheme.of(context).surface,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: BlocBuilder<GetNewPassengerBloc, GetNewPassengerState>(
            builder: (context, state) {
              return Column(
                children: [
                  Center(
                    child: Container(
                      margin: const EdgeInsets.only(top: 12, bottom: 4),
                      height: 4,
                      width: 40,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),

                  Expanded(child: _buildBody(state, controller)),
                ],
              );
            },
          ),
        );
      },
    );
  }

  CustomGoogleMap _buildGoogleMap() {
    return CustomGoogleMap(
      onMapCreated: (controller) {
        _mapController = controller;
      },
      myLocationEnabled: true,
      initialCameraPosition: CameraPosition(
        target: _currentLocation ?? LatLng(27.7172, 85.3240),
        zoom: kMapInitialZoom,
      ),
    );
  }

  Widget _buildBody(GetNewPassengerState state, ScrollController controller) {
    return state.maybeWhen(
      failure:
          (failure) => SingleChildScrollView(
            controller: controller,
            child: ErrorWidgetWithRetry(
              failure: failure,
              onRetry:
                  () =>
                      sl<GetNewPassengerBloc>().add(GetNewPassengerEvent.get()),
            ),
          ),
      loaded: (newPassengers) {
        return CustomScrollView(
          controller: controller,
          slivers: [
            newPassengers.isEmpty
                ? SliverToBoxAdapter(
                  child: Center(
                    child: Text(
                      "No Passengers",
                      style: TextTheme.of(context).bodyLarge,
                    ),
                  ),
                )
                : SliverToBoxAdapter(),

            SliverList(
              delegate: SliverChildBuilderDelegate((context, index) {
                final newPassenger = newPassengers[index];

                return RideRequestItem(
                  newPassenger: newPassenger,
                  onTap: () async => _onPressedRideRequestDetail(newPassenger),
                );
              }, childCount: newPassengers.length),
            ),
          ],
        );
      },

      orElse: () => SingleChildScrollView(controller: controller),
    );
  }

  Future<void> _onPressedRideRequestDetail(
    NewPassengerModel newPassenger,
  ) async {
    final newPassengers = await context.pushNamed(
      AppRoutesName.rideRequestDetailsPage,
      extra: newPassenger,
    );
    if (newPassengers == 'showLoadingDilogRiderRequest') {
      if (!mounted) return;
      await showRiderWaitingForPassengersAcceptanceDialog(
        context: context,
        passengerModel: newPassenger,
      );
    }

    ///after going in ride request detail page it will again start fetching
    sl<GetNewPassengerBloc>().add(GetNewPassengerEvent.resume());
  }
}

Future<bool?> showRiderWaitingForPassengersAcceptanceDialog({
  required BuildContext context,
  required NewPassengerModel passengerModel,
}) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) {
      return RiderWaitingForPassengerAcceptDialogPage(
        passengerModel: passengerModel,
      );
    },
  );
}
