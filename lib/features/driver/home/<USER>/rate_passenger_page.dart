import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import '../../../../core/widget/custom_appbar.dart';

class RatePassengerPage extends StatefulWidget {
  const RatePassengerPage({super.key});

  @override
  State<RatePassengerPage> createState() => _RatePassengerPageState();
}

class _RatePassengerPageState extends State<RatePassengerPage> {
  int _rating = 0;
  final TextEditingController _commentController = TextEditingController();

  void _submitRating() {
    // Submit logic here
    context.pushNamed(AppRoutesName.passengerHome);
  }

  Widget _buildStar(int index) {
    return GestureDetector(
      onTap: () => setState(() => _rating = index),
      child: Icon(
        _rating >= index ? Icons.star : Icons.star_border,
        color: Colors.orange,
        size: 36,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: Text('Rate Your Passenger'),
        actions: [
          IconButton(
            onPressed: () {},
            icon: Icon(Icons.report_gmailerrorred, color: Colors.red, size: 24),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'How was your experience with Ramesh Sharma?',

              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Card(
              margin: const EdgeInsets.all(0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.orange[100],
                  child: const Text(
                    'RS',
                    style: TextStyle(color: Colors.black),
                  ),
                ),
                title: const Text(
                  'Ramesh Sharma',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                subtitle: const Text(
                  'Thamel, Kathmandu → Tribhuvan Airport',
                  style: TextStyle(fontWeight: FontWeight.w500, fontSize: 15),
                ),
              ),
            ),
            const SizedBox(height: 24),
            const Center(
              child: Text(
                'Rate this passenger',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(5, (index) => _buildStar(index + 1)),
            ),
            const SizedBox(height: 8),
            const Center(child: Text('Tap to rate')),
            const SizedBox(height: 24),
            const Text(
              'Quick Feedback (Optional)',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            Wrap(
              spacing: 10,
              runSpacing: 10,
              children: [
                FeedbackChip(
                  label: 'Polite',
                  icon: Icons.thumb_up_alt_outlined,
                ),
                FeedbackChip(
                  label: 'Easy Pickup',
                  icon: Icons.check_circle_outline,
                ),
                FeedbackChip(
                  label: 'Issue',
                  icon: Icons.thumb_down_alt_outlined,
                ),
                FeedbackChip(label: 'On Time', icon: Icons.access_time),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _commentController,
              maxLines: 3,
              decoration: InputDecoration(
                hintText: 'Add any additional comments…',
                hintStyle: const TextStyle(color: Colors.grey),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 14,
                ),
                filled: true,
                fillColor: Colors.white,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.grey),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.grey),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: ColorScheme.of(context).primary,
                    width: 1.5,
                  ),
                ),
              ),
            ),
            SizedBox(height: 50),
            Row(
              spacing: 12,
              children: [
                Expanded(
                  child: CustomButtonPrimary(
                    title: 'Submit Rating',
                    onPressed: _submitRating,
                  ),
                ),

                Expanded(
                  child: CustomTextButton(
                    title: 'Skip',
                    onPressed: () {
                      context.goNamed(AppRoutesName.passengerHome);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class FeedbackChip extends StatefulWidget {
  final String label;
  final IconData icon;

  const FeedbackChip({super.key, required this.label, required this.icon});

  @override
  State<FeedbackChip> createState() => _FeedbackChipState();
}

class _FeedbackChipState extends State<FeedbackChip> {
  bool _isSelected = false;

  void _toggleSelected() {
    setState(() {
      _isSelected = !_isSelected;
    });
  }

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      onPressed: _toggleSelected,
      icon: Icon(
        widget.icon,
        size: 18,
        color: _isSelected ? Colors.white : Colors.green,
      ),
      label: Text(
        widget.label,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: _isSelected ? Colors.white : Colors.black87,
        ),
      ),
      style: OutlinedButton.styleFrom(
        side: const BorderSide(color: Colors.grey, width: 0.8),
        backgroundColor: _isSelected ? Colors.green : Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }
}
