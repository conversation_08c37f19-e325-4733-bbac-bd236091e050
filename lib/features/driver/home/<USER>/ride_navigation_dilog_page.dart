import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class RideNavigationCard extends StatelessWidget {
  const RideNavigationCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppStyles.space16),

      decoration: BoxDecoration(
        borderRadius: AppStyles.radiusMd,
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(1, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppStyles.space16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Status and Fare <PERSON>
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    "Going to Pickup",
                    style: TextStyle(fontSize: 12, color: Colors.blue),
                  ),
                ),
                const Text(
                  "NPR 450",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),

            // Driver Info
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.orange[100],
                  child: const Text(
                    "RS",
                    style: TextStyle(color: Colors.black),
                  ),
                ),
                const SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    Text(
                      "Ramesh Sharma",
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Row(
                      children: [
                        Icon(Icons.star, color: Colors.amber, size: 16),
                        SizedBox(width: 4),
                        Text("4.8"),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Location Info
            Row(
              children: const [
                Icon(Icons.location_on, color: Colors.green, size: 16),
                SizedBox(width: 5),
                Text("Thamel, Kathmandu"),
              ],
            ),
            const SizedBox(height: 5),
            Row(
              children: const [
                Icon(Icons.location_on_outlined, color: Colors.red, size: 16),
                SizedBox(width: 5),
                Text("Tribhuvan Airport"),
              ],
            ),
            const SizedBox(height: 16),

            // Navigate & Alert Buttons
            Row(
              children: [
                Expanded(
                  child: CustomButtonPrimary(
                    height: 40,
                    title: 'Navigate',
                    leadingIcon: const Icon(LucideIcons.navigation2),
                    onPressed: () {
                      context.pushNamed(AppRoutesName.rideCompletedScreen);
                    },
                  ),
                ),
                const SizedBox(width: 10),
                IconButton(
                  icon: const Icon(
                    Icons.warning_amber_rounded,
                    color: Colors.red,
                  ),
                  onPressed: () {
                    // Handle warning logic
                  },
                  style: IconButton.styleFrom(
                    side: const BorderSide(color: Colors.red),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// class RideNavigationDialog extends StatelessWidget {
//   const RideNavigationDialog({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       insetPadding: EdgeInsets.all(10),
//       shape: RoundedRectangleBorder(borderRadius: AppStyles.radiusMd),
//       child:
//       Container(
//         padding: const EdgeInsets.all(16),
//         width: MediaQuery.of(context).size.width * 0.9,
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             // Status and Fare Row
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Container(
//                   padding: const EdgeInsets.symmetric(
//                     horizontal: 8,
//                     vertical: 2,
//                   ),
//                   decoration: BoxDecoration(
//                     color: Colors.blue.shade100,
//                     borderRadius: BorderRadius.circular(12),
//                   ),
//                   child: const Text(
//                     "Going to Pickup  ",
//                     style: TextStyle(fontSize: 12, color: Colors.blue),
//                   ),
//                 ),
//                 Text(
//                   "NPR 450",
//                   style: TextStyle(
//                     fontWeight: FontWeight.bold,
//                     fontSize: 16,
//                     color: Colors.green,
//                   ),
//                 ),
//               ],
//             ),
//             SizedBox(height: 10),

//             // Driver Info
//             Row(
//               children: [
//                 CircleAvatar(
//                   radius: 20,
//                   backgroundColor: Colors.orange[100],
//                   child: Text("RS", style: TextStyle(color: Colors.black)),
//                 ),
//                 SizedBox(width: 10),
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       "Ramesh Sharma",
//                       style: TextStyle(fontWeight: FontWeight.bold),
//                     ),
//                     Row(
//                       children: [
//                         Icon(Icons.star, color: Colors.amber, size: 16),
//                         Text("4.8"),
//                       ],
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//             SizedBox(height: 16),

//             // Location Info
//             Row(
//               children: [
//                 Icon(Icons.location_on, color: Colors.green, size: 16),
//                 SizedBox(width: 5),
//                 Text("Thamel, Kathmandu"),
//               ],
//             ),
//             SizedBox(height: 5),
//             Row(
//               children: [
//                 Icon(Icons.location_on_outlined, color: Colors.red, size: 16),
//                 SizedBox(width: 5),
//                 Text("Tribhuvan Airport"),
//               ],
//             ),
//             SizedBox(height: 16),

//             // Navigate & Alert Buttons
//             Row(
//               children: [
//                 Expanded(
//                   child: CustomButtonPrimary(
//                     height: 40,
//                     title: 'Navigate',
//                     leadingIcon: Icon(LucideIcons.navigation2),
//                     onPressed: () {
//                       context.pop();
//                       context.pushNamed(AppRoutesName.rideCompletedScreen);
//                     },
//                   ),
//                 ),
//                 SizedBox(width: 10),
//                 IconButton(
//                   icon: Icon(Icons.warning_amber_rounded, color: Colors.red),
//                   onPressed: () {
//                     context.pop();
//                   },
//                   style: IconButton.styleFrom(
//                     side: BorderSide(color: Colors.red),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(8),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
