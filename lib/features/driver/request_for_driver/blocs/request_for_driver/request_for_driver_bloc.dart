

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:safari_yatri/core/services/app_cached_keys.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/driver/request_for_driver/models/request_for_driver_form.dart';
import 'package:safari_yatri/features/driver/request_for_driver/repositories/request_for_driver_repository.dart';
import 'package:safari_yatri/features/my_profile/model/my_profile_model.dart';

part 'request_for_driver_event.dart';
part 'request_for_driver_state.dart';
part 'request_for_driver_bloc.freezed.dart';

class RequestForDriverBloc
    extends Bloc<RequestForDriverEvent, RequestForDriverState> {
  final RequestForDriverRepository _requestForDriverRepository;
  final CoreLocalDataSource _coreLocalDataSource = CoreLocalDataSource();

  String? _pageName;
  DriverRegistrationRequest? _driverRequestForm;

  RequestForDriverBloc({required RequestForDriverRepository repo})
    : _requestForDriverRepository = repo,
      super(const BaseState.initial()) {
    on<_RequestForDriver>(_onRequestForDriver);
    on<requestForDriverProgressPage>(_onRequestForDriverProgressPage);
    on<_InitializeDataFromRemoteOnce>(_onInitializeDataFromRemoteOnce);
  }

  Future<void> _onRequestForDriver(
    _RequestForDriver event,
    Emitter<RequestForDriverState> emit,
  ) async {
    emit(const BaseState.loading());
    _driverRequestForm = _driverRequestForm?.copyWith(
      identityPhoto: event.identityPhoto,
    );
    
    // log(_driverRequestForm!.localToMap().toString());

    final result = await _requestForDriverRepository.requestForDriver(
      _driverRequestForm!,
    );

    result.fold(
      (failure) => emit(BaseState.failure(failure)),
      (data) => emit(
        BaseState.loaded((data: _driverRequestForm!, pageName: _pageName!)),
      ),
    );
  }

  Future<void> _onRequestForDriverProgressPage(
    requestForDriverProgressPage event,
    Emitter<RequestForDriverState> emit,
  ) async {
    _pageName = event.pageName;

    if (_driverRequestForm == null) {
      _driverRequestForm = event.driverRequestForm;
    } else {
      _driverRequestForm = _driverRequestForm?.copyWith(
        userName: event.driverRequestForm.userName,
        phoneNo: event.driverRequestForm.phoneNo,
        emailAddress: event.driverRequestForm.emailAddress,
        address: event.driverRequestForm.address,
        gender: event.driverRequestForm.gender,
        vehicleTypeId: event.driverRequestForm.vehicleTypeId,

        vehicleNo: event.driverRequestForm.vehicleNo,
        ownerName: event.driverRequestForm.ownerName,
        ownerPhone: event.driverRequestForm.ownerPhone,

        vehiclePhoto: event.driverRequestForm.vehiclePhoto,
        identityPhoto: event.driverRequestForm.identityPhoto,
        blueBookPhoto: event.driverRequestForm.blueBookPhoto,
        citizenCardFront: event.driverRequestForm.citizenCardFront,
        citizenCardBack: event.driverRequestForm.citizenCardBack,
      );
    }

    await _coreLocalDataSource.saveModel(
      AppCachedKeys.requestForDriverForm,
      _driverRequestForm!.localToMap(),
    );

    emit(BaseState.loaded((data: _driverRequestForm!, pageName: _pageName!)));
  }

  Future<void> _onInitializeDataFromRemoteOnce(
    _InitializeDataFromRemoteOnce event,
    Emitter<RequestForDriverState> emit,
  ) async {
    final userData = _coreLocalDataSource.getModel(
      AppCachedKeys.userProfileData,
      (json) => GetUserProfileDetailsModel.fromMap(json),
    );

    if (userData == null) return;

    final driverForm = _coreLocalDataSource.getModel(
      AppCachedKeys.requestForDriverForm,
      (json) => DriverRegistrationRequest.fromMap(json),
    );
    if (driverForm != null) {
      _driverRequestForm = driverForm;
      emit(
        BaseState.loaded((
          data: _driverRequestForm!,
          pageName: _pageName ?? '',
        )),
      );
      return;
    }

    _driverRequestForm = DriverRegistrationRequest(
      userName: userData.userName,
      gender: userData.gender,
      emailAddress: userData.emailAddress,
      phoneNo: userData.phoneNo,
      address: userData.address,
    );

    emit(
      BaseState.loaded((data: _driverRequestForm!, pageName: _pageName ?? '')),
    );
  }
}
