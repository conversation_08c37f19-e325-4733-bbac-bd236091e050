// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'request_for_driver_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RequestForDriverEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is RequestForDriverEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'RequestForDriverEvent()';
  }
}

/// @nodoc

class _RequestForDriver implements RequestForDriverEvent {
  const _RequestForDriver(this.identityPhoto);

  final XFile identityPhoto;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RequestForDriver &&
            (identical(other.identityPhoto, identityPhoto) ||
                other.identityPhoto == identityPhoto));
  }

  @override
  int get hashCode => Object.hash(runtimeType, identityPhoto);

  @override
  String toString() {
    return 'RequestForDriverEvent.requestForDriver(identityPhoto: $identityPhoto)';
  }
}

/// @nodoc

class requestForDriverProgressPage implements RequestForDriverEvent {
  const requestForDriverProgressPage({
    required this.pageName,
    required this.driverRequestForm,
  });

  final String pageName;
  final DriverRegistrationRequest driverRequestForm;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is requestForDriverProgressPage &&
            (identical(other.pageName, pageName) ||
                other.pageName == pageName) &&
            (identical(other.driverRequestForm, driverRequestForm) ||
                other.driverRequestForm == driverRequestForm));
  }

  @override
  int get hashCode => Object.hash(runtimeType, pageName, driverRequestForm);

  @override
  String toString() {
    return 'RequestForDriverEvent.requestForDriverProgressPage(pageName: $pageName, driverRequestForm: $driverRequestForm)';
  }
}

/// @nodoc

class _InitializeDataFromRemoteOnce implements RequestForDriverEvent {
  const _InitializeDataFromRemoteOnce();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InitializeDataFromRemoteOnce);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'RequestForDriverEvent.initializeDataFromRemoteOnce()';
  }
}
