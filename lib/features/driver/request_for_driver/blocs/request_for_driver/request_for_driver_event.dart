part of 'request_for_driver_bloc.dart';

@freezed
abstract class RequestForDriverEvent with _$RequestForDriverEvent {
  //yo chaii final request pathaune ho server maa
  const factory RequestForDriverEvent.requestForDriver(XFile identityPhoto) =
      _RequestForDriver;

  //just locally cache garna ko lagi ho yo chaii
  const factory RequestForDriverEvent.requestForDriverProgressPage({
    required String pageName,
    required DriverRegistrationRequest driverRequestForm,
  }) = requestForDriverProgressPage;

  const factory RequestForDriverEvent.initializeDataFromRemoteOnce() =
      _InitializeDataFromRemoteOnce;
}
