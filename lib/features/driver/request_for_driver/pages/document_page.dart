import 'dart:io'; // Import for File
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/driver/request_for_driver/blocs/request_for_driver/request_for_driver_bloc.dart';
import 'package:safari_yatri/features/driver/request_for_driver/models/request_for_driver_form.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/custom_pop_button.dart';

class DocumentPage extends StatefulWidget {
  const DocumentPage({super.key});

  @override
  State<DocumentPage> createState() => _DocumentPageState();
}

class _DocumentPageState extends State<DocumentPage> {
  XFile? _citizenshipFront;
  XFile? _citizenshipBack;

  bool _showCitizenshipFrontError = false;
  bool _showCitizenshipBackError = false;

  @override
  void initState() {
    super.initState();
    // Load existing document photos from the bloc
    final state = sl<RequestForDriverBloc>().state;
    state.whenOrNull(
      loaded: (data) {
        _citizenshipFront = data.data.citizenCardFront;
        _citizenshipBack = data.data.citizenCardBack;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<RequestForDriverBloc, RequestForDriverState>(
        builder: (context, state) {
          return SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal: AppStyles.space12,
              vertical: AppStyles.space40,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomBackButton(),
                SizedBox(height: AppStyles.space8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: Column(
                    children: [
                      Text(
                        'Upload your Documents',
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.displayLarge,
                      ),
                    ],
                  ),
                ),
                SizedBox(height: AppStyles.space12),

                // Citizenship Front Document Picker
                _buildImagePicker(
                  label: 'Select Citizenship Front',
                  image: _citizenshipFront,
                  onTap: _pickCitizenshipFront,
                  placeholderImage:
                      'placeholder.png', // You need a placeholder image path here
                ),
                if (_showCitizenshipFrontError)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0, left: 8.0),
                    child: Text(
                      'Please select the front of your citizenship.',
                      style: TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
                SizedBox(height: AppStyles.space12),

                // Citizenship Back Document Picker
                _buildImagePicker(
                  label: 'Select Citizenship Back',
                  image: _citizenshipBack,
                  onTap: _pickCitizenshipBack,
                  placeholderImage:
                      'placeholder.png', // You need a placeholder image path here
                ),
                if (_showCitizenshipBackError)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0, left: 8.0),
                    child: Text(
                      'Please select the back of your citizenship.',
                      style: TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
                SizedBox(height: AppStyles.space12 * 2),

                CustomButtonPrimary(title: 'Next', onPressed: _onPressed),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildImagePicker({
    String? label,
    required XFile? image,
    required VoidCallback onTap,
    String? placeholderImage,
  }) {
    return Material(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10),
        child: Container(
          height: 150,
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(10),
          ),
          child:
              image != null
                  ? ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: Image.file(File(image.path), fit: BoxFit.cover),
                  )
                  : Center(child: Text(label ?? '')), // Fallback if no image
        ),
      ),
    );
  }

  Future<void> _pickCitizenshipFront() async {
    final pickedFile = await _pickImage();
    if (pickedFile != null) {
      setState(() {
        _citizenshipFront = pickedFile;
        _showCitizenshipFrontError = false;
      });
    }
  }

  Future<void> _pickCitizenshipBack() async {
    final pickedFile = await _pickImage();
    if (pickedFile != null) {
      setState(() {
        _citizenshipBack = pickedFile;
        _showCitizenshipBackError = false;
      });
    }
  }

  Future<XFile?> _pickImage() async {
    final picker = ImagePicker();
    return await picker.pickImage(source: ImageSource.gallery);
  }

  void _onPressed() {
    setState(() {
      _showCitizenshipFrontError = false;
      _showCitizenshipBackError = false;
    });

    bool isValid = true;
    if (_citizenshipFront == null) {
      setState(() {
        _showCitizenshipFrontError = true;
      });
      isValid = false;
    }
    if (_citizenshipBack == null) {
      setState(() {
        _showCitizenshipBackError = true;
      });
      isValid = false;
    }

    if (isValid) {
      // Save document photos and proceed
      sl<RequestForDriverBloc>().add(
        RequestForDriverEvent.requestForDriverProgressPage(
          pageName: AppRoutesName.driverDocumentPage,
          driverRequestForm: DriverRegistrationRequest(
            citizenCardFront: _citizenshipFront,
            citizenCardBack: _citizenshipBack,
          ),
        ),
      );
      context.pushNamed(AppRoutesName.selfePage);
    }
  }
}
