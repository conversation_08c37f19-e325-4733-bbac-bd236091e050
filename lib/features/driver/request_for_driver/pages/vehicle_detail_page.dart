import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:safari_yatri/common/blocs/get_vehicle_type/get_vehicle_type_bloc.dart';
import 'package:safari_yatri/common/blocs/image_picker/image_picker_bloc.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/models/get_vehile_type.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_form_field.dart';
import 'package:safari_yatri/core/widget/custom_pop_button.dart';
import 'package:safari_yatri/core/widget/vehicle_type_widget.dart';
import 'package:safari_yatri/features/driver/request_for_driver/blocs/request_for_driver/request_for_driver_bloc.dart';
import 'package:safari_yatri/features/driver/request_for_driver/models/request_for_driver_form.dart';

class VehicleDetailPage extends StatefulWidget {
  const VehicleDetailPage({super.key});

  @override
  State<VehicleDetailPage> createState() => _VehicleDetailPageState();
}

class _VehicleDetailPageState extends State<VehicleDetailPage> {
  final _formKey = GlobalKey<FormState>();
  late final ImagePickerBloc _vehiclePhotoBloc;
  late final ImagePickerBloc _blueBookPhotoBloc;

  final _vehicleNoController = TextEditingController();
  final _ownerNameController = TextEditingController();
  final _ownerPhoneController = TextEditingController();
  GetVehicleType? _selectedVehicleType; // To store selected vehicle type object

  @override
  initState() {
    super.initState();
    _vehiclePhotoBloc = sl<ImagePickerBloc>();
    _blueBookPhotoBloc =
        ImagePickerBloc(); // Create a new instance for blue book
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final state = sl<RequestForDriverBloc>().state;
    state.whenOrNull(
      loaded: (data) {
        // Load vehicle photo
        final List<XFile> vehicleImageList = [];
        if (data.data.vehiclePhoto != null) {
          vehicleImageList.add(data.data.vehiclePhoto!);
        }
        _vehiclePhotoBloc.add(LoadInitialImagesEvent(vehicleImageList));

        // Load blue book photo
        final List<XFile> blueBookImageList = [];
        if (data.data.blueBookPhoto != null) {
          blueBookImageList.add(data.data.blueBookPhoto!);
        }
        _blueBookPhotoBloc.add(LoadInitialImagesEvent(blueBookImageList));

        // Load text fields
        _vehicleNoController.text = data.data.vehicleNo ?? "";
        _ownerNameController.text = data.data.ownerName ?? "";
        _ownerPhoneController.text = data.data.ownerPhone ?? "";

        // Set selected vehicle type from vehicleTypeId
        if (data.data.vehicleTypeId != 0) {
          sl<GetVehicleTypeBloc>().state.maybeWhen(
            loaded: (vehicleTypes) {
              _selectedVehicleType = vehicleTypes.firstWhere(
                (type) => type.vehicleTypeId == data.data.vehicleTypeId,
                orElse: () => vehicleTypes.first, // Fallback if not found
              );
            },
            orElse: () {},
          );
        }
      },
    );
  }

  @override
  void dispose() {
    _vehiclePhotoBloc.close();
    _blueBookPhotoBloc.close();
    _vehicleNoController.dispose();
    _ownerNameController.dispose();
    _ownerPhoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: AppStyles.space12,
            vertical: AppStyles.space40,
          ),
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomBackButton(),
                  SizedBox(height: AppStyles.space8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30.0),
                    child: Column(
                      children: [
                        Text(
                          'Enter your Vehicle information',
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.displayLarge,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: AppStyles.space12),

                VehicleTypeDropdown(selectedVehicleType: _selectedVehicleType, onChanged: (vehicle){
                  _selectedVehicleType = vehicle;
                }),
                  SizedBox(height: AppStyles.space8),
                  CustomFormFieldWidget(
                    label: 'Vehicle Number',
                    controller: _vehicleNoController,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter vehicle number';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: AppStyles.space8),
                  CustomFormFieldWidget(
                    label: 'Owner Name',
                    controller: _ownerNameController,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter owner name';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: AppStyles.space8),
                  CustomFormFieldWidget(
                    label: 'Owner Phone',
                    controller: _ownerPhoneController,
                    keys: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter owner phone number';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: AppStyles.space12),
                  buildImagePicker(
                    context,
                    label: 'Upload Vehicle Photo',
                    bloc: _vehiclePhotoBloc,
                    maxImages: 1, // Only one vehicle photo allowed
                  ),
                  SizedBox(height: AppStyles.space12),
                  buildImagePicker(
                    context,
                    label: 'Upload Blue Book Photo',
                    bloc: _blueBookPhotoBloc,
                    maxImages: 1, // Only one blue book photo allowed
                  ),
                  SizedBox(height: AppStyles.space64),
                  CustomButtonPrimary(title: 'Next', onPressed: _onPressed),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildImagePicker(
    BuildContext context, {
    required String label,
    required ImagePickerBloc bloc,
    int maxImages = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: Theme.of(context).textTheme.titleMedium),
        SizedBox(height: AppStyles.space8),
        BlocBuilder<ImagePickerBloc, ImagePickerState>(
          bloc: bloc,
          builder: (context, state) {
            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount:
                  state.selectedImages.length < maxImages
                      ? state.selectedImages.length + 1
                      : state.selectedImages.length,
              itemBuilder: (context, index) {
                if (index < state.selectedImages.length) {
                  final XFile image = state.selectedImages[index];
                  return Stack(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                          image: DecorationImage(
                            image: FileImage(File(image.path)),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () => bloc.add(RemoveImageEvent(index)),
                          child: const CircleAvatar(
                            radius: 12,
                            backgroundColor: Colors.red,
                            child: Icon(
                              Icons.close,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                } else {
                  return GestureDetector(
                    onTap:
                        () => bloc.add(
                          PickImagesEvent(context, ImagePickMode.single),
                        ),
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.add_a_photo,
                          size: 40,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  );
                }
              },
            );
          },
        ),
        BlocListener<ImagePickerBloc, ImagePickerState>(
          bloc: bloc,
          listener: (context, state) {
            if (state.errorMessage != null) {
              CustomToast.showError(state.errorMessage!);
            }
          },
          child: const SizedBox.shrink(),
        ),
      ],
    );
  }

  void _onPressed() {
    if (_formKey.currentState!.validate()) {
      if (_selectedVehicleType == null) {
        CustomToast.showError('Please select a vehicle type.');
        return;
      }
      if (_vehiclePhotoBloc.state.selectedImages.isEmpty) {
        CustomToast.showError('Please upload a vehicle photo.');
        return;
      }
      if (_blueBookPhotoBloc.state.selectedImages.isEmpty) {
        CustomToast.showError('Please upload a blue book photo.');
        return;
      }

      // print('Vehicle Type ID: ${_selectedVehicleType!.vehicleTypeId}');

      // Save vehicle details and proceed
      sl<RequestForDriverBloc>().add(
        RequestForDriverEvent.requestForDriverProgressPage(
          pageName: AppRoutesName.vehicleDetailsPage,
          driverRequestForm: DriverRegistrationRequest(
            vehicleTypeId: _selectedVehicleType!.vehicleTypeId,
            vehicleNo: _vehicleNoController.text,
            ownerName: _ownerNameController.text,
            ownerPhone: _ownerPhoneController.text,
            vehiclePhoto:
                _vehiclePhotoBloc.state.selectedImages.isNotEmpty
                    ? _vehiclePhotoBloc.state.selectedImages[0]
                    : null,
            blueBookPhoto:
                _blueBookPhotoBloc.state.selectedImages.isNotEmpty
                    ? _blueBookPhotoBloc.state.selectedImages[0]
                    : null,
          ),
        ),
      );
      context.pushNamed(AppRoutesName.driverDocumentPage);
    }
  }
}
