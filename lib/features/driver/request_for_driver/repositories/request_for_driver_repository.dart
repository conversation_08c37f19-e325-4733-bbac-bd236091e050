import 'package:dartz/dartz.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/network/api_service.dart';
import 'package:safari_yatri/features/driver/request_for_driver/models/request_for_driver_form.dart';

abstract interface class RequestForDriverRepository {
  FutureEither<String> requestForDriver(
    DriverRegistrationRequest driverRequestForm,
  );
}

class RequestForDriverRepositoryI implements RequestForDriverRepository {
  final ApiService _apiService;
  RequestForDriverRepositoryI({required ApiService apiService})
    : _apiService = apiService;

  @override
  FutureEither<String> requestForDriver(
    DriverRegistrationRequest driverRequestForm,
  ) async {
    ///main reason to make try is that serverToMap might throw us error to tackle these
    ///issue we are making it
    try {
      final data = await driverRequestForm.serverToMap();
      // log(jsonEncode(data));
      return await _apiService.post<String>(
        'MyProfile/RequestForRider',
        data: data,
        fromJson: (data) => "Success",
      );
    } catch (e) {
      return Left(UnexpectedFailure(message: e.toString()));
    }
  }
}
