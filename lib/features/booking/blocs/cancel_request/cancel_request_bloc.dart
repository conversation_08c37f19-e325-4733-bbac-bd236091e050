import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';
part 'cancel_request_event.dart';
part 'cancel_request_state.dart';
part 'cancel_request_bloc.freezed.dart';

class CancelRequestBloc extends Bloc<CancelRequestEvent, CancelRequestState> {
  final BookingRepository _bookingRepository;
  CancelRequestBloc({required BookingRepository repo})
    : _bookingRepository = repo,
      super(CancelRequestState.initial()) {
    on<_CancelRequest>(_onCancelBooking);
  }

  Future<void> _onCancelBooking(
    _CancelRequest event,
    Emitter<CancelRequestState> emit,
  ) async {
    emit(CancelRequestState.loading());
    final result = await _bookingRepository.cancelBooking(
      bookingId: event.bookingId,
      reasonForCancel: event.reasonForCancel,
    );

    result.fold(
      (failure) => emit(CancelRequestState.failure(failure)),
      (data) => emit(CancelRequestState.loaded(data)),
    );
  }
}
