// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_to_cart_booking_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AddToCartBookingEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AddToCartBookingEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AddToCartBookingEvent()';
  }
}

/// Adds pattern-matching-related methods to [AddToCartBookingEvent].
extension AddToCartBookingEventPatterns on AddToCartBookingEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Create value)? create,
    TResult Function(_ShareRidingMode value)? shareRidingMode,
    TResult Function(InititalizeRoutes value)? inititalizeRoutes,
    TResult Function(_ScheduleTripDateTime value)? scheduleTripDateTime,
    TResult Function(_InitialPassengerCount value)? initialPassengerCount,
    TResult Function(_RaiseFare value)? raiseFare,
    TResult Function(_ExpandSearchDiameter value)? expandSearchDiameter,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Create() when create != null:
        return create(_that);
      case _ShareRidingMode() when shareRidingMode != null:
        return shareRidingMode(_that);
      case InititalizeRoutes() when inititalizeRoutes != null:
        return inititalizeRoutes(_that);
      case _ScheduleTripDateTime() when scheduleTripDateTime != null:
        return scheduleTripDateTime(_that);
      case _InitialPassengerCount() when initialPassengerCount != null:
        return initialPassengerCount(_that);
      case _RaiseFare() when raiseFare != null:
        return raiseFare(_that);
      case _ExpandSearchDiameter() when expandSearchDiameter != null:
        return expandSearchDiameter(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Create value) create,
    required TResult Function(_ShareRidingMode value) shareRidingMode,
    required TResult Function(InititalizeRoutes value) inititalizeRoutes,
    required TResult Function(_ScheduleTripDateTime value) scheduleTripDateTime,
    required TResult Function(_InitialPassengerCount value)
    initialPassengerCount,
    required TResult Function(_RaiseFare value) raiseFare,
    required TResult Function(_ExpandSearchDiameter value) expandSearchDiameter,
  }) {
    final _that = this;
    switch (_that) {
      case _Create():
        return create(_that);
      case _ShareRidingMode():
        return shareRidingMode(_that);
      case InititalizeRoutes():
        return inititalizeRoutes(_that);
      case _ScheduleTripDateTime():
        return scheduleTripDateTime(_that);
      case _InitialPassengerCount():
        return initialPassengerCount(_that);
      case _RaiseFare():
        return raiseFare(_that);
      case _ExpandSearchDiameter():
        return expandSearchDiameter(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Create value)? create,
    TResult? Function(_ShareRidingMode value)? shareRidingMode,
    TResult? Function(InititalizeRoutes value)? inititalizeRoutes,
    TResult? Function(_ScheduleTripDateTime value)? scheduleTripDateTime,
    TResult? Function(_InitialPassengerCount value)? initialPassengerCount,
    TResult? Function(_RaiseFare value)? raiseFare,
    TResult? Function(_ExpandSearchDiameter value)? expandSearchDiameter,
  }) {
    final _that = this;
    switch (_that) {
      case _Create() when create != null:
        return create(_that);
      case _ShareRidingMode() when shareRidingMode != null:
        return shareRidingMode(_that);
      case InititalizeRoutes() when inititalizeRoutes != null:
        return inititalizeRoutes(_that);
      case _ScheduleTripDateTime() when scheduleTripDateTime != null:
        return scheduleTripDateTime(_that);
      case _InitialPassengerCount() when initialPassengerCount != null:
        return initialPassengerCount(_that);
      case _RaiseFare() when raiseFare != null:
        return raiseFare(_that);
      case _ExpandSearchDiameter() when expandSearchDiameter != null:
        return expandSearchDiameter(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      double passengerOfferedFare,
      int maxRiderSearchDistanceInMeter,
    )?
    create,
    TResult Function(bool isShareRidingMode)? shareRidingMode,
    TResult Function(
      DirectionRouteModel direction,
      List<LatLngWithAddress> locations,
    )?
    inititalizeRoutes,
    TResult Function(DateTime scheduleDataTime)? scheduleTripDateTime,
    TResult Function(int passengerCount)? initialPassengerCount,
    TResult Function(double passengerOfferedFare)? raiseFare,
    TResult Function(int maxDriverSearchingAreaInMeter)? expandSearchDiameter,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Create() when create != null:
        return create(
          _that.passengerOfferedFare,
          _that.maxRiderSearchDistanceInMeter,
        );
      case _ShareRidingMode() when shareRidingMode != null:
        return shareRidingMode(_that.isShareRidingMode);
      case InititalizeRoutes() when inititalizeRoutes != null:
        return inititalizeRoutes(_that.direction, _that.locations);
      case _ScheduleTripDateTime() when scheduleTripDateTime != null:
        return scheduleTripDateTime(_that.scheduleDataTime);
      case _InitialPassengerCount() when initialPassengerCount != null:
        return initialPassengerCount(_that.passengerCount);
      case _RaiseFare() when raiseFare != null:
        return raiseFare(_that.passengerOfferedFare);
      case _ExpandSearchDiameter() when expandSearchDiameter != null:
        return expandSearchDiameter(_that.maxDriverSearchingAreaInMeter);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      double passengerOfferedFare,
      int maxRiderSearchDistanceInMeter,
    )
    create,
    required TResult Function(bool isShareRidingMode) shareRidingMode,
    required TResult Function(
      DirectionRouteModel direction,
      List<LatLngWithAddress> locations,
    )
    inititalizeRoutes,
    required TResult Function(DateTime scheduleDataTime) scheduleTripDateTime,
    required TResult Function(int passengerCount) initialPassengerCount,
    required TResult Function(double passengerOfferedFare) raiseFare,
    required TResult Function(int maxDriverSearchingAreaInMeter)
    expandSearchDiameter,
  }) {
    final _that = this;
    switch (_that) {
      case _Create():
        return create(
          _that.passengerOfferedFare,
          _that.maxRiderSearchDistanceInMeter,
        );
      case _ShareRidingMode():
        return shareRidingMode(_that.isShareRidingMode);
      case InititalizeRoutes():
        return inititalizeRoutes(_that.direction, _that.locations);
      case _ScheduleTripDateTime():
        return scheduleTripDateTime(_that.scheduleDataTime);
      case _InitialPassengerCount():
        return initialPassengerCount(_that.passengerCount);
      case _RaiseFare():
        return raiseFare(_that.passengerOfferedFare);
      case _ExpandSearchDiameter():
        return expandSearchDiameter(_that.maxDriverSearchingAreaInMeter);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      double passengerOfferedFare,
      int maxRiderSearchDistanceInMeter,
    )?
    create,
    TResult? Function(bool isShareRidingMode)? shareRidingMode,
    TResult? Function(
      DirectionRouteModel direction,
      List<LatLngWithAddress> locations,
    )?
    inititalizeRoutes,
    TResult? Function(DateTime scheduleDataTime)? scheduleTripDateTime,
    TResult? Function(int passengerCount)? initialPassengerCount,
    TResult? Function(double passengerOfferedFare)? raiseFare,
    TResult? Function(int maxDriverSearchingAreaInMeter)? expandSearchDiameter,
  }) {
    final _that = this;
    switch (_that) {
      case _Create() when create != null:
        return create(
          _that.passengerOfferedFare,
          _that.maxRiderSearchDistanceInMeter,
        );
      case _ShareRidingMode() when shareRidingMode != null:
        return shareRidingMode(_that.isShareRidingMode);
      case InititalizeRoutes() when inititalizeRoutes != null:
        return inititalizeRoutes(_that.direction, _that.locations);
      case _ScheduleTripDateTime() when scheduleTripDateTime != null:
        return scheduleTripDateTime(_that.scheduleDataTime);
      case _InitialPassengerCount() when initialPassengerCount != null:
        return initialPassengerCount(_that.passengerCount);
      case _RaiseFare() when raiseFare != null:
        return raiseFare(_that.passengerOfferedFare);
      case _ExpandSearchDiameter() when expandSearchDiameter != null:
        return expandSearchDiameter(_that.maxDriverSearchingAreaInMeter);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Create implements AddToCartBookingEvent {
  const _Create({
    required this.passengerOfferedFare,
    required this.maxRiderSearchDistanceInMeter,
  });

  final double passengerOfferedFare;
  final int maxRiderSearchDistanceInMeter;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Create &&
            (identical(other.passengerOfferedFare, passengerOfferedFare) ||
                other.passengerOfferedFare == passengerOfferedFare) &&
            (identical(
                  other.maxRiderSearchDistanceInMeter,
                  maxRiderSearchDistanceInMeter,
                ) ||
                other.maxRiderSearchDistanceInMeter ==
                    maxRiderSearchDistanceInMeter));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    passengerOfferedFare,
    maxRiderSearchDistanceInMeter,
  );

  @override
  String toString() {
    return 'AddToCartBookingEvent.create(passengerOfferedFare: $passengerOfferedFare, maxRiderSearchDistanceInMeter: $maxRiderSearchDistanceInMeter)';
  }
}

/// @nodoc

class _ShareRidingMode implements AddToCartBookingEvent {
  const _ShareRidingMode({required this.isShareRidingMode});

  final bool isShareRidingMode;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ShareRidingMode &&
            (identical(other.isShareRidingMode, isShareRidingMode) ||
                other.isShareRidingMode == isShareRidingMode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isShareRidingMode);

  @override
  String toString() {
    return 'AddToCartBookingEvent.shareRidingMode(isShareRidingMode: $isShareRidingMode)';
  }
}

/// @nodoc

class InititalizeRoutes implements AddToCartBookingEvent {
  const InititalizeRoutes({
    required this.direction,
    required final List<LatLngWithAddress> locations,
  }) : _locations = locations;

  final DirectionRouteModel direction;
  final List<LatLngWithAddress> _locations;
  List<LatLngWithAddress> get locations {
    if (_locations is EqualUnmodifiableListView) return _locations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_locations);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InititalizeRoutes &&
            (identical(other.direction, direction) ||
                other.direction == direction) &&
            const DeepCollectionEquality().equals(
              other._locations,
              _locations,
            ));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    direction,
    const DeepCollectionEquality().hash(_locations),
  );

  @override
  String toString() {
    return 'AddToCartBookingEvent.inititalizeRoutes(direction: $direction, locations: $locations)';
  }
}

/// @nodoc

class _ScheduleTripDateTime implements AddToCartBookingEvent {
  const _ScheduleTripDateTime({required this.scheduleDataTime});

  final DateTime scheduleDataTime;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ScheduleTripDateTime &&
            (identical(other.scheduleDataTime, scheduleDataTime) ||
                other.scheduleDataTime == scheduleDataTime));
  }

  @override
  int get hashCode => Object.hash(runtimeType, scheduleDataTime);

  @override
  String toString() {
    return 'AddToCartBookingEvent.scheduleTripDateTime(scheduleDataTime: $scheduleDataTime)';
  }
}

/// @nodoc

class _InitialPassengerCount implements AddToCartBookingEvent {
  const _InitialPassengerCount({required this.passengerCount});

  final int passengerCount;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InitialPassengerCount &&
            (identical(other.passengerCount, passengerCount) ||
                other.passengerCount == passengerCount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passengerCount);

  @override
  String toString() {
    return 'AddToCartBookingEvent.initialPassengerCount(passengerCount: $passengerCount)';
  }
}

/// @nodoc

class _RaiseFare implements AddToCartBookingEvent {
  const _RaiseFare({required this.passengerOfferedFare});

  final double passengerOfferedFare;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RaiseFare &&
            (identical(other.passengerOfferedFare, passengerOfferedFare) ||
                other.passengerOfferedFare == passengerOfferedFare));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passengerOfferedFare);

  @override
  String toString() {
    return 'AddToCartBookingEvent.raiseFare(passengerOfferedFare: $passengerOfferedFare)';
  }
}

/// @nodoc

class _ExpandSearchDiameter implements AddToCartBookingEvent {
  const _ExpandSearchDiameter({required this.maxDriverSearchingAreaInMeter});

  final int maxDriverSearchingAreaInMeter;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ExpandSearchDiameter &&
            (identical(
                  other.maxDriverSearchingAreaInMeter,
                  maxDriverSearchingAreaInMeter,
                ) ||
                other.maxDriverSearchingAreaInMeter ==
                    maxDriverSearchingAreaInMeter));
  }

  @override
  int get hashCode => Object.hash(runtimeType, maxDriverSearchingAreaInMeter);

  @override
  String toString() {
    return 'AddToCartBookingEvent.expandSearchDiameter(maxDriverSearchingAreaInMeter: $maxDriverSearchingAreaInMeter)';
  }
}
