import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/common/repositories/vehicle_repository.dart';
import 'package:safari_yatri/core/models/direction_route_model.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/core/utils/date_time_utils.dart';
import 'package:safari_yatri/features/admin/core/repositories/ride_shift_repository.dart';
import 'package:safari_yatri/features/booking/models/booking_add_to_cart.dart';
import 'package:safari_yatri/features/booking/models/booking_details.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';
import 'package:safari_yatri/core/utils/calculate_fare.dart';
import 'package:safari_yatri/features/passenger/location/models/passenger_location.dart';

part 'add_to_cart_booking_event.dart';
part 'add_to_cart_booking_state.dart';
part 'add_to_cart_booking_bloc.freezed.dart';

class AddToCartBookingBloc
    extends Bloc<AddToCartBookingEvent, AddToCartBookingState> {
  final BookingRepository _bookingRepository;
  final RideShiftRepository _rideShiftRepository;
  double _totalDistanceInMeter = 0.0;
  double _totalFare = 0.0;

  BookingAddToCartModel? _bookingAddToCartModel;

  int _defaultPassengerCount = 1;
  bool _isSharingRidingMode = false;
  int _currentSearchingDiameter = 500;
  double _passengerOfferedFare = 0.0;

  AddToCartBookingBloc({
    required BookingRepository repo,
    required RideShiftRepository rideShiftRepo,
    required VehicleRepository vehicleTypeRepo,
  }) : _bookingRepository = repo,
       _rideShiftRepository = rideShiftRepo,
       super(const AddToCartBookingState.initial()) {
    on<InititalizeRoutes>(_onInitializeRoutes);
    on<_ShareRidingMode>(_onShareRidingMode);
    on<_InitialPassengerCount>(_onInitialPassengerCount);
    on<_Create>(_onCreate);
    on<_ScheduleTripDateTime>(_onScheduleTripDateTime);
    on<_RaiseFare>(_onRaiseFare);
    on<_ExpandSearchDiameter>(_onExpandSearchDiameter);
  }

  /// Handles the initial passenger count event.

  void _onInitialPassengerCount(
    _InitialPassengerCount event,
    Emitter<AddToCartBookingState> emit,
  ) {
    _initializeValuesIfNull();
    _defaultPassengerCount = event.passengerCount;
    emit(
      AddToCartBookingState.loaded((
        data: _bookingAddToCartModel?.copyWith(
          passengerCount: _defaultPassengerCount,
        ),
        message: null,
      )),
    );
  }

  Future<void> _onInitializeRoutes(
    InititalizeRoutes event,
    Emitter<AddToCartBookingState> emit,
  ) async {
    try {
      _isSharingRidingMode = false;
      _initializeValuesIfNull();
      if (event.locations.length < 2) {
        return;
      }
      _totalDistanceInMeter = event.direction.totalDistanceInMeters.toDouble();

      final List<BookingDetailModel> routeDetails = [];
      for (int i = 0; i < event.locations.length - 1; i++) {
        final LatLngWithAddress source = event.locations[i];
        final LatLngWithAddress destination = event.locations[i + 1];

        routeDetails.add(
          BookingDetailModel(
            sourceAddress: source.address,
            destinationAddress: destination.address,
            sourceLatitude: source.latitude,
            sourceLongitude: source.longitude,
            destinationLatitude: destination.latitude,
            destinationLongitude: destination.longitude,
          ),
        );
      }
      final failureOrFareModels = await _rideShiftRepository.getRideShiftList(
        false,
        forceRefresh: false,
      );
      _totalDistanceInMeter = _totalDistanceInMeter.ceilToDouble();
      failureOrFareModels.fold((f) {}, (data) {
        ///first vaneko safari ho tehii hunu parxa ahile chaii hard code naii xa
        ///
        _totalFare = FareUtils.calculateFare(
          slabs: data.firstOrNull?.fareRateItems ?? [],
          distanceInMeter: _totalDistanceInMeter,
          passengerCount: _defaultPassengerCount,
        );
      });
      final currentDate = DateTime.now();
      final currentTime = TimeOfDay.now();
      int totalDistanceInInt = _totalDistanceInMeter.toInt();
      _bookingAddToCartModel = BookingAddToCartModel(
        bookingStartDate: AppDateTimeUtils.getDateInYYMMDD(currentDate),
        bookingStartTime: AppDateTimeUtils.getTimeInHHMMAndPeriod(currentTime),
        passengerFareAmount: 0,
        cartDetailViews: routeDetails,
        systemFareAmount: _totalFare,
        totalDistanceInMeter: totalDistanceInInt,
        isSharedBookingMode: _isSharingRidingMode,
        passengerCount: _defaultPassengerCount,
      );

      // dLog.i(_bookingAddToCartModel!.toMap());
      emit(
        AddToCartBookingState.loaded((
          data: _bookingAddToCartModel,
          message: null,
        )),
      );
    } catch (e) {
      add(
        AddToCartBookingEvent.inititalizeRoutes(
          direction: event.direction,
          locations: event.locations,
        ),
      );
    }
  }

  /// Handles toggling the shared riding mode.
  Future<void> _onShareRidingMode(
    _ShareRidingMode event,
    Emitter<AddToCartBookingState> emit,
  ) async {
    _initializeValuesIfNull();

    _isSharingRidingMode = event.isShareRidingMode;
    _bookingAddToCartModel = _bookingAddToCartModel!.copyWith(
      isSharedBookingMode: _isSharingRidingMode,
    );
  }

  /// Handles the final "create" event to send the booking to the repository.
  Future<void> _onCreate(
    _Create event,
    Emitter<AddToCartBookingState> emit,
  ) async {
    final currentDate = DateTime.now();
    final currentTime = TimeOfDay.now();
    _passengerOfferedFare = event.passengerOfferedFare;
    _bookingAddToCartModel = _bookingAddToCartModel!.copyWith(
      riderDistanceInMeter: event.maxRiderSearchDistanceInMeter,
      passengerFareAmount: event.passengerOfferedFare,
      isSharedBookingMode: _isSharingRidingMode,
      systemFareAmount: _totalFare,
      passengerCount: _defaultPassengerCount,
      bookingStartDate:
          _bookingAddToCartModel?.bookingStartDate ??
          AppDateTimeUtils.getDateInYYMMDD(currentDate),
      bookingStartTime:
          _bookingAddToCartModel?.bookingStartTime ??
          AppDateTimeUtils.getTimeInHHMMAndPeriod(currentTime),
    );

    if (_bookingAddToCartModel == null) {
      return;
    }

    emit(const AddToCartBookingState.loading());
    final result = await _bookingRepository.bookingAddToCart(
      bookingAddToCart: _bookingAddToCartModel!,
    );

    result.fold(
      (failure) => emit(AddToCartBookingState.failure(failure)),
      (successMessage) => emit(
        AddToCartBookingState.loaded((
          data: _bookingAddToCartModel,
          message: successMessage,
        )),
      ),
    );
  }

  Future<void> _onScheduleTripDateTime(
    _ScheduleTripDateTime event,
    Emitter<AddToCartBookingState> emit,
  ) async {
    _initializeValuesIfNull();
    _bookingAddToCartModel = _bookingAddToCartModel!.copyWith(
      bookingStartDate: AppDateTimeUtils.getDateInYYMMDD(
        event.scheduleDataTime,
      ),
      bookingStartTime: AppDateTimeUtils.getTimeInHHMMAndPeriod(
        TimeOfDay.fromDateTime(event.scheduleDataTime),
      ),
    );
  }

  void _initializeValuesIfNull() {
    _bookingAddToCartModel ??= BookingAddToCartModel(
      isSharedBookingMode: _isSharingRidingMode,
      systemFareAmount: _totalFare,
      passengerCount: _defaultPassengerCount,
      passengerFareAmount: _passengerOfferedFare,
    );
  }

  Future<void> _onRaiseFare(
    _RaiseFare event,
    Emitter<AddToCartBookingState> emit,
  ) async {
    _bookingAddToCartModel = _bookingAddToCartModel!.copyWith(
      passengerFareAmount: event.passengerOfferedFare,
    );

    if (_bookingAddToCartModel == null) {
      return;
    }

    emit(const AddToCartBookingState.loading());
    final result = await _bookingRepository.bookingAddToCart(
      bookingAddToCart: _bookingAddToCartModel!,
    );

    result.fold((failure) => emit(AddToCartBookingState.failure(failure)), (
      successMessage,
    ) {
      _passengerOfferedFare = event.passengerOfferedFare;

      emit(
        AddToCartBookingState.loaded((
          data: _bookingAddToCartModel,
          message: "Fare raised",
        )),
      );
    });
  }

  Future<void> _onExpandSearchDiameter(
    _ExpandSearchDiameter event,
    Emitter<AddToCartBookingState> emit,
  ) async {
    _currentSearchingDiameter = event.maxDriverSearchingAreaInMeter;
    _bookingAddToCartModel = _bookingAddToCartModel!.copyWith(
      riderDistanceInMeter: _currentSearchingDiameter,
    );

    if (_bookingAddToCartModel == null) {
      return;
    }

    // emit(const AddToCartBookingState.loading());
    final result = await _bookingRepository.bookingAddToCart(
      bookingAddToCart: _bookingAddToCartModel!,
    );

    result.fold(
      (failure) => emit(AddToCartBookingState.failure(failure)),
      (successMessage) => emit(
        AddToCartBookingState.loaded((
          data: _bookingAddToCartModel,
          message: null,
        )),
      ),
    );
  }
}
