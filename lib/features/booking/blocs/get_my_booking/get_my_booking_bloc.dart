import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'get_my_booking_event.dart';
part 'get_my_booking_state.dart';
part 'get_my_booking_bloc.freezed.dart';

///It will help to get all the bookings of the Passenger
///[PassengerBooking]
class GetMyBookingBloc extends Bloc<GetMyBookingEvent, GetMyBookingState> {
  final BookingRepository _bookingRepository;
  GetMyBookingBloc({required BookingRepository repo})
    : _bookingRepository = repo,
      super(const GetMyBookingState.initial()) {
    on<_GetActiveBookings>(_onGetActiveBookings);
    on<_GetAllBookings>(_onGetAllBookings);
  }

  Future<void> _onGetActiveBookings(
    _GetActiveBookings event,
    Emitter<GetMyBookingState> emit,
  ) async {
    emit(const GetMyBookingState.loading());
    final result = await _bookingRepository.getMyBookings(
      bookingStatus: "ActiveOnly",
    );
    result.fold(
      (l) => emit(GetMyBookingState.failure(l)),
      (r) => emit(GetMyBookingState.loaded(r)),
    );
  }

  Future<void> _onGetAllBookings(
    _GetAllBookings event,
    Emitter<GetMyBookingState> emit,
  ) async {
    emit(const GetMyBookingState.loading());
    final result = await _bookingRepository.getMyBookings(bookingStatus: "All");
    result.fold(
      (l) => emit(GetMyBookingState.failure(l)),
      (r) => emit(GetMyBookingState.loaded(r)),
    );
  }
}
