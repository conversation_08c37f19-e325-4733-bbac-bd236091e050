// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_my_current_passengers_booking_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetMyCurrentPassengersBookingEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GetMyCurrentPassengersBookingEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyCurrentPassengersBookingEvent()';
  }
}

/// Adds pattern-matching-related methods to [GetMyCurrentPassengersBookingEvent].
extension GetMyCurrentPassengersBookingEventPatterns
    on GetMyCurrentPassengersBookingEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetCurrentBooking value)? getCurrentBooking,
    TResult Function(_StopCurrentBookings value)? stopCurrentBookings,
    TResult Function(_ResumeCurrentBookings value)? resumeCurrentBookign,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GetCurrentBooking() when getCurrentBooking != null:
        return getCurrentBooking(_that);
      case _StopCurrentBookings() when stopCurrentBookings != null:
        return stopCurrentBookings(_that);
      case _ResumeCurrentBookings() when resumeCurrentBookign != null:
        return resumeCurrentBookign(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetCurrentBooking value) getCurrentBooking,
    required TResult Function(_StopCurrentBookings value) stopCurrentBookings,
    required TResult Function(_ResumeCurrentBookings value)
    resumeCurrentBookign,
  }) {
    final _that = this;
    switch (_that) {
      case _GetCurrentBooking():
        return getCurrentBooking(_that);
      case _StopCurrentBookings():
        return stopCurrentBookings(_that);
      case _ResumeCurrentBookings():
        return resumeCurrentBookign(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetCurrentBooking value)? getCurrentBooking,
    TResult? Function(_StopCurrentBookings value)? stopCurrentBookings,
    TResult? Function(_ResumeCurrentBookings value)? resumeCurrentBookign,
  }) {
    final _that = this;
    switch (_that) {
      case _GetCurrentBooking() when getCurrentBooking != null:
        return getCurrentBooking(_that);
      case _StopCurrentBookings() when stopCurrentBookings != null:
        return stopCurrentBookings(_that);
      case _ResumeCurrentBookings() when resumeCurrentBookign != null:
        return resumeCurrentBookign(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getCurrentBooking,
    TResult Function()? stopCurrentBookings,
    TResult Function()? resumeCurrentBookign,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GetCurrentBooking() when getCurrentBooking != null:
        return getCurrentBooking();
      case _StopCurrentBookings() when stopCurrentBookings != null:
        return stopCurrentBookings();
      case _ResumeCurrentBookings() when resumeCurrentBookign != null:
        return resumeCurrentBookign();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getCurrentBooking,
    required TResult Function() stopCurrentBookings,
    required TResult Function() resumeCurrentBookign,
  }) {
    final _that = this;
    switch (_that) {
      case _GetCurrentBooking():
        return getCurrentBooking();
      case _StopCurrentBookings():
        return stopCurrentBookings();
      case _ResumeCurrentBookings():
        return resumeCurrentBookign();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getCurrentBooking,
    TResult? Function()? stopCurrentBookings,
    TResult? Function()? resumeCurrentBookign,
  }) {
    final _that = this;
    switch (_that) {
      case _GetCurrentBooking() when getCurrentBooking != null:
        return getCurrentBooking();
      case _StopCurrentBookings() when stopCurrentBookings != null:
        return stopCurrentBookings();
      case _ResumeCurrentBookings() when resumeCurrentBookign != null:
        return resumeCurrentBookign();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _GetCurrentBooking implements GetMyCurrentPassengersBookingEvent {
  const _GetCurrentBooking();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _GetCurrentBooking);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyCurrentPassengersBookingEvent.getCurrentBooking()';
  }
}

/// @nodoc

class _StopCurrentBookings implements GetMyCurrentPassengersBookingEvent {
  const _StopCurrentBookings();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _StopCurrentBookings);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyCurrentPassengersBookingEvent.stopCurrentBookings()';
  }
}

/// @nodoc

class _ResumeCurrentBookings implements GetMyCurrentPassengersBookingEvent {
  const _ResumeCurrentBookings();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ResumeCurrentBookings);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyCurrentPassengersBookingEvent.resumeCurrentBookign()';
  }
}
