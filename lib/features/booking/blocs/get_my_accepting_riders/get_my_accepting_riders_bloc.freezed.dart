// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_my_accepting_riders_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetMyAcceptingRidersEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GetMyAcceptingRidersEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyAcceptingRidersEvent()';
  }
}

/// Adds pattern-matching-related methods to [GetMyAcceptingRidersEvent].
extension GetMyAcceptingRidersEventPatterns on GetMyAcceptingRidersEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Get value)? get,
    TResult Function(_Stop value)? stop,
    TResult Function(_Resume value)? resume,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Get() when get != null:
        return get(_that);
      case _Stop() when stop != null:
        return stop(_that);
      case _Resume() when resume != null:
        return resume(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Get value) get,
    required TResult Function(_Stop value) stop,
    required TResult Function(_Resume value) resume,
  }) {
    final _that = this;
    switch (_that) {
      case _Get():
        return get(_that);
      case _Stop():
        return stop(_that);
      case _Resume():
        return resume(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Get value)? get,
    TResult? Function(_Stop value)? stop,
    TResult? Function(_Resume value)? resume,
  }) {
    final _that = this;
    switch (_that) {
      case _Get() when get != null:
        return get(_that);
      case _Stop() when stop != null:
        return stop(_that);
      case _Resume() when resume != null:
        return resume(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? get,
    TResult Function()? stop,
    TResult Function()? resume,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Get() when get != null:
        return get();
      case _Stop() when stop != null:
        return stop();
      case _Resume() when resume != null:
        return resume();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() get,
    required TResult Function() stop,
    required TResult Function() resume,
  }) {
    final _that = this;
    switch (_that) {
      case _Get():
        return get();
      case _Stop():
        return stop();
      case _Resume():
        return resume();
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? get,
    TResult? Function()? stop,
    TResult? Function()? resume,
  }) {
    final _that = this;
    switch (_that) {
      case _Get() when get != null:
        return get();
      case _Stop() when stop != null:
        return stop();
      case _Resume() when resume != null:
        return resume();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Get implements GetMyAcceptingRidersEvent {
  const _Get();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Get);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyAcceptingRidersEvent.get()';
  }
}

/// @nodoc

class _Stop implements GetMyAcceptingRidersEvent {
  const _Stop();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Stop);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyAcceptingRidersEvent.stop()';
  }
}

/// @nodoc

class _Resume implements GetMyAcceptingRidersEvent {
  const _Resume();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Resume);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetMyAcceptingRidersEvent.resume()';
  }
}
