import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/services/polling/polling_service.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/models/accepting_riders_model.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'get_my_accepting_riders_event.dart';
part 'get_my_accepting_riders_state.dart';
part 'get_my_accepting_riders_bloc.freezed.dart';

class GetMyAcceptingRidersBloc
    extends Bloc<GetMyAcceptingRidersEvent, GetMyAcceptingRidersState> {
  final PollingService _pollingService;
  final BookingRepository _bookingRepository;
  final String _pollingId = "GetMyAcceptingRiders";
  bool _isInitialize = false;
  GetMyAcceptingRidersBloc({
    required PollingService pollingService,
    required BookingRepository repo,
  }) : _pollingService = pollingService,
       _bookingRepository = repo,
       super(GetMyAcceptingRidersState.initial()) {
    if (!_pollingService.hasTask(_pollingId)) {
      _pollingService.register(
        id: _pollingId,
        immediate: false,
        onPoll: (date) {
          add(GetMyAcceptingRidersEvent.get());
        },
        interval: Duration(seconds: 12),
      );
    }
    _pollingService.start(_pollingId);

    on<_Get>(_onGetMyAcceptingRiders);
    on<_Stop>(_onStop);
    on<_Resume>(_onResume);
  }

  Future<void> _onGetMyAcceptingRiders(
    _Get event,
    Emitter<GetMyAcceptingRidersState> emit,
  ) async {
    if (!_isInitialize) {
      _isInitialize = true;
      emit(GetMyAcceptingRidersState.loading());
    }
    final response = await _bookingRepository.getMyAcceptingRiders();
    response.fold(
      (failure) => emit(GetMyAcceptingRidersState.failure(failure)),
      (data) => emit(GetMyAcceptingRidersState.loaded(data)),
    );
  }

  Future<void> _onStop(
    _Stop event,
    Emitter<GetMyAcceptingRidersState> emit,
  ) async {
    _pollingService.stop(_pollingId);
  }

  Future<void> _onResume(
    _Resume event,
    Emitter<GetMyAcceptingRidersState> emit,
  ) async {
    await _pollingService.resume(_pollingId);
  }
}
