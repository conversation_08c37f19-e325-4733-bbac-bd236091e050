import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'clear_my_cart_event.dart';
part 'clear_my_cart_state.dart';
part 'clear_my_cart_bloc.freezed.dart';

class ClearMyCartBloc extends Bloc<ClearMyCartEvent, ClearMyCartState> {
  final BookingRepository _bookingRepository;
  ClearMyCartBloc({required BookingRepository repo})
    : _bookingRepository = repo,
      super(ClearMyCartState.initial()) {
    on<_ClearMyCart>(_onClearMyCart);
  }

  Future<void> _onClearMyCart(
    _ClearMyCart event,
    Emitter<ClearMyCartState> emit,
  ) async {
    emit(ClearMyCartState.loading());

    final result = await _bookingRepository.clearMyCart();

    result.fold(
      (failure) => emit(ClearMyCartState.failure(failure)),
      (data) => emit(ClearMyCartState.loaded(data)),
    );
  }
}
