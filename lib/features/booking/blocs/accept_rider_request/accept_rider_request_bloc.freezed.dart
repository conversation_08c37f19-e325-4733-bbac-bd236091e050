// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'accept_rider_request_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AcceptRiderRequestEvent {
  int get riderId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AcceptRiderRequestEvent &&
            (identical(other.riderId, riderId) || other.riderId == riderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, riderId);

  @override
  String toString() {
    return 'AcceptRiderRequestEvent(riderId: $riderId)';
  }
}

/// Adds pattern-matching-related methods to [AcceptRiderRequestEvent].
extension AcceptRiderRequestEventPatterns on AcceptRiderRequestEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Accept value)? accept,
    TResult Function(_Decline value)? decline,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Accept() when accept != null:
        return accept(_that);
      case _Decline() when decline != null:
        return decline(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Accept value) accept,
    required TResult Function(_Decline value) decline,
  }) {
    final _that = this;
    switch (_that) {
      case _Accept():
        return accept(_that);
      case _Decline():
        return decline(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Accept value)? accept,
    TResult? Function(_Decline value)? decline,
  }) {
    final _that = this;
    switch (_that) {
      case _Accept() when accept != null:
        return accept(_that);
      case _Decline() when decline != null:
        return decline(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int riderId)? accept,
    TResult Function(int riderId)? decline,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Accept() when accept != null:
        return accept(_that.riderId);
      case _Decline() when decline != null:
        return decline(_that.riderId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int riderId) accept,
    required TResult Function(int riderId) decline,
  }) {
    final _that = this;
    switch (_that) {
      case _Accept():
        return accept(_that.riderId);
      case _Decline():
        return decline(_that.riderId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int riderId)? accept,
    TResult? Function(int riderId)? decline,
  }) {
    final _that = this;
    switch (_that) {
      case _Accept() when accept != null:
        return accept(_that.riderId);
      case _Decline() when decline != null:
        return decline(_that.riderId);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Accept implements AcceptRiderRequestEvent {
  const _Accept(this.riderId);

  @override
  final int riderId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Accept &&
            (identical(other.riderId, riderId) || other.riderId == riderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, riderId);

  @override
  String toString() {
    return 'AcceptRiderRequestEvent.accept(riderId: $riderId)';
  }
}

/// @nodoc

class _Decline implements AcceptRiderRequestEvent {
  const _Decline(this.riderId);

  @override
  final int riderId;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Decline &&
            (identical(other.riderId, riderId) || other.riderId == riderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, riderId);

  @override
  String toString() {
    return 'AcceptRiderRequestEvent.decline(riderId: $riderId)';
  }
}
