import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'accept_rider_request_event.dart';
part 'accept_rider_request_state.dart';
part 'accept_rider_request_bloc.freezed.dart';

class AcceptRiderRequestBloc
    extends Bloc<AcceptRiderRequestEvent, AcceptRiderRequestState> {
  final BookingRepository _bookingRepository;
  AcceptRiderRequestBloc({required BookingRepository repo})
    : _bookingRepository = repo,
      super(AcceptRiderRequestState.initial()) {
    on<_Accept>(_onAccept);
  }

  Future<void> _onAccept(
    _Accept event,
    Emitter<AcceptRiderRequestState> emit,
  ) async {
    emit(AcceptRiderRequestState.loading());
    final result = await _bookingRepository.insertBooking(
      riderId: event.riderId,
    );
    result.fold(
      (failure) => emit(AcceptRiderRequestState.failure(failure)),
      (data) => emit(AcceptRiderRequestState.loaded(data)),
    );
  }
}
