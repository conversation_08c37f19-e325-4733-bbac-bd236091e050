// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_new_passenger_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetNewPassengerEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is GetNewPassengerEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetNewPassengerEvent()';
  }
}

/// Adds pattern-matching-related methods to [GetNewPassengerEvent].
extension GetNewPassengerEventPatterns on GetNewPassengerEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetNewPassenger value)? get,
    TResult Function(_StopListening value)? stop,
    TResult Function(_ResumeListening value)? resume,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GetNewPassenger() when get != null:
        return get(_that);
      case _StopListening() when stop != null:
        return stop(_that);
      case _ResumeListening() when resume != null:
        return resume(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetNewPassenger value) get,
    required TResult Function(_StopListening value) stop,
    required TResult Function(_ResumeListening value) resume,
  }) {
    final _that = this;
    switch (_that) {
      case _GetNewPassenger():
        return get(_that);
      case _StopListening():
        return stop(_that);
      case _ResumeListening():
        return resume(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetNewPassenger value)? get,
    TResult? Function(_StopListening value)? stop,
    TResult? Function(_ResumeListening value)? resume,
  }) {
    final _that = this;
    switch (_that) {
      case _GetNewPassenger() when get != null:
        return get(_that);
      case _StopListening() when stop != null:
        return stop(_that);
      case _ResumeListening() when resume != null:
        return resume(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? get,
    TResult Function()? stop,
    TResult Function()? resume,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GetNewPassenger() when get != null:
        return get();
      case _StopListening() when stop != null:
        return stop();
      case _ResumeListening() when resume != null:
        return resume();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() get,
    required TResult Function() stop,
    required TResult Function() resume,
  }) {
    final _that = this;
    switch (_that) {
      case _GetNewPassenger():
        return get();
      case _StopListening():
        return stop();
      case _ResumeListening():
        return resume();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? get,
    TResult? Function()? stop,
    TResult? Function()? resume,
  }) {
    final _that = this;
    switch (_that) {
      case _GetNewPassenger() when get != null:
        return get();
      case _StopListening() when stop != null:
        return stop();
      case _ResumeListening() when resume != null:
        return resume();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _GetNewPassenger implements GetNewPassengerEvent {
  const _GetNewPassenger();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _GetNewPassenger);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetNewPassengerEvent.get()';
  }
}

/// @nodoc

class _StopListening implements GetNewPassengerEvent {
  const _StopListening();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _StopListening);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetNewPassengerEvent.stop()';
  }
}

/// @nodoc

class _ResumeListening implements GetNewPassengerEvent {
  const _ResumeListening();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ResumeListening);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'GetNewPassengerEvent.resume()';
  }
}
