import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'get_my_passengers_booking_event.dart';
part 'get_my_passengers_booking_state.dart';
part 'get_my_passengers_booking_bloc.freezed.dart';

class GetMyPassengersBookingBloc
    extends Bloc<GetMyPassengersBookingEvent, GetMyPassengersBookingState> {
  final BookingRepository _repository;

  List<BookingModel> _allBookings = [];
  List<BookingModel> _activeBookings = [];

  GetMyPassengersBookingBloc({required BookingRepository repo})
    : _repository = repo,
      super(const GetMyPassengersBookingState.initial()) {
    on<_GetActiveBookings>(_onGetActiveBookings);
    on<_GetAllBookings>(_onGetAllBookings);
  }

  Future<void> _onGetActiveBookings(
    _GetActiveBookings event,
    Emitter<GetMyPassengersBookingState> emit,
  ) async {
    if (event.refresh==true || _activeBookings.isEmpty) {
      emit(const GetMyPassengersBookingState.loading());

      final result = await _repository.getMyPassengerBookings(
        bookingStatus: "ActiveOnly",
      );

      result.fold(
        (failure) => emit(GetMyPassengersBookingState.failure(failure)),
        (bookings) {
          _activeBookings = bookings;
          emit(GetMyPassengersBookingState.loaded(bookings));
        },
      );
    } else {
      emit(GetMyPassengersBookingState.loaded(_activeBookings));
    }
  }

  Future<void> _onGetAllBookings(
    _GetAllBookings event,
    Emitter<GetMyPassengersBookingState> emit,
  ) async {
    if (event.refresh == true || _allBookings.isEmpty) {
      emit(const GetMyPassengersBookingState.loading());

      final result = await _repository.getMyPassengerBookings(
        bookingStatus: "All",
      );

      result.fold(
        (failure) => emit(GetMyPassengersBookingState.failure(failure)),
        (bookings) {
          _allBookings = bookings;
          emit(GetMyPassengersBookingState.loaded(bookings));
        },
      );
    } else {
      emit(GetMyPassengersBookingState.loaded(_allBookings));
    }
  }
}
