// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_my_passengers_booking_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetMyPassengersBookingEvent {
  bool? get refresh;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GetMyPassengersBookingEvent &&
            (identical(other.refresh, refresh) || other.refresh == refresh));
  }

  @override
  int get hashCode => Object.hash(runtimeType, refresh);

  @override
  String toString() {
    return 'GetMyPassengersBookingEvent(refresh: $refresh)';
  }
}

/// Adds pattern-matching-related methods to [GetMyPassengersBookingEvent].
extension GetMyPassengersBookingEventPatterns on GetMyPassengersBookingEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetActiveBookings value)? getActiveBookings,
    TResult Function(_GetAllBookings value)? getAllBookings,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GetActiveBookings() when getActiveBookings != null:
        return getActiveBookings(_that);
      case _GetAllBookings() when getAllBookings != null:
        return getAllBookings(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetActiveBookings value) getActiveBookings,
    required TResult Function(_GetAllBookings value) getAllBookings,
  }) {
    final _that = this;
    switch (_that) {
      case _GetActiveBookings():
        return getActiveBookings(_that);
      case _GetAllBookings():
        return getAllBookings(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetActiveBookings value)? getActiveBookings,
    TResult? Function(_GetAllBookings value)? getAllBookings,
  }) {
    final _that = this;
    switch (_that) {
      case _GetActiveBookings() when getActiveBookings != null:
        return getActiveBookings(_that);
      case _GetAllBookings() when getAllBookings != null:
        return getAllBookings(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool? refresh)? getActiveBookings,
    TResult Function(bool? refresh)? getAllBookings,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GetActiveBookings() when getActiveBookings != null:
        return getActiveBookings(_that.refresh);
      case _GetAllBookings() when getAllBookings != null:
        return getAllBookings(_that.refresh);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool? refresh) getActiveBookings,
    required TResult Function(bool? refresh) getAllBookings,
  }) {
    final _that = this;
    switch (_that) {
      case _GetActiveBookings():
        return getActiveBookings(_that.refresh);
      case _GetAllBookings():
        return getAllBookings(_that.refresh);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool? refresh)? getActiveBookings,
    TResult? Function(bool? refresh)? getAllBookings,
  }) {
    final _that = this;
    switch (_that) {
      case _GetActiveBookings() when getActiveBookings != null:
        return getActiveBookings(_that.refresh);
      case _GetAllBookings() when getAllBookings != null:
        return getAllBookings(_that.refresh);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _GetActiveBookings implements GetMyPassengersBookingEvent {
  const _GetActiveBookings({this.refresh});

  @override
  final bool? refresh;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GetActiveBookings &&
            (identical(other.refresh, refresh) || other.refresh == refresh));
  }

  @override
  int get hashCode => Object.hash(runtimeType, refresh);

  @override
  String toString() {
    return 'GetMyPassengersBookingEvent.getActiveBookings(refresh: $refresh)';
  }
}

/// @nodoc

class _GetAllBookings implements GetMyPassengersBookingEvent {
  const _GetAllBookings({this.refresh});

  @override
  final bool? refresh;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GetAllBookings &&
            (identical(other.refresh, refresh) || other.refresh == refresh));
  }

  @override
  int get hashCode => Object.hash(runtimeType, refresh);

  @override
  String toString() {
    return 'GetMyPassengersBookingEvent.getAllBookings(refresh: $refresh)';
  }
}
