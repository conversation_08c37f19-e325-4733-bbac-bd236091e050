import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'accept_passenger_request_event.dart';
part 'accept_passenger_request_state.dart';
part 'accept_passenger_request_bloc.freezed.dart';

class AcceptPassengerRequestBloc
    extends Bloc<AcceptPassengerRequestEvent, AcceptPassengerRequestState> {
  final BookingRepository _bookingRepository;
  AcceptPassengerRequestBloc({required BookingRepository repo})
    : _bookingRepository = repo,
      super(AcceptPassengerRequestState.initial()) {
    on<AcceptPassengerRequestEvent>(_onAcceptPassengerRequest);
  }
  Future<void> _onAcceptPassengerRequest(
    AcceptPassengerRequestEvent event,
    Emitter<AcceptPassengerRequestState> emit,
  ) async {
    emit(const AcceptPassengerRequestState.loading());
    final result = await _bookingRepository.acceptBooking(
      passengerId: event.passengerId,
      fareAmount: event.fareAmount,
    );
    result.fold((failure) {
      emit(AcceptPassengerRequestState.failure(failure));
    }, (successMessage) {
      emit(AcceptPassengerRequestState.loaded(successMessage));
    });
  }
}



