// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'polylines_points_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PolylinesPointsEvent {
  List<BookingDetailModel> get locations;
  String get uniqueValue;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PolylinesPointsEvent &&
            const DeepCollectionEquality().equals(other.locations, locations) &&
            (identical(other.uniqueValue, uniqueValue) ||
                other.uniqueValue == uniqueValue));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(locations),
    uniqueValue,
  );

  @override
  String toString() {
    return 'PolylinesPointsEvent(locations: $locations, uniqueValue: $uniqueValue)';
  }
}

/// Adds pattern-matching-related methods to [PolylinesPointsEvent].
extension PolylinesPointsEventPatterns on PolylinesPointsEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(GetPolylines value)? getPolylines,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case GetPolylines() when getPolylines != null:
        return getPolylines(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(GetPolylines value) getPolylines,
  }) {
    final _that = this;
    switch (_that) {
      case GetPolylines():
        return getPolylines(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(GetPolylines value)? getPolylines,
  }) {
    final _that = this;
    switch (_that) {
      case GetPolylines() when getPolylines != null:
        return getPolylines(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<BookingDetailModel> locations, String uniqueValue)?
    getPolylines,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case GetPolylines() when getPolylines != null:
        return getPolylines(_that.locations, _that.uniqueValue);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      List<BookingDetailModel> locations,
      String uniqueValue,
    )
    getPolylines,
  }) {
    final _that = this;
    switch (_that) {
      case GetPolylines():
        return getPolylines(_that.locations, _that.uniqueValue);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<BookingDetailModel> locations, String uniqueValue)?
    getPolylines,
  }) {
    final _that = this;
    switch (_that) {
      case GetPolylines() when getPolylines != null:
        return getPolylines(_that.locations, _that.uniqueValue);
      case _:
        return null;
    }
  }
}

/// @nodoc

class GetPolylines implements PolylinesPointsEvent {
  const GetPolylines({
    required final List<BookingDetailModel> locations,
    required this.uniqueValue,
  }) : _locations = locations;

  final List<BookingDetailModel> _locations;
  @override
  List<BookingDetailModel> get locations {
    if (_locations is EqualUnmodifiableListView) return _locations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_locations);
  }

  @override
  final String uniqueValue;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GetPolylines &&
            const DeepCollectionEquality().equals(
              other._locations,
              _locations,
            ) &&
            (identical(other.uniqueValue, uniqueValue) ||
                other.uniqueValue == uniqueValue));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_locations),
    uniqueValue,
  );

  @override
  String toString() {
    return 'PolylinesPointsEvent.getPolylines(locations: $locations, uniqueValue: $uniqueValue)';
  }
}
