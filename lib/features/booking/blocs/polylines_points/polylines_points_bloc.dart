import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/common/repositories/direction_repository.dart';
import 'package:safari_yatri/core/models/direction_route_model.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/core/utils/convert_map_cart_to_markers.dart';
import 'package:safari_yatri/features/booking/models/booking_details.dart';
import 'package:safari_yatri/features/location/repositories/location_repository.dart';

part 'polylines_points_event.dart';
part 'polylines_points_state.dart';
part 'polylines_points_bloc.freezed.dart';

class PolylinesPointsBloc
    extends Bloc<PolylinesPointsEvent, PolylinesPointsState> {
  final DirectionRepository _directionRepository;
  final LocationRepository _locationRepository;
  final Map<String, DirectionRouteModel> _previousRouteModels = {};

  PolylinesPointsBloc({
    required DirectionRepository directionRepository,
    required LocationRepository locationRepository,
  }) : _directionRepository = directionRepository,
       _locationRepository = locationRepository,
       super(const PolylinesPointsState.initial()) {
    on<GetPolylines>(_onGetPolylines);
  }

  Future<void> _onGetPolylines(
    GetPolylines event,
    Emitter<PolylinesPointsState> emit,
  ) async {
    emit(const PolylinesPointsState.loading());
    if (event.locations.isEmpty) return;
    if (_previousRouteModels.containsKey(event.uniqueValue)) {
      emit(
        PolylinesPointsState.loaded(_previousRouteModels[event.uniqueValue]!),
      );

      return;
    }

    final currentPosition = await _locationRepository.getCurrentPosition();
    LatLng? currentLatLng;
    currentPosition.fold((f) {}, (position) {
      currentLatLng = LatLng(position.latitude, position.longitude);
    });

    final latlng = convertCartDetailsToLatLng(event.locations);
    final result = await _directionRepository.getDirectionRoute(
      current: currentLatLng,
      start: latlng.first,
      destinations: latlng.skip(1).map((loc) => loc).toList(),
    );
    result.fold(
      (failure) {
        emit(PolylinesPointsState.failure(failure));
      },
      (data) {
        _previousRouteModels.addAll({event.uniqueValue: data});

        emit(PolylinesPointsState.loaded(data));
      },
    );
  }
}
