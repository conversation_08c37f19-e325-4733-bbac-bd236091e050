import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/features/booking/blocs/accept_rider_request/accept_rider_request_bloc.dart';
import 'package:safari_yatri/features/booking/models/accepting_riders_model.dart';
import 'package:safari_yatri/features/passenger/home/<USER>/rider_request_card_widget.dart';

///Passenger will this which driver accepted my offer
class DriverAcceptedCartOffer extends StatefulWidget {
  const DriverAcceptedCartOffer({super.key, required this.drivers});
  final List<GetMyAcceptingRidersModel> drivers;

  @override
  State<DriverAcceptedCartOffer> createState() =>
      _DriverAcceptedCartOfferState();
}

class _DriverAcceptedCartOfferState extends State<DriverAcceptedCartOffer> {
  late List<GetMyAcceptingRidersModel> drivers;

  @override
  void initState() {
    super.initState();
    drivers = [...widget.drivers];

    // Auto-remove drivers after 10 seconds
    for (var driver in widget.drivers) {
      Future.delayed(const Duration(seconds: 10), () {
        if (mounted) {
          setState(() {
            drivers.removeWhere((d) => d.riderId == driver.riderId);
          });
        }
      });
    }
  }

  void _removeDriver(int riderId) {
    setState(() {
      drivers.removeWhere((d) => d.riderId == riderId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SizedBox(
        height: double.maxFinite,
        width: double.maxFinite,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.topCenter,
              child: SizedBox(
                height: MediaQuery.of(context).size.height * 0.68,
                child: SingleChildScrollView(
                  child: Column(
                    children: drivers.map((driver) {
                      return BlocListener<AcceptRiderRequestBloc, AcceptRiderRequestState>(
                        listener: (context, state) {
                          state.whenOrNull(
                            loading: () => AppLoadingDialog.show(context),
                            loaded: (_) {
                              AppLoadingDialog.hide(context);
                              context.goNamed(
                                AppRoutesName.rideTrackingPageForPassenger,
                                extra: driver.riderId,
                              );
                            },
                            failure: (failure) {
                              AppLoadingDialog.hide(context);
                              CustomToast.showError(failure.message);
                            },
                          );
                        },
                        child: AnimatedDriverCard(
                          key: ValueKey("${driver.riderId}_${driver.riderName}"),
                          driver: driver,
                          onAccept: () {
                            sl<AcceptRiderRequestBloc>().add(
                              AcceptRiderRequestEvent.accept(driver.riderId),
                            );
                          },
                          onDecline: () => _removeDriver(driver.riderId ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
