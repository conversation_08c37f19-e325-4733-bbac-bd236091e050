import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/common/blocs/selected_vehicle_type/selected_vehicle_type_cubit.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/booking/blocs/add_to_cart_booking/add_to_cart_booking_bloc.dart';

import '../../../core/utils/theme_utils.dart';

/// A widget that allows users to adjust a numerical fare value
/// using plus and minus buttons, with visual feedback and animations.
/// It also tracks a 'last confirmed' fare to prevent decreasing below it.
class FareAdjuster extends StatefulWidget {
  final int initialFare;

  final ValueChanged<int> onFareChanged;

  const FareAdjuster({
    super.key,
    required this.initialFare,
    required this.onFareChanged,
  });

  @override
  State<FareAdjuster> createState() => _FareAdjusterState();
}

class _FareAdjusterState extends State<FareAdjuster> {
  late int _currentFare; // Manages the current fare value within this widget
  late int _lastConfirmedFare; // Stores the fare after it's 'raised'
  int _selectedIndex =
      -1; // Tracks which button (if any) was last pressed for visual feedback

  int _negotiationStepValue = 0;
  bool _isLoading = false;
  @override
  void initState() {
    super.initState();
    _currentFare = widget.initialFare;
    _lastConfirmedFare =
        widget
            .initialFare; // Initially, the current fare is the last confirmed one
  }

  /// Sets the current fare as the new 'last confirmed' fare.
  /// This method is called by the parent widget when the 'Raise Fare' button is pressed.
  void confirmCurrentFare() {
    setState(() {
      _lastConfirmedFare = _currentFare;
      _selectedIndex = -1; // Reset button selection after confirmation
    });
  }

  /// Decreases the current fare by 5, ensuring it doesn't go below the last confirmed fare.
  void _decreaseFare() {
    // Only allow decreasing if the new fare would not be less than the last confirmed fare
    if (_currentFare - 5 >= _lastConfirmedFare) {
      setState(() {
        _selectedIndex = 0; // Mark the decrease button as selected
        _currentFare -= _negotiationStepValue;
        widget.onFareChanged(_currentFare); // Notify parent of the change
      });
    } else {
      // Optional: Add a visual shake or a subtle hint that it cannot go lower
      // For now, we just disable the button visually.
    }
  }

  /// Increases the current fare by 5.
  void _increaseFare() {
    setState(() {
      _selectedIndex = 1; // Mark the increase button as selected
      _currentFare += _negotiationStepValue;
      widget.onFareChanged(_currentFare); // Notify parent of the change
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final isDark = T.isDark(context);

    // Determine if the decrease button should be enabled
    final bool canDecrease = _currentFare > _lastConfirmedFare;

    return BlocBuilder<SelectedVehicleTypeCubit, SelectedVehicleTypeState>(
      builder: (context, state) {
        _negotiationStepValue =
            state.vehicleType?.negotiationStepValue.toInt() ?? 10;
        var children = [
          _decreaseButton(canDecrease, textTheme, context),
          _buildPrice(context, isDark),

          // Increase Fare Button
          _increaseButton(textTheme, context),
        ];

        return Column(
          children: [
            _buildYOurOffer(context, isDark),
            const SizedBox(height: AppStyles.space8),

            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: children,
            ),

            const SizedBox(height: AppStyles.space24),
            _buildFareButton(),
          ],
        );
      },
    );
  }

  Center _buildYOurOffer(BuildContext context, bool isDark) {
    return Center(
      child: Text(
        "Your Offer",
        style: T
            .t(context)
            .bodySmall
            ?.copyWith(
              color:
                  isDark
                      ? T.c(context).onSurfaceVariant.withAlpha(179)
                      : T.c(context).onSurfaceVariant.withAlpha(153),
            ),
      ),
    );
  }

  BlocConsumer<AddToCartBookingBloc, AddToCartBookingState> _buildFareButton() {
    return BlocConsumer<AddToCartBookingBloc, AddToCartBookingState>(
      listener: (context, state) {
        state.whenOrNull(
          loading: () {
            setState(() {
              _isLoading = true;
            });
          },
          loaded: (data) {
            setState(() {
              _lastConfirmedFare = _currentFare;
              _isLoading = false;
            });
            if (data.message == null) {
              return;
            }
            CustomToast.showSuccess(
              L.t.rideRequest_raisedFareToast(_currentFare),
            );
          },
        );
      },
      builder: (context, state) {
        _isLoading = state.maybeWhen(orElse: () => false, loading: () => true);
        return CustomButtonPrimary(
          isLoading: _isLoading,
          title: L.t.rideRequest_raiseFare,
          textColor: T.c(context).surfaceContainer,
          onPressed: () {
            sl<AddToCartBookingBloc>().add(
              AddToCartBookingEvent.raiseFare(
                passengerOfferedFare: _currentFare.toDouble(),
              ),
            );
          },
        );
      },
    );
  }

  Row _buildPrice(BuildContext context, bool isDark) {
    return Row(
      children: [
        Text(
          'NPR',
          style: T
              .t(context)
              .displayMedium
              ?.copyWith(
                color:
                    isDark
                        ? T.c(context).onSurfaceVariant.withAlpha(179)
                        : T.c(context).onSurfaceVariant.withAlpha(153),
              ),
        ),
        SizedBox(width: 10),
        // Animated Fare Display
        AnimatedSwitcher(
          duration: const Duration(
            milliseconds: 350,
          ), // Slightly longer for smoother slide
          transitionBuilder: (Widget child, Animation<double> animation) {
            // Determine slide direction based on fare change
            final offsetAnimation = Tween<Offset>(
              begin: const Offset(0.0, 0.5), // Start from slightly below
              end: Offset.zero,
            ).animate(
              CurvedAnimation(
                parent: animation,
                curve: Curves.easeOutCubic, // Modern cubic easing
              ),
            );

            return SlideTransition(
              position: offsetAnimation,
              child: FadeTransition(
                // Combine with fade for elegance
                opacity: animation,
                child: child,
              ),
            );
          },
          child: Text(
            "$_currentFare",
            key: ValueKey<int>(
              _currentFare,
            ), // Key is crucial for AnimatedSwitcher
            style: T
                .t(context)
                .displayMedium
                ?.copyWith(
                  color:
                      isDark
                          ? T.c(context).primary.withAlpha(179)
                          : T.c(context).onSurfaceVariant.withAlpha(153),
                ),
          ),
        ),
      ],
    );
  }

  GestureDetector _decreaseButton(
    bool canDecrease,
    TextTheme textTheme,
    BuildContext context,
  ) {
    return GestureDetector(
      onTap:
          _isLoading
              ? () {}
              : canDecrease
              ? _decreaseFare
              : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeOut,
        height: 55,
        width: 80,
        decoration: BoxDecoration(
          borderRadius: AppStyles.radiusLg,
          color:
              canDecrease
                  ? (_selectedIndex == 0 ? Colors.green[400] : Colors.grey[200])
                  : Colors.grey[350], // Disabled color
          boxShadow:
              canDecrease && _selectedIndex == 0
                  ? [
                    BoxShadow(
                      color: Colors.green.withOpacity(0.4),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ]
                  : [],
        ),
        child: Center(
          child: Text(
            " - $_negotiationStepValue",
            style: textTheme.headlineSmall?.copyWith(
              color:
                  canDecrease
                      ? (_selectedIndex == 0
                          ? Theme.of(context).colorScheme.onPrimary
                          : Colors.black87)
                      : Colors.grey[500], // Disabled text color
            ),
          ),
        ),
      ),
    );
  }

  GestureDetector _increaseButton(TextTheme textTheme, BuildContext context) {
    return GestureDetector(
      onTap: _isLoading ? () {} : _increaseFare,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeOut,
        height: 55,
        width: 80,
        decoration: BoxDecoration(
          borderRadius: AppStyles.radiusLg,
          color: _selectedIndex == 1 ? Colors.green[400] : Colors.grey[200],
          boxShadow:
              _selectedIndex == 1
                  ? [
                    BoxShadow(
                      color: Colors.green.withOpacity(0.4),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ]
                  : [],
        ),
        child: Center(
          child: Text(
            "+ $_negotiationStepValue",
            style: textTheme.headlineSmall?.copyWith(
              color:
                  _selectedIndex == 1
                      ? Theme.of(context).colorScheme.onPrimary
                      : Colors.black87,
            ),
          ),
        ),
      ),
    );
  }
}
