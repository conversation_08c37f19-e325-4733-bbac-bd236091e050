// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'booking_details.dart';

class BookingModel {
  final int bookingId;
  final String bookingDate;
  final String bookingStartDate;
  final bool isSharedBookingMode;
  final int totalDistanceInMeter;
  final int passengerCount;
  final double totalFareAmount;
  final double acceptedFareAmount;
  final String passengerName;
  final String riderName;
  final double? riderCurrentLatitude;
  final double? riderCurrentLongitude;
  final String bookingCancelDate;
  final String cancelUserName;
  final String reasonForCancel;
  final String? serviceStartDate;
  final String? serviceCompleteDate;
  final String paymentStatus;
  final String serviceStatus;
  final String? serviceConfirmationCode;
  final List<BookingDetailModel> bookingDetailViews;

  BookingModel({
    required this.bookingId,
    required this.bookingDate,
    required this.bookingStartDate,
    required this.isSharedBookingMode,
    required this.totalDistanceInMeter,
    required this.passengerCount,
    required this.totalFareAmount,
    required this.acceptedFareAmount,
    required this.passengerName,
    required this.riderName,
    this.riderCurrentLatitude,
    this.riderCurrentLongitude,
    required this.bookingCancelDate,
    required this.cancelUserName,
    required this.reasonForCancel,
    this.serviceStartDate,
    this.serviceCompleteDate,
    required this.paymentStatus,
    required this.serviceStatus,
    this.serviceConfirmationCode,
    required this.bookingDetailViews,
  });

  factory BookingModel.fromMap(Map<String, dynamic> map) {
    return BookingModel(
      bookingId: map['BookingId'] ?? 0,
      bookingDate: map['BookingDate'] ?? '',
      bookingStartDate: map['BookingStartDate'] ?? '',
      isSharedBookingMode: map['IsSharedBookingMode'] ?? false,
      totalDistanceInMeter: map['TotalDistanceInMeter'] ?? 0,
      passengerCount: map['PassengerCount'] ?? 0,
      totalFareAmount: (map['TotalFareAmount'] ?? 0).toDouble(),
      acceptedFareAmount: (map['AcceptedFareAmount'] ?? 0).toDouble(),
      passengerName: map['PassengerName'] ?? '',
      riderName: map['RiderName'] ?? '',
      riderCurrentLatitude: (map['RiderCurrentLatitude'] ?? 0).toDouble(),
      riderCurrentLongitude: (map['RiderCurrentLongitude'] ?? 0).toDouble(),
      bookingCancelDate: map['BookingCancelDate'] ?? '',
      cancelUserName: map['CancelUserName'] ?? '',
      reasonForCancel: map['ReasonForCancel'] ?? '',
      serviceStartDate: map['ServiceStartDate'] ?? '',
      serviceCompleteDate: map['ServiceCompleteDate'] ?? '',
      paymentStatus: map['PaymentStatus'] ?? '',
      serviceStatus: map['ServiceStatus'] ?? '',
      serviceConfirmationCode: map['ServiceConfirmationCode'] ?? '',
      bookingDetailViews:
          (map['BookingDetailViews'] as List<dynamic>?)
              ?.map((e) => BookingDetailModel.fromMap(e))
              .toList() ??
          [],
    );
  }

  BookingModel copyWith({
    int? bookingId,
    String? bookingDate,
    String? bookingStartDate,
    bool? isSharedBookingMode,
    int? totalDistanceInMeter,
    int? passengerCount,
    double? totalFareAmount,
    double? acceptedFareAmount,
    String? passengerName,
    String? riderName,
    double? riderCurrentLatitude,
    double? riderCurrentLongitude,
    String? bookingCancelDate,
    String? cancelUserName,
    String? reasonForCancel,
    String? serviceStartDate,
    String? serviceCompleteDate,
    String? paymentStatus,
    String? serviceStatus,
    String? serviceConfirmationCode,
    List<BookingDetailModel>? bookingDetailViews,
  }) {
    return BookingModel(
      bookingId: bookingId ?? this.bookingId,
      bookingDate: bookingDate ?? this.bookingDate,
      bookingStartDate: bookingStartDate ?? this.bookingStartDate,
      isSharedBookingMode: isSharedBookingMode ?? this.isSharedBookingMode,
      totalDistanceInMeter: totalDistanceInMeter ?? this.totalDistanceInMeter,
      passengerCount: passengerCount ?? this.passengerCount,
      totalFareAmount: totalFareAmount ?? this.totalFareAmount,
      acceptedFareAmount: acceptedFareAmount ?? this.acceptedFareAmount,
      passengerName: passengerName ?? this.passengerName,
      riderName: riderName ?? this.riderName,
      riderCurrentLatitude: riderCurrentLatitude ?? this.riderCurrentLatitude,
      riderCurrentLongitude: riderCurrentLongitude ?? this.riderCurrentLongitude,
      bookingCancelDate: bookingCancelDate ?? this.bookingCancelDate,
      cancelUserName: cancelUserName ?? this.cancelUserName,
      reasonForCancel: reasonForCancel ?? this.reasonForCancel,
      serviceStartDate: serviceStartDate ?? this.serviceStartDate,
      serviceCompleteDate: serviceCompleteDate ?? this.serviceCompleteDate,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      serviceStatus: serviceStatus ?? this.serviceStatus,
      serviceConfirmationCode: serviceConfirmationCode ?? this.serviceConfirmationCode,
      bookingDetailViews: bookingDetailViews ?? this.bookingDetailViews,
    );
  }
}
