import 'package:safari_yatri/features/booking/models/booking_details.dart';

class NewPassengerModel {
  final String bookingStartDate;
  final String bookingStartTime;
  final bool isSharedBookingMode;
  final int totalDistanceInMeter;
  final int passengerCount;
  final double totalFareAmount;
  final double passengerFareAmount;
  final List<BookingDetailModel> cartDetailViews;
  final int passengerId;
  final String passengerName;
  final String passengerPhone;
  final String cartDate;
  final double currentLatitude;
  final double currentLongitude;

  NewPassengerModel({
    required this.bookingStartDate,
    required this.bookingStartTime,
    required this.isSharedBookingMode,
    required this.totalDistanceInMeter,
    required this.passengerCount,
    required this.totalFareAmount,
    required this.passengerFareAmount,
    required this.cartDetailViews,
    required this.passengerId,
    required this.passengerName,
    required this.passengerPhone,
    required this.cartDate,
    required this.currentLatitude,
    required this.currentLongitude,
  });

  factory NewPassengerModel.fromMap(Map<String, dynamic> map) {
    return NewPassengerModel(
      bookingStartDate: map['BookingStartDate'] ?? '',
      bookingStartTime: map['BookingStartTime'] ?? '',
      isSharedBookingMode: map['IsSharedBookingMode'] ?? false,
      totalDistanceInMeter: map['TotalDistanceInMeter'] ?? 0,
      passengerCount: map['PassengerCount'] ?? 0,
      totalFareAmount: (map['TotalFareAmount'] ?? 0).toDouble(),
      passengerFareAmount: (map['PassengerFareAmount'] ?? 0).toDouble(),
      cartDetailViews:
          (map['CartDetailViews'] as List<dynamic>)
              .map((e) => BookingDetailModel.fromMap(e as Map<String, dynamic>))
              .toList(),
      passengerId: map['PassengerId'] ?? 0,
      passengerName: map['PassengerName'] ?? '',
      passengerPhone: map['PassengerPhone'] ?? '',
      cartDate: map['CartDate'] ?? '',
      currentLatitude: (map['CurrentLatitude'] ?? 0).toDouble(),
      currentLongitude: (map['CurrentLongitude'] ?? 0).toDouble(),
    );
  }
}
