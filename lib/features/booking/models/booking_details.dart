class BookingDetailModel {
  final double sourceLatitude;
  final double sourceLongitude;
  final String sourceAddress;
  final String destinationAddress;
  final double destinationLatitude;
  final double destinationLongitude;

  BookingDetailModel({
    required this.sourceAddress,
    required this.destinationAddress,

    required this.sourceLatitude,
    required this.sourceLongitude,
    required this.destinationLatitude,
    required this.destinationLongitude,
  });

  factory BookingDetailModel.fromMap(Map<String, dynamic> map) {
    return BookingDetailModel(
      sourceAddress: map['SourceLocationName'] ?? '',
      destinationAddress: map['DestinationLocationName'] ?? '',

      sourceLatitude: (map['SourceLatitude'] ?? 0).toDouble(),
      sourceLongitude: (map['SourceLongitude'] ?? 0).toDouble(),
      destinationLatitude: (map['DestinationLatitude'] ?? 0).toDouble(),
      destinationLongitude: (map['DestinationLongitude'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'SourceLocationName': sourceAddress,
      'DestinationLocationName': destinationAddress,
      'SourceLatitude': sourceLatitude,
      'SourceLongitude': sourceLongitude,
      'DestinationLatitude': destinationLatitude,
      'DestinationLongitude': destinationLongitude,
    };
  }

  BookingDetailModel copyWith({
    int? detailId,
    double? sourceLatitude,
    double? sourceLongitude,
    double? destinationLatitude,
    double? destinationLongitude,
    double? distanceInMeter,
    String? sourceAddress,
    String? destinationAddress,
  }) {
    return BookingDetailModel(
      sourceAddress: sourceAddress ?? this.sourceAddress,
      destinationAddress: destinationAddress ?? this.destinationAddress,
      sourceLatitude: sourceLatitude ?? this.sourceLatitude,
      sourceLongitude: sourceLongitude ?? this.sourceLongitude,
      destinationLatitude: destinationLatitude ?? this.destinationLatitude,
      destinationLongitude: destinationLongitude ?? this.destinationLongitude,
    );
  }
}
