class RiderViewModel {
  final int riderId;
  final String riderDetail;
  final double fairAmount;

  RiderViewModel({
    required this.riderId,
    required this.riderDetail,
    required this.fairAmount,
  });

  factory RiderViewModel.fromMap(Map<String, dynamic> map) {
    return RiderViewModel(
      riderId: map['RiderId'] ?? 0,
      riderDetail: map['RiderDetail'] ?? '',
      fairAmount: (map['FairAmount'] ?? 0).toDouble(),
    );
  }
}
