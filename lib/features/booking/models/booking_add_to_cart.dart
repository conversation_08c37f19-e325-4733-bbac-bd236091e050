// ignore_for_file: public_member_api_docs, sort_constructors_first
// booking_cart_request.dart
import 'package:safari_yatri/features/booking/models/booking_details.dart';

class BookingAddToCartModel {
  final String? bookingStartDate;
  final String? bookingStartTime;
  final bool isSharedBookingMode;
  final double passengerFareAmount;
  final double systemFareAmount;
  final int passengerCount;
  final int totalDistanceInMeter;
  final List<BookingDetailModel> cartDetailViews;
  final int riderDistanceInMeter;
  BookingAddToCartModel({
    this.riderDistanceInMeter = 0,
    this.totalDistanceInMeter = 0,
    this.bookingStartDate,
    this.bookingStartTime,
    this.isSharedBookingMode = false,
    required this.passengerFareAmount,
    required this.systemFareAmount,
    this.passengerCount = 1,
    this.cartDetailViews = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      "RiderDistanceInMeter": riderDistanceInMeter,
      "TotalDistanceInMeter": totalDistanceInMeter,
      'BookingStartDate': bookingStartDate,
      'BookingStartTime': bookingStartTime,
      'IsSharedBookingMode': isSharedBookingMode,
      'PassengerFareAmount': passengerFareAmount,
      'CartDetailViews': cartDetailViews.map((e) => e.toMap()).toList(),
      'PassengerCount': passengerCount,
      'TotalFareAmount': systemFareAmount,
    };
  }

  factory BookingAddToCartModel.fromMap(Map<String, dynamic> map) {
    return BookingAddToCartModel(
      riderDistanceInMeter: map['RiderDistanceInMeter'],
      totalDistanceInMeter: map['TotalDistanceInMeter'],
      bookingStartDate: map['BookingStartDate'],
      bookingStartTime: map['BookingStartTime'],
      isSharedBookingMode: map['IsSharedBookingMode'] ?? false,
      passengerFareAmount: map['PassengerFareAmount']!,
      systemFareAmount: map['TotalFareAmount']!,
      passengerCount: map['PassengerCount'] ?? 1,
      cartDetailViews:
          (map['CartDetailViews'] as List<dynamic>? ?? [])
              .map((e) => BookingDetailModel.fromMap(e))
              .toList(),
    );
  }

  BookingAddToCartModel copyWith({
    String? bookingStartDate,
    String? bookingStartTime,
    bool? isSharedBookingMode,
    double? passengerFareAmount,
    double? systemFareAmount,
    int? passengerCount,
    List<BookingDetailModel>? cartDetailViews,
    int? riderDistanceInMeter,
  }) {
    return BookingAddToCartModel(
      riderDistanceInMeter: riderDistanceInMeter ?? this.riderDistanceInMeter,
      passengerCount: passengerCount ?? this.passengerCount,
      systemFareAmount: systemFareAmount ?? this.systemFareAmount,
      totalDistanceInMeter: totalDistanceInMeter,
      bookingStartDate: bookingStartDate ?? this.bookingStartDate,
      bookingStartTime: bookingStartTime ?? this.bookingStartTime,
      isSharedBookingMode: isSharedBookingMode ?? this.isSharedBookingMode,
      passengerFareAmount: passengerFareAmount ?? this.passengerFareAmount,
      cartDetailViews: cartDetailViews ?? this.cartDetailViews,
    );
  }
}
