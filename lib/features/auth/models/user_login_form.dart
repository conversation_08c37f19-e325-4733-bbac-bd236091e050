import 'dart:convert';

class UserLoginForm {
  final String loginId;
  final String password;

  UserLoginForm({required this.loginId, required this.password});

  Map<String, dynamic>toMap() {
    return <String, dynamic>{'LoginId': loginId, 'Password': password};
  }

  factory UserLoginForm.fromMap(Map<String, dynamic> map) {
    return UserLoginForm(
      loginId: map['LoginId'] as String,
      password: map['Password'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory UserLoginForm.fromJson(String source) =>
      UserLoginForm.fromMap(json.decode(source) as Map<String, dynamic>);
}
