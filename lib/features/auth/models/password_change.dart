import 'dart:convert';

class Password<PERSON>hangeForm {
  final String password;
  final String newPassword;
  final String retypePassword;

  PasswordChangeForm({
    required this.password,
    required this.newPassword,
    required this.retypePassword,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'Password': password,
      'NewPassword': newPassword,
      'RetypePassword': retypePassword,
    };
  }

  factory PasswordChangeForm.fromMap(Map<String, dynamic> map) {
    return PasswordChangeForm(
      password: map['Password'] as String,
      newPassword: map['NewPassword'] as String,
      retypePassword: map['RetypePassword'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory PasswordChangeForm.fromJson(String source) =>
      PasswordChangeForm.fromMap(json.decode(source) as Map<String, dynamic>);
}
