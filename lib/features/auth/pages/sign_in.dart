import 'dart:developer';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/services/location_permission_status_navigator.dart';
import 'package:safari_yatri/features/auth/blocs/user_login/user_login_bloc.dart';
import 'package:safari_yatri/features/auth/models/user_login_form.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../../core/animations/icon_animation.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../core/services/cache_service.dart';
import '../../../core/utils/input_validator_helper.dart';
import '../../../core/router/app_route_names.dart';
import '../../../core/utils/localization_utils.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_styles.dart';
import '../../../core/utils/theme_utils.dart';
import '../../../core/widget/custom_button.dart';
import '../../../core/widget/custom_form_field.dart';
import '../blocs/password_toggle_cubit/password_toggle_cubit.dart';
import '../widgets/applogo.dart';
import '../widgets/footer.dart';

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  @override
  State<SignInPage> createState() => _SignInPageState();
}

class _SignInPageState extends State<SignInPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _rememberMe = false;

  @override
  void initState() {
    super.initState();
    _loadRememberMeCredentials();
  }

  Future<void> _loadRememberMeCredentials() async {
    try {
      final hasCredentials =
          await CacheService.instance.hasRememberMeCredentials();
      if (hasCredentials) {
        final credentials =
            await CacheService.instance.getRememberMeCredentials();
        setState(() {
          _rememberMe = true;
          _phoneController.text = credentials['phone'] ?? '';
          _passwordController.text = credentials['password'] ?? '';
        });
      }
    } catch (e) {
      log('Error loading remember me credentials: $e');
    }
  }

  /// Handle remember me functionality
  Future<void> _handleRememberMe() async {
    if (_rememberMe) {
      // Save credentials
      await CacheService.instance.setRememberMeCredentials(
        phone: _phoneController.text,
        password: _passwordController.text,
      );
    } else {
      // Clear credentials
      await CacheService.instance.clearRememberMeCredentials();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(AppStyles.space12),
          child: Column(
            spacing: AppStyles.space12,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: AppStyles.space12,
                  children: [
                    AppHeader(
                      showBackButton: false,
                      appHeaderTitle: L.t.loginScreenAppHeaderTitle,
                      appHeaderSubTitle: L.t.loginScreenAppHeaderSubTitle,
                    ),
                    CustomFormFieldWidget(
                      prefixIcon: LucideIcons.phone,
                      label: L.t.loginScreenPhoneNumberFormLabel,
                      controller: _phoneController,
                      keys: TextInputType.numberWithOptions(),
                      validator: InputValidator.validatePhone,
                    ),
                    BlocBuilder<PasswordToggleCubit, PasswordToggleState>(
                      builder: (context, state) {
                        return CustomFormFieldWidget(
                          prefixIcon: LucideIcons.lock,
                          label: L.t.loginScreenPasswordFormLabel,
                          controller: _passwordController,
                          keys: TextInputType.visiblePassword,
                          obscureText: state.isToggle,
                          validator: InputValidator.validatePassword,
                          suffixWidget: AnimatedIconToggle(
                            icon1: LucideIcons.eye,
                            icon2: LucideIcons.eyeClosed,
                            onToggle: () {
                              sl<PasswordToggleCubit>().passwordToggle();
                            },
                          ),
                        );
                      },
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            ShadCheckbox(
                              value: _rememberMe,
                              onChanged: (value) {
                                setState(() {
                                  _rememberMe = value;
                                });
                                if (!value) {
                                  // Clear credentials immediately when unchecked
                                  CacheService.instance
                                      .clearRememberMeCredentials();
                                }
                              },
                              color: T.c(context).primary,
                              decoration: ShadDecoration(
                                border: ShadBorder.all(
                                  color: T.c(context).primary,
                                ),
                              ),
                            ),
                            SizedBox(width: AppStyles.space8),
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  _rememberMe = !_rememberMe;
                                });
                                if (!_rememberMe) {
                                  CacheService.instance
                                      .clearRememberMeCredentials();
                                }
                              },
                              child: Text(
                                L.t.loginScreenRememberMeLabel,
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(fontWeight: FontWeight.w500),
                              ),
                            ),
                          ],
                        ),
                        InkWell(
                          onTap: () {
                            context.pushNamed(AppRoutesName.forgetPassword);
                          },
                          child: Text(
                            L.t.loginScreenForgetPasswordBtnTextLabel,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 15,
                              color: AppColors.lightError,
                            ),
                          ),
                        ),
                      ],
                    ),
                    BlocConsumer<UserLoginBloc, UserLoginState>(
                      listener: (context, state) {
                        state.whenOrNull(
                          loaded: (data) async {
                            await _handleRememberMe();
                            CustomToast.showSuccess(
                              L.t.loginScreenSucessToastLabel,
                            );
                            locationPermissionStatusNavigator(
                              onDenied: () {
                                context.goNamed(
                                  AppRoutesName.locationPermissionHandler,
                                );
                              },
                              onGranted: () {
                                context.goNamed(
                                  AppRoutesName.roleBaseNavigatorPage,
                                );
                              },
                            );
                          },

                          failure: (failure) {
                            if (failure.message == "Login not active yet.") {
                              CustomToast.showError(
                                L.t.loginScreenErrorToastLoginNotActiveLabel,
                              );
                              context.goNamed(AppRoutesName.verifyOpt);
                              return;
                            }
                            CustomToast.showError(failure.message);
                          },
                        );
                      },
                      builder: (context, state) {
                        final bool isLoading = state.maybeWhen(
                          loading: () => true,
                          orElse: () => false,
                        );
                        return CustomButtonPrimary(
                          title: L.t.loginScreenButtonTitle,
                          isLoading: isLoading,
                          onPressed:
                              isLoading
                                  ? () {}
                                  : () {
                                    if (_formKey.currentState!.validate()) {
                                      sl<UserLoginBloc>().add(
                                        UserLoginEvent.singIn(
                                          UserLoginForm(
                                            loginId: _phoneController.text,
                                            password: _passwordController.text,
                                          ),
                                        ),
                                      );
                                    }
                                  },
                        );
                      },
                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: RichText(
                        text: TextSpan(
                          text: L.t.loginScreenDontHaveAnAccount,
                          style: Theme.of(context).textTheme.titleSmall,
                          children: [
                            TextSpan(
                              text: L.t.loginScreenSignUpButtonTitle,
                              style: Theme.of(
                                context,
                              ).textTheme.titleSmall?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                              recognizer:
                                  TapGestureRecognizer()
                                    ..onTap = () {
                                      context.pushNamed(AppRoutesName.signUp);
                                    },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // SizedBox(height: MediaQuery.of(context).size.height / 2.3),
              FooterText(),
            ],
          ),
        ),
      ),
    );
  }
}
