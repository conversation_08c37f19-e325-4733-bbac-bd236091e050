import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import 'package:safari_yatri/features/auth/blocs/change_password/change_password_bloc.dart';
import 'package:safari_yatri/features/auth/models/password_change.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
// import '../../../core/utils/input_validator_helper.dart';
import '../../../core/widget/custom_button.dart';
import '../../../core/widget/custom_form_field.dart';

class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({super.key});

  @override
  State<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _newConfirmPasswordController = TextEditingController();

  @override
  void dispose() {
    _passwordController.dispose();
    _newPasswordController.dispose();
    _newConfirmPasswordController.dispose();
    super.dispose();
  }

  String? _validateRequired(String? value) {
    if (value == null || value.isEmpty) {
      return 'This field is required';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'New Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters long';
    }

    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Confirm Password is required';
    }
    if (value != _newPasswordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: Text('Change Password')),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: AppStyles.space8,
          vertical: AppStyles.space12,
        ),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              CustomPasswordFormFieldWidget(
                prefixIcon: LucideIcons.lock,
                label: 'Current Password',
                controller: _passwordController,
                validator: _validateRequired,
                keys: TextInputType.visiblePassword,
              ),
              const SizedBox(height: AppStyles.space12),
              CustomPasswordFormFieldWidget(
                prefixIcon: LucideIcons.lock,
                label: 'New Password',
                controller: _newPasswordController,
                keys: TextInputType.visiblePassword,
                validator: _validatePassword,
              ),
              const SizedBox(height: AppStyles.space12),
              CustomPasswordFormFieldWidget(
                prefixIcon: LucideIcons.lock,
                label: 'Confirm New Password',
                controller: _newConfirmPasswordController,
                validator: _validateConfirmPassword,
                keys: TextInputType.visiblePassword,
              ),
              const SizedBox(height: AppStyles.space12),

              BlocConsumer<ChangePasswordBloc, ChangePasswordState>(
                listener: (context, state) {
                  state.whenOrNull(
                    loaded: (data) {
                      context.pop();
                      CustomToast.showSuccess("Password updated successfully!");
                    },
                    failure: (failure) {
                      CustomToast.showError(failure.message);
                    },
                  );
                },
                builder: (context, state) {
                  final bool isLoading = state.maybeWhen(
                    loading: () => true,
                    orElse: () => false,
                  );
                  return CustomButtonPrimary(
                    title: 'Update Password',
                    isLoading: isLoading,
                    onPressed:
                        isLoading
                            ? null
                            : () {
                              if (_formKey.currentState!.validate()) {
                                sl<ChangePasswordBloc>().add(
                                  ChangePasswordEvent.passwordChange(
                                    PasswordChangeForm(
                                      password: _passwordController.text,
                                      newPassword: _newPasswordController.text,
                                      retypePassword:
                                          _newConfirmPasswordController.text,
                                    ),
                                  ),
                                );
                              }
                            },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
