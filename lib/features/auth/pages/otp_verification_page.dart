import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/common/blocs/local_user_mode/local_user_mode_cubit.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/services/location_permission_status_navigator.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_office/get_office_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/resend_otp/resend_opt_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/verify_otp/verify_otp_bloc.dart';
import '../../../core/utils/localization_utils.dart';
import '../../../core/theme/app_colors.dart';
import '../widgets/applogo.dart';
import 'package:pinput/pinput.dart';

class OTPVerificationPage extends StatefulWidget {
  const OTPVerificationPage({super.key});

  @override
  State<OTPVerificationPage> createState() => _OTPVerificationPageState();
}

class _OTPVerificationPageState extends State<OTPVerificationPage> {
  // String? _code;
  Timer? _timer;
  int _secondsRemaining = 180;
  bool _canResend = false;

  final controller = TextEditingController();
  final focusNode = FocusNode();

  bool showError = false;
  bool _autoLoginAfterOtpVerification = true;

  // @override
  // initState() {
  //   super.initState();
  //   sl<GetOfficeBloc>().add(const GetOfficeEvent.get());
  // }

  @override
  didChangeDependencies() {
    super.didChangeDependencies();
    startTimer();
  }

  void startTimer() {
    _canResend = false;
    final getOfficeState = sl<GetOfficeBloc>().state;
    _secondsRemaining = getOfficeState.maybeWhen(
      loaded: (data) {
        _autoLoginAfterOtpVerification = data.autoLoginAfterOtpVerification;
        return data.otpLifeInSecond;
      },
      orElse: () => 180,
    );

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_secondsRemaining == 0) {
        timer.cancel();
        setState(() {
          _canResend = true;
        });
      } else {
        setState(() {
          _secondsRemaining--;
        });
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    controller.dispose();
    focusNode.dispose();
    super.dispose();
  }

  void _resendOtp() {
    sl<ResendOptBloc>().add(ResendOptEvent.resend());
    setState(() => showError = false);
    _timer?.cancel();
    controller.clear();
    focusNode.unfocus();
    startTimer();
  }

  @override
  Widget build(BuildContext context) {
    const length = 4;
    const borderColor = Color.fromRGBO(114, 178, 238, 1);
    const fillColor = Color.fromRGBO(222, 231, 240, .57);
    final defaultPinTheme = PinTheme(
      width: 56,
      height: 60,

      decoration: BoxDecoration(
        color: fillColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.transparent),
      ),
    );
    final errorPinTheme = PinTheme(
      width: 56,
      height: 60,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.darkError, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
    );
    return BlocListener<VerifyOtpBloc, VerifyOtpState>(
      listener: (context, state) {
        state.whenOrNull(
          loading: () async => await AppLoadingDialog.show(context),
          loaded: (data) {
            CustomToast.showSuccess(data);
            AppLoadingDialog.hide(context);
            sl<LocalUserModeCubit>().switchPassengerMode();

            ///Hamile chaii auto login garney nagareny ta after
            ///otp verification if false vayo vane
            ///ferii login garna lagaune
            if (!_autoLoginAfterOtpVerification) {
              CustomToast.showInfo(L.t.verifyOtpScreenPleaseLoginToast);
              context.goNamed(AppRoutesName.signIn);
              return;
            }

            locationPermissionStatusNavigator(
              onDenied: () {
                context.goNamed(AppRoutesName.locationPermissionHandler);
              },
              onGranted: () {
                context.goNamed(AppRoutesName.roleBaseNavigatorPage);
              },
            );
          },
          failure: (failure) {
            AppLoadingDialog.hide(context);
            CustomToast.showError(failure.message);
          },
        );
      },
      child: Scaffold(
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppHeader(
                  appHeaderTitle: L.t.verifyOtpScreenAppHeaderTitle,
                  appHeaderSubTitle: L.t.verifyOtpScreenAppHeaderSubTitle,
                ),
                const SizedBox(height: AppStyles.space64),
                SizedBox(
                  height: 68,
                  child: Pinput(
                    length: length,
                    controller: controller,
                    focusNode: focusNode,
                    defaultPinTheme:
                        showError ? errorPinTheme : defaultPinTheme,
                    onCompleted: (pin) {
                      // dLog.d("local _cdeo $_code");
                      // final isValid = pin == _code;

                      sl<VerifyOtpBloc>().add(VerifyOtpEvent.verifyOtp(pin));
                    },
                    focusedPinTheme: defaultPinTheme.copyWith(
                      height: 68,
                      width: 64,
                      decoration: defaultPinTheme.decoration!.copyWith(
                        border: Border.all(color: borderColor),
                      ),
                    ),
                    errorPinTheme: defaultPinTheme.copyWith(
                      decoration: BoxDecoration(
                        color: AppColors.darkError,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                BlocListener<ResendOptBloc, ResendOptState>(
                  listener: (context, state) {
                    state.whenOrNull(
                      loaded: (data) {
                        setState(() => showError = false);

                        CustomToast.showInfo(
                          L.t.verifyOtpScreenResendSuccessToast,
                        );
                      },
                      failure: (failure) {
                        CustomToast.showError(failure.message);
                        setState(() => showError = true);
                      },
                    );
                  },
                  child:
                      _canResend
                          ? CustomButtonPrimary(
                            height: 40,
                            width: 150,
                            title: L.t.verifyOtpScreenResendButtonText,
                            onPressed: _resendOtp,
                          )
                          : Text(
                            L.t.verifyOtpScreenResendCountdownText(
                              _secondsRemaining,
                            ),
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(color: Colors.grey),
                          ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
