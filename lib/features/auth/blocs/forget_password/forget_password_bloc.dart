import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/auth/repositories/auth_repository.dart';
part 'forget_password_event.dart';
part 'forget_password_state.dart';
part 'forget_password_bloc.freezed.dart';

class ForgetPasswordBloc
    extends Bloc<ForgetPasswordEvent, ForgetPasswordState> {
  final AuthRepository _authRepository;
  ForgetPasswordBloc({required AuthRepository repo})
    : _authRepository = repo,
      super(ForgetPasswordState.initial()) {
    on<_ForgetPassword>(onForgetPassword);
  }

  Future<void> onForgetPassword(
    _ForgetPassword event,
    Emitter<ForgetPasswordState> emit,
  ) async {
    emit(ForgetPasswordState.loading());

    final failureOrSuccess = await _authRepository.forgetPassword(
      phoneNumber: event.phoneNumber.trim(),
    );
    failureOrSuccess.fold(
      (failure) => emit(ForgetPasswordState.failure(failure)),
      (data) => (emit(ForgetPasswordState.loaded(data))),
    );
  }
}
