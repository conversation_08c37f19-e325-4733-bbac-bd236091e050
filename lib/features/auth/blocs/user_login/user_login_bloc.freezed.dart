// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_login_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserLoginEvent {
  UserLoginForm get userForm;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserLoginEvent &&
            (identical(other.userForm, userForm) ||
                other.userForm == userForm));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userForm);

  @override
  String toString() {
    return 'UserLoginEvent(userForm: $userForm)';
  }
}

/// Adds pattern-matching-related methods to [UserLoginEvent].
extension UserLoginEventPatterns on UserLoginEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SignIn value)? singIn,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SignIn() when singIn != null:
        return singIn(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SignIn value) singIn,
  }) {
    final _that = this;
    switch (_that) {
      case _SignIn():
        return singIn(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SignIn value)? singIn,
  }) {
    final _that = this;
    switch (_that) {
      case _SignIn() when singIn != null:
        return singIn(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(UserLoginForm userForm)? singIn,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SignIn() when singIn != null:
        return singIn(_that.userForm);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(UserLoginForm userForm) singIn,
  }) {
    final _that = this;
    switch (_that) {
      case _SignIn():
        return singIn(_that.userForm);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(UserLoginForm userForm)? singIn,
  }) {
    final _that = this;
    switch (_that) {
      case _SignIn() when singIn != null:
        return singIn(_that.userForm);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _SignIn implements UserLoginEvent {
  const _SignIn(this.userForm);

  @override
  final UserLoginForm userForm;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SignIn &&
            (identical(other.userForm, userForm) ||
                other.userForm == userForm));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userForm);

  @override
  String toString() {
    return 'UserLoginEvent.singIn(userForm: $userForm)';
  }
}
