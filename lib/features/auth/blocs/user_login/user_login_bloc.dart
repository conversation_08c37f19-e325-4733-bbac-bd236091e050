import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/auth/models/user_login_form.dart';
import 'package:safari_yatri/features/auth/repositories/auth_repository.dart';
part 'user_login_event.dart';
part 'user_login_state.dart';
part 'user_login_bloc.freezed.dart';

class UserLoginBloc extends Bloc<UserLoginEvent, UserLoginState> {
  final AuthRepository _authRepository;
  // int count = 0;
  UserLoginBloc({required AuthRepository repo})
    : _authRepository = repo,
      super(UserLoginState.initial()) {
    on<_SignIn>(_onSignIn);
  }

  Future<void> _onSignIn(_SignIn event, Emitter<UserLoginState> emit) async {
    emit(UserLoginState.loading());
    // count++;
    // dLog.d("*********** Calling $count timese");
    final result = await _authRepository.signIn(userLoginForm: event.userForm);

    // final result = await  Future.delayed((Duration(milliseconds: 1500))).then((_) {
    //   return Left(TestFailure(message: "dfdsfds"));
    // });

    result.fold(
      (failure) => emit(UserLoginState.failure(failure)),
      (data) => emit(UserLoginState.loaded(data)),
    );
  }
}
