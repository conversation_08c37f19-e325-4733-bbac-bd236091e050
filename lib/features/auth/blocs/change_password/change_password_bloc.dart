import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/auth/models/password_change.dart';
import 'package:safari_yatri/features/auth/repositories/auth_repository.dart';
part 'change_password_event.dart';
part 'change_password_state.dart';
part 'change_password_bloc.freezed.dart';

class ChangePasswordBloc
    extends Bloc<ChangePasswordEvent, ChangePasswordState> {
  final AuthRepository _authRepository;

  ChangePasswordBloc({required AuthRepository repo})
    : _authRepository = repo,
      super(ChangePasswordState.initial()) {
    on<_PasswordChange>(_onPasswordChange);
  }

  Future<void> _onPasswordChange(
    _PasswordChange event,
    Emitter<ChangePasswordState> emit,
  ) async {
    emit(ChangePasswordState.loading());

    final result = await _authRepository.changePassword(
      passwordChangeForm: event.passwordForm,
    );

    result.fold(
      (failure) => emit(ChangePasswordState.failure(failure)),
      (data) => emit(ChangePasswordState.loaded(data)),
    );
  }
}
