// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'change_password_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChangePasswordEvent {
  PasswordChangeForm get passwordForm;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ChangePasswordEvent &&
            (identical(other.passwordForm, passwordForm) ||
                other.passwordForm == passwordForm));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passwordForm);

  @override
  String toString() {
    return 'ChangePasswordEvent(passwordForm: $passwordForm)';
  }
}

/// Adds pattern-matching-related methods to [ChangePasswordEvent].
extension ChangePasswordEventPatterns on ChangePasswordEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PasswordChange value)? passwordChange,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PasswordChange() when passwordChange != null:
        return passwordChange(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PasswordChange value) passwordChange,
  }) {
    final _that = this;
    switch (_that) {
      case _PasswordChange():
        return passwordChange(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PasswordChange value)? passwordChange,
  }) {
    final _that = this;
    switch (_that) {
      case _PasswordChange() when passwordChange != null:
        return passwordChange(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PasswordChangeForm passwordForm)? passwordChange,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PasswordChange() when passwordChange != null:
        return passwordChange(_that.passwordForm);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(PasswordChangeForm passwordForm) passwordChange,
  }) {
    final _that = this;
    switch (_that) {
      case _PasswordChange():
        return passwordChange(_that.passwordForm);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(PasswordChangeForm passwordForm)? passwordChange,
  }) {
    final _that = this;
    switch (_that) {
      case _PasswordChange() when passwordChange != null:
        return passwordChange(_that.passwordForm);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _PasswordChange implements ChangePasswordEvent {
  const _PasswordChange(this.passwordForm);

  @override
  final PasswordChangeForm passwordForm;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PasswordChange &&
            (identical(other.passwordForm, passwordForm) ||
                other.passwordForm == passwordForm));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passwordForm);

  @override
  String toString() {
    return 'ChangePasswordEvent.passwordChange(passwordForm: $passwordForm)';
  }
}
