import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import '../../repositories/auth_repository.dart';
part 'resend_opt_event.dart';
part 'resend_opt_state.dart';
part 'resend_opt_bloc.freezed.dart';

class ResendOptBloc extends Bloc<ResendOptEvent, ResendOptState> {
  final AuthRepository _authRepository;

  ResendOptBloc({required AuthRepository repo})
    : _authRepository = repo,
      super(const ResendOptState.initial()) {
    on<_ResendOpt>(onResendOtp);
  }
  Future<void> onResendOtp(
    _ResendOpt event,
    Emitter<ResendOptState> emit,
  ) async {
    emit(ResendOptState.loading());
    final failureOrSucess = await _authRepository.resendOTP();
    failureOrSucess.fold(
      (failure) => emit(ResendOptState.failure(failure)),
      (data) => (emit(ResendOptState.loaded(data))),
    );
  }
}
