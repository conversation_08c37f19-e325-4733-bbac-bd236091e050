import 'package:flutter/material.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import '../../../core/utils/localization_utils.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/widget/custom_pop_button.dart';

class AppHeader extends StatelessWidget {
  final String appHeaderTitle;
  final String appHeaderSubTitle;
  final bool showBackButton;
  const AppHeader({
    super.key,
    required this.appHeaderTitle,
    required this.appHeaderSubTitle,
    this.showBackButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: AppStyles.space4,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (showBackButton) CustomBackButton(),
            Text(
              L.t.appname,
              style: TextStyle(
                color: AppColors.darkPrimary,
                fontSize: 28,
                fontWeight: FontWeight.bold,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: AppStyles.space12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                appHeaderTitle,
                style: Theme.of(context).textTheme.headlineLarge,
              ),
              Text(
                appHeaderSubTitle,
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(color: Colors.grey),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
