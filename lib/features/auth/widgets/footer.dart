import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import '../../../core/utils/localization_utils.dart';
import '../../../core/theme/app_colors.dart';

class FooterText extends StatelessWidget {
  const FooterText({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              fontSize: 12,
              color: ColorScheme.of(context).onSurface,
              height: 1.5,
            ),
            children: [
              TextSpan(text: L.t.footerWidgetTextSpanByContinue),
              TextSpan(
                text: L.t.footerWidgetTextSpanTermsOfServices,
                style: const TextStyle(
                  color: AppColors.darkPrimary,

                  fontWeight: FontWeight.w500,
                ),
                recognizer:
                    TapGestureRecognizer()
                      ..onTap = () {
                        // Navigate to Terms of Service
                      },
              ),
              TextSpan(text: L.t.footerWidgetTextSpanAnd),
              TextSpan(
                text: L.t.footerWidgetTextSpanPrivacyPolicy,
                style: const TextStyle(
                  color: AppColors.darkPrimary,

                  fontWeight: FontWeight.w500,
                ),
                recognizer:
                    TapGestureRecognizer()
                      ..onTap = () {
                        // Navigate to Privacy Policy
                      },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
