import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/features/role/blocs/get_user_role/get_user_role_bloc.dart';

class RemoteUserRoleWidget extends StatelessWidget {
  const RemoteUserRoleWidget({
    super.key,
    this.passengerChild,
    this.riderChild,
    this.adminChild,
  });
  final Widget? passengerChild;
  final Widget? riderChild;
  final Widget? adminChild;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GetRemoteUserRoleBloc, GetRemoteUserRoleState>(
      builder: (context, state) {
        return state.maybeWhen(
          loaded: (role) {
            if (role.toLowerCase() == 'passenger' && passengerChild != null) {
              return passengerChild!;
            } else if (role.toLowerCase() == 'rider' && riderChild != null) {
              return riderChild!;
            } else if (role.toLowerCase() == 'admin' && adminChild != null) {
              return adminChild!;
            } else {
              return const SizedBox();
            }
          },
          orElse: () => const SizedBox(),
        );
      },
    );
  }
}
