import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/role/repositories/role_repository.dart';

part 'get_user_role_event.dart';
part 'get_user_role_state.dart';
part 'get_user_role_bloc.freezed.dart';

//IT will fetch user role from server
class GetRemoteUserRoleBloc
    extends Bloc<GetRemoteUserRoleEvent, GetRemoteUserRoleState> {
  final RoleRepository _roleRepository;
  GetRemoteUserRoleBloc({required RoleRepository roleRepository})
    : _roleRepository = roleRepository,
      super(const GetRemoteUserRoleState.initial()) {
    on<_Get>(_onGet);
  }

  Future<void> _onGet(_Get event, Emitter<GetRemoteUserRoleState> emit) async {
    emit(const GetRemoteUserRoleState.loading());

    final result = await _roleRepository.getMyRole();

    result.fold(
      (failure) => emit(GetRemoteUserRoleState.failure(failure)),
      (role) => emit(GetRemoteUserRoleState.loaded(role)),
    );
  }
}
