import 'package:dartz/dartz.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/network/api_service.dart';
import 'package:safari_yatri/features/role/services/remote_role_service.dart';

abstract interface class RoleRepository {
  FutureEither<String> getMyRole();
}

class RoleRepositoryI implements RoleRepository {
  final ApiService _apiService;
  RoleRepositoryI({required ApiService apiService}) : _apiService = apiService;

  @override
  FutureEither<String> getMyRole() async {
    final failureOrRoles = await _apiService.get<List>("MyProfile/GetMyRoles");
    return failureOrRoles.fold((failureOrRoles) => Left(failureOrRoles), (
      data,
    ) async {
      final role = data[0];
      await RemoteRoleService.instance.setUserRole(role);
      return Right(role);
    });
  }
}
