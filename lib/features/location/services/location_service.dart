// import 'package:flutter/foundation.dart';
// import 'package:geolocator/geolocator.dart';

// class LocationService {
//   final GeolocatorPlatform _geolocator;
//   LocationService({required GeolocatorPlatform geolocator})
//     : _geolocator = geolocator;

//   Future<bool> isLocationServiceEnabled() async {
//     return await _geolocator.isLocationServiceEnabled();
//   }

//   Future<LocationPermission> checkPermission() async {
//     return await _geolocator.checkPermission();
//   }

//   Future<LocationPermission> requestPermission() async {
//     return await _geolocator.requestPermission();
//   }

//   Future<Position?> getLastKnownPosition() async {
//     try {
//       return await _geolocator.getLastKnownPosition();
//     } catch (e) {
//       // Handle exceptions, e.g., if no last known position is available.
//       print('Error getting last known position: $e');
//       return null;
//     }
//   }

//   Future<Position> getCurrentPosition({
//     LocationAccuracy desiredAccuracy = LocationAccuracy.high,
//     bool forceAndroidLocationManager = false, // Useful in some scenarios
//     Duration? timeLimit,
//   }) async {
//     // Ensure service is enabled and permissions are granted before calling this
//     return await _geolocator.getCurrentPosition(
//       locationSettings: _getLocationSettings(
//         desiredAccuracy: desiredAccuracy,
//         forceAndroidLocationManager: forceAndroidLocationManager,
//         timeLimit: timeLimit,
//       ),
//     );
//   }

//   Stream<Position> getPositionStream({
//     LocationAccuracy desiredAccuracy = LocationAccuracy.high,
//     int distanceFilter = 0, // Meters
//     bool forceAndroidLocationManager = false,
//     Duration? timeLimit,
//     Duration intervalDuration = const Duration(seconds: 1), // Android specific
//   }) {
//     final locationSettings = _getLocationSettings(
//       desiredAccuracy: desiredAccuracy,
//       distanceFilter: distanceFilter,
//       forceAndroidLocationManager: forceAndroidLocationManager,
//       timeLimit: timeLimit,
//       intervalDuration: intervalDuration,
//     );
//     return _geolocator.getPositionStream(locationSettings: locationSettings);
//   }

//   Stream<ServiceStatus> getServiceStatusStream() {
//     return _geolocator.getServiceStatusStream();
//   }

//   // Helper to create LocationSettings based on platform
//   LocationSettings _getLocationSettings({
//     LocationAccuracy desiredAccuracy = LocationAccuracy.high,
//     int distanceFilter = 0,
//     bool forceAndroidLocationManager = false,
//     Duration? timeLimit,
//     Duration intervalDuration = const Duration(seconds: 3),
//   }) {
//     if (defaultTargetPlatform == TargetPlatform.android) {
//       return AndroidSettings(
//         accuracy: desiredAccuracy,
//         distanceFilter: distanceFilter,
//         forceLocationManager: forceAndroidLocationManager,
//         intervalDuration: intervalDuration, // How often to check location
//         // foregroundNotificationConfig: ForegroundNotificationConfig( // For background
//         //   notificationText: "Ride Sharing App is using your location to provide services.",
//         //   notificationTitle: "Location in use",
//         //   enableWakeLock: true, // Keep CPU awake
//         //   setOngoing: true, // Make notification non-dismissable
//         // )
//       );
//     } else if (defaultTargetPlatform == TargetPlatform.iOS ||
//         defaultTargetPlatform == TargetPlatform.macOS) {
//       return AppleSettings(
//         accuracy: desiredAccuracy,
//         activityType: ActivityType.automotiveNavigation, // Crucial for iOS
//         distanceFilter: distanceFilter,
//         pauseLocationUpdatesAutomatically:
//             false, // Important for continuous tracking
//         showBackgroundLocationIndicator: true, // Inform user
//       );
//     } else {
//       return LocationSettings(
//         // Generic
//         accuracy: desiredAccuracy,
//         distanceFilter: distanceFilter,
//       );
//     }
//   }

//   // --- Advanced Features ---
//   Future<bool> openAppSettings() async {
//     return await _geolocator.openAppSettings();
//   }

//   Future<bool> openLocationSettings() async {
//     return await _geolocator.openLocationSettings();
//   }
// }
