// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'set_choosed_location_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SetChoosedLocationEvent {
  LatLng get setHomeLocation;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SetChoosedLocationEvent &&
            (identical(other.setHomeLocation, setHomeLocation) ||
                other.setHomeLocation == setHomeLocation));
  }

  @override
  int get hashCode => Object.hash(runtimeType, setHomeLocation);

  @override
  String toString() {
    return 'SetChoosedLocationEvent(setHomeLocation: $setHomeLocation)';
  }
}

/// Adds pattern-matching-related methods to [SetChoosedLocationEvent].
extension SetChoosedLocationEventPatterns on SetChoosedLocationEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetHomeLocation value)? setHomeLocation,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SetHomeLocation() when setHomeLocation != null:
        return setHomeLocation(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetHomeLocation value) setHomeLocation,
  }) {
    final _that = this;
    switch (_that) {
      case _SetHomeLocation():
        return setHomeLocation(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetHomeLocation value)? setHomeLocation,
  }) {
    final _that = this;
    switch (_that) {
      case _SetHomeLocation() when setHomeLocation != null:
        return setHomeLocation(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(LatLng setHomeLocation)? setHomeLocation,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SetHomeLocation() when setHomeLocation != null:
        return setHomeLocation(_that.setHomeLocation);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(LatLng setHomeLocation) setHomeLocation,
  }) {
    final _that = this;
    switch (_that) {
      case _SetHomeLocation():
        return setHomeLocation(_that.setHomeLocation);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(LatLng setHomeLocation)? setHomeLocation,
  }) {
    final _that = this;
    switch (_that) {
      case _SetHomeLocation() when setHomeLocation != null:
        return setHomeLocation(_that.setHomeLocation);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _SetHomeLocation implements SetChoosedLocationEvent {
  const _SetHomeLocation(this.setHomeLocation);

  @override
  final LatLng setHomeLocation;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SetHomeLocation &&
            (identical(other.setHomeLocation, setHomeLocation) ||
                other.setHomeLocation == setHomeLocation));
  }

  @override
  int get hashCode => Object.hash(runtimeType, setHomeLocation);

  @override
  String toString() {
    return 'SetChoosedLocationEvent.setHomeLocation(setHomeLocation: $setHomeLocation)';
  }
}
