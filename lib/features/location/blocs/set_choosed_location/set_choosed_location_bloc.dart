import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/my_profile/repositories/my_profile_repository.dart';

part 'set_choosed_location_event.dart';
part 'set_choosed_location_state.dart';
part 'set_choosed_location_bloc.freezed.dart';

class SetChoosedLocationBloc
    extends Bloc<SetChoosedLocationEvent, SetChoosedLocationState> {
  final MyProfileRepository _myProfileRepository;

  SetChoosedLocationBloc({required MyProfileRepository repo})
    : _myProfileRepository = repo,
      super(const SetChoosedLocationState.initial()) {
    on<_SetHomeLocation>(_onSetHomeLocation);
  }

  Future<void> _onSetHomeLocation(
    _SetHomeLocation event,
    Emitter<SetChoosedLocationState> emit,
  ) async {
    emit(const SetChoosedLocationState.loading());

    final result = await _myProfileRepository.setHomeLocation(
      lat: event.setHomeLocation.latitude,
      lng: event.setHomeLocation.longitude,
    );

    result.fold(
      (failure) => emit(SetChoosedLocationState.failure(failure)),
      (data) => emit(SetChoosedLocationState.loaded(data)),
    );
  }
}
