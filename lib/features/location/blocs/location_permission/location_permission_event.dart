part of 'location_permission_bloc.dart';

@freezed
class LocationPermissionEvent with _$LocationPermissionEvent {
  const factory LocationPermissionEvent.checkPermissionStatus() =
      _CheckPermissionStatus;

  const factory LocationPermissionEvent.getCurrentGrantedPermission() =
      _GetCurrentGrantedPermission;

  const factory LocationPermissionEvent.isLocationServiceEnable() =
      _IsLocationServiceEnable;

  const factory LocationPermissionEvent.checkAndRequestPermission() =
      _CheckAndRequestPermission;

  const factory LocationPermissionEvent.askBackgroundPermission() =
      _AskBackgroundPermission;

  const factory LocationPermissionEvent.openAppSettings() = _OpenAppSettings;
  const factory LocationPermissionEvent.openDeviceLocationSettings() =
      _OpenDeviceLocationSettings;
}

