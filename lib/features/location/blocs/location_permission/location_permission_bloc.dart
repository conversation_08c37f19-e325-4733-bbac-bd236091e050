import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/core/utils/location_permission_status_failure_mapper.dart';
import 'package:safari_yatri/features/location/repositories/location_repository.dart';
part 'location_permission_event.dart';
part 'location_permission_state.dart';
part 'location_permission_bloc.freezed.dart';

class LocationPermissionBloc
    extends Bloc<LocationPermissionEvent, LocationPermissionState> {
  final LocationRepository _locationRepository;
  // PermissionStatus? _permissionStatus;
  // LocationGrantedStatus? _grantedStatus;
  // bool? _isLocationServiceEnabled;

  LocationPermissionBloc({required LocationRepository repo})
    : _locationRepository = repo,
      super(const LocationPermissionState.initial()) {
    on<_CheckAndRequestPermission>(_onCheckAndRequestPermission);
    on<_CheckPermissionStatus>(_onCheckPermissionStatus);
    // on<_IsLocationServiceEnable>(_onCheckLocationServices);
    on<_OpenAppSettings>(_onOpenAppSettings);
    on<_OpenDeviceLocationSettings>(_onOpenDeviceLocationSettings);
    on<_GetCurrentGrantedPermission>(_onGetCurrentGrantedPermission);
    on<_AskBackgroundPermission>(_onAskBackgroundPermission);
  }

  // Future<void> _onCheckLocationServices(
  //   _IsLocationServiceEnable event,
  //   Emitter<LocationPermissionState> emit,
  // ) async {
  //   emit(const LocationPermissionState.loading());

  //   final isEnabled = await _locationRepository.isLocationServiceEnabled();

  //   if (!isEnabled) {
  //     emit(
  //       LocationPermissionState.failure(
  //         const LocationServiceDisabledFailure(
  //           message: 'Location services are disabled',
  //         ),
  //       ),
  //     );
  //   } else {
  //     // If location services are enabled, check permission status
  //     add(const LocationPermissionEvent.checkPermissionStatus());
  //   }
  // }

  Future<void> _onCheckPermissionStatus(
    _CheckPermissionStatus event,
    Emitter<LocationPermissionState> emit,
  ) async {
    emit(const LocationPermissionState.loading());

    final permission = await _locationRepository.checkPermission();
    permission.fold(
      (failure) {
        emit(LocationPermissionState.failure(failure));
      },
      (status) {
        // _permissionStatus = status;
        if (status == PermissionStatus.granted) {
          add(const LocationPermissionEvent.getCurrentGrantedPermission());
        } else {
          emit(LocationPermissionState.failure(mapLocationFailure(status)));
        }
      },
    );
  }

  Future<void> _onCheckAndRequestPermission(
    _CheckAndRequestPermission event,
    Emitter<LocationPermissionState> emit,
  ) async {
    emit(const LocationPermissionState.loading());

    final requestResult = await _locationRepository.checkAndRequestPermission();

    await requestResult.fold(
      (failure) async => emit(LocationPermissionState.failure(failure)),
      (permissionStatusAfterRequest) {
        // _permissionStatus = permissionStatusAfterRequest;
        if (permissionStatusAfterRequest == PermissionStatus.granted) {
          emit(
            LocationPermissionState.loaded(LocationGrantedStatus.whileUsing),
          );
        } else {
          emit(
            LocationPermissionState.failure(
              mapLocationFailure(permissionStatusAfterRequest),
            ),
          );
        }
      },
    );
  }

  Future<void> _onAskBackgroundPermission(
    _AskBackgroundPermission event,
    Emitter<LocationPermissionState> emit,
  ) async {
    emit(const LocationPermissionState.loading());

    final result = await _locationRepository.requestBackgroundPermission();

    result.fold((failure) => emit(LocationPermissionState.failure(failure)), (
      permissionStatus,
    ) {
      // _permissionStatus = permissionStatus;
      if (permissionStatus != PermissionStatus.granted) {
        emit(
          LocationPermissionState.failure(mapLocationFailure(permissionStatus)),
        );
        return;
      }
      add(const LocationPermissionEvent.getCurrentGrantedPermission());
    });
  }

  Future<void> _onOpenAppSettings(
    _OpenAppSettings event,
    Emitter<LocationPermissionState> emit,
  ) async {
    emit(const LocationPermissionState.loading());
    final result = await _locationRepository.openAppSettings();
    result.fold(
      (failure) => emit(LocationPermissionState.failure(failure)),
      (_) => add(const LocationPermissionEvent.getCurrentGrantedPermission()),
    );
  }

  Future<void> _onOpenDeviceLocationSettings(
    _OpenDeviceLocationSettings event,
    Emitter<LocationPermissionState> emit,
  ) async {
    await Geolocator.openLocationSettings();
  }

  Future<void> _onGetCurrentGrantedPermission(
    _GetCurrentGrantedPermission event,
    Emitter<LocationPermissionState> emit,
  ) async {
    emit(const LocationPermissionState.loading());

    final result = await _locationRepository.getCurrentGrantedPermission();

    result.fold((failure) => emit(LocationPermissionState.failure(failure)), (
      grantedStatus,
    ) async {
      // _grantedStatus = grantedStatus;
      emit(LocationPermissionState.loaded(grantedStatus));
    });
  }
}
