import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart' as permission;
import 'package:safari_yatri/core/errors/location_failure.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/location/repositories/location_repository.dart';

part 'current_location_event.dart';
part 'current_location_state.dart';
part 'current_location_bloc.freezed.dart';

class CurrentLocationBloc
    extends Bloc<CurrentLocationEvent, CurrentLocationState> {
  final LocationRepository _locationRepository;

  CurrentLocationBloc({required LocationRepository repo})
    : _locationRepository = repo,
      super(const CurrentLocationState.initial()) {
    on<_GetCurrentLocation>(_onGetCurrentLocation);
  }

  Future<void> _onGetCurrentLocation(
    _GetCurrentLocation event,
    Emitter<CurrentLocationState> emit,
  ) async {
    emit(const CurrentLocationState.loading());

    final isServiceEnabled =
        await _locationRepository.isLocationServiceEnabled();
    if (!isServiceEnabled) {
      emit(
        const CurrentLocationState.failure(
          LocationServiceDisabledFailure(
            message: 'Location services are disabled',
          ),
        ),
      );
      return;
    }

    final permissionResult =
        await _locationRepository.checkAndRequestPermission();

    bool hasPermission = false;

    permissionResult.fold(
      (failure) => emit(CurrentLocationState.failure(failure)),
      (status) {
        if (status != permission.PermissionStatus.granted) {
          emit(
            const CurrentLocationState.failure(
              LocationFailureWithMessage(
                message: 'Location permission not granted',
              ),
            ),
          );
        } else {
          hasPermission = true;
        }
      },
    );

    if (!hasPermission) return;

    final position = await _locationRepository.getCurrentPosition();
    emit(
      position.fold(
        (failure) => CurrentLocationState.failure(failure),
        (position) => CurrentLocationState.loaded(position),
      ),
    );
  }
}
