import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/location/repositories/location_repository.dart';

part 'current_location_navigator_state.dart';

/// it will navigate to current location of user like
/// there is button that help to move to current location whenever it button pressed
/// or corresponding function gets called
class CurrentLocationNavigatorCubit
    extends Cubit<CurrentLocationNavigatorState> {
  final LocationRepository _locationRepository;

  CurrentLocationNavigatorCubit({required LocationRepository repo})
    : _locationRepository = repo,
      super(CurrentLocationNavigatorState.initial());

  Future<void> getCurrentLocation() async {
    emit(const CurrentLocationNavigatorState.loading());

    final currentPosition = await _locationRepository.getCurrentPosition();

    currentPosition.fold(
      (failure) => emit(CurrentLocationNavigatorState.failure(failure)),
      (position) {
        emit(
          CurrentLocationNavigatorState.loaded(
            LatLng(position.latitude, position.longitude),
          ),
        );
      },
    );
  }
}
