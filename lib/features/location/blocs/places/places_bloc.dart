import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/location/models/place_model.dart';
import 'package:safari_yatri/features/location/repositories/places_repository.dart';

part 'places_event.dart';
part 'places_state.dart';
part 'places_bloc.freezed.dart';

///Help to search places in [EnterRouteBottomSheetPage]
class PlacesBloc extends Bloc<PlacesEvent, PlacesState> {
  final PlacesRepository _placesRepository;

  PlacesBloc({required PlacesRepository repo})
    : _placesRepository = repo,
      super(PlacesState.initial()) {
    {
      on<_SearchPlace>(_onSearchPlace);
      // on<_GetPlaceDetails>(_onGetPlaceDetails);
    }
  }

  Future<void> _onSearchPlace(
    _SearchPlace event,
    Emitter<PlacesState> emit,
  ) async {
    emit(PlacesState.loading());

    final failureOrPredictions = await _placesRepository.getPlacePredictions(
      event.query,
    );

    failureOrPredictions.fold(
      (failure) => emit(PlacesState.failure(failure)),
      (predictions) => emit(PlacesState.loaded(predictions)),
    );
  }

  // Future<void> _onGetPlaceDetails(
  //   _GetPlaceDetails event,
  //   Emitter<PlacesState> emit,
  // ) async {
  //   emit(PlacesState.loading());

  //   final failureOrPlaceDetails = await _placesRepository.getPlaceDetails(
  //     event.placeId,
  //   );

  //   failureOrPlaceDetails.fold(
  //     (failure) => emit(PlacesState.failure(failure)),
  //     (placeDetails) => emit(PlacesState.loadedPlaceDetails(placeDetails)),
  //   );
  // }
}
