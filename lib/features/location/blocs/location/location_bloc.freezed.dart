// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LocationEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationEvent()';
  }
}

/// Adds pattern-matching-related methods to [LocationEvent].
extension LocationEventPatterns on LocationEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_StartTracking value)? startTracking,
    TResult Function(_LocationUpdated value)? locationUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _StartTracking() when startTracking != null:
        return startTracking(_that);
      case _LocationUpdated() when locationUpdated != null:
        return locationUpdated(_that);
      case _ErrorOccurred() when errorOccurred != null:
        return errorOccurred(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_StartTracking value) startTracking,
    required TResult Function(_LocationUpdated value) locationUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    final _that = this;
    switch (_that) {
      case _StartTracking():
        return startTracking(_that);
      case _LocationUpdated():
        return locationUpdated(_that);
      case _ErrorOccurred():
        return errorOccurred(_that);
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_StartTracking value)? startTracking,
    TResult? Function(_LocationUpdated value)? locationUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    final _that = this;
    switch (_that) {
      case _StartTracking() when startTracking != null:
        return startTracking(_that);
      case _LocationUpdated() when locationUpdated != null:
        return locationUpdated(_that);
      case _ErrorOccurred() when errorOccurred != null:
        return errorOccurred(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? startTracking,
    TResult Function(Position position)? locationUpdated,
    TResult Function(String message)? errorOccurred,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _StartTracking() when startTracking != null:
        return startTracking();
      case _LocationUpdated() when locationUpdated != null:
        return locationUpdated(_that.position);
      case _ErrorOccurred() when errorOccurred != null:
        return errorOccurred(_that.message);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() startTracking,
    required TResult Function(Position position) locationUpdated,
    required TResult Function(String message) errorOccurred,
  }) {
    final _that = this;
    switch (_that) {
      case _StartTracking():
        return startTracking();
      case _LocationUpdated():
        return locationUpdated(_that.position);
      case _ErrorOccurred():
        return errorOccurred(_that.message);
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? startTracking,
    TResult? Function(Position position)? locationUpdated,
    TResult? Function(String message)? errorOccurred,
  }) {
    final _that = this;
    switch (_that) {
      case _StartTracking() when startTracking != null:
        return startTracking();
      case _LocationUpdated() when locationUpdated != null:
        return locationUpdated(_that.position);
      case _ErrorOccurred() when errorOccurred != null:
        return errorOccurred(_that.message);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _StartTracking implements LocationEvent {
  const _StartTracking();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _StartTracking);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationEvent.startTracking()';
  }
}

/// @nodoc

class _LocationUpdated implements LocationEvent {
  const _LocationUpdated(this.position);

  final Position position;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LocationUpdated &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @override
  int get hashCode => Object.hash(runtimeType, position);

  @override
  String toString() {
    return 'LocationEvent.locationUpdated(position: $position)';
  }
}

/// @nodoc

class _ErrorOccurred implements LocationEvent {
  const _ErrorOccurred({required this.message});

  final String message;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ErrorOccurred &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'LocationEvent.errorOccurred(message: $message)';
  }
}
