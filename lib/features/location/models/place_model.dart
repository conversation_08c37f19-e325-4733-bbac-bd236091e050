class PlacePrediction {
  final String description;
  final String placeId;

  PlacePrediction({required this.description, required this.placeId});

  factory PlacePrediction.fromJson(Map<String, dynamic> json) {
    return PlacePrediction(
      description: json['description'] as String,
      placeId: json['place_id'] as String,
    );
  }
}

class PlaceDetails {
  final String placeId;
  final String name;
  final String address;
  final double? latitude;
  final double? longitude;

  PlaceDetails({
    required this.placeId,
    required this.name,
    required this.address,
    this.latitude,
    this.longitude,
  });

  factory PlaceDetails.fromJson(Map<String, dynamic> json) {
    final geometry = json['geometry'];
    final location = geometry != null ? geometry['location'] : null;
    return PlaceDetails(
      placeId: json['place_id'] as String,
      name: json['name'] as String,
      address: json['formatted_address'] as String,
      latitude: location != null ? (location['lat'] as num?)?.toDouble() : null,
      longitude:
          location != null ? (location['lng'] as num?)?.toDouble() : null,
    );
  }
}
