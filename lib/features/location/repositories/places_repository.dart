

import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:safari_yatri/core/errors/error_handler.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/config/env_secrete_config.dart';
import 'package:safari_yatri/features/location/models/place_model.dart';

abstract class PlacesRepository {
  FutureEither<List<PlacePrediction>> getPlacePredictions(String query);

  FutureEither<PlaceDetails> getPlaceDetails(String placeId);
}

class GooglePlacesRepository implements PlacesRepository {
  final Dio _dio;

  final String _baseUrl = 'https://maps.googleapis.com/maps/api/place';

  GooglePlacesRepository({required Dio dio}) : _dio = dio;

  @override
  FutureEither<List<PlacePrediction>> getPlacePredictions(String query) async {
    final apiKey = EnvSecreteConfig.instance.getGoogleMapApiKey;

    if (query.isEmpty) {
      return Right([]);
    }

    final url = '$_baseUrl/autocomplete/json?input=$query&key=$apiKey';

    try {
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == 'OK') {
          final predictions =
              (data['predictions'] as List)
                  .map((json) => PlacePrediction.fromJson(json))
                  .toList();

          return Right(predictions);
        } else {
          return Left(
            UnexpectedFailure(
              message:
                  'Google Places API Error: ${data['status']} - ${data['error_message'] ?? ''}',
            ),
          );
        }
      } else {
        return Left(
          ServerFailure(
            message: 'Failed to load predictions: HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  FutureEither<PlaceDetails> getPlaceDetails(String placeId) async {
    final apiKey = EnvSecreteConfig.instance.getGoogleMapApiKey;

    final url =
        '$_baseUrl/details/json?place_id=$placeId&key=$apiKey&fields=place_id,name,formatted_address,geometry';

    try {
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == 'OK') {
          return Right(PlaceDetails.fromJson(data['result']));
        } else {
          return Left(
            UnexpectedFailure(
              message:
                  'Google Places API Error: ${data['status']} - ${data['error_message'] ?? ''}',
            ),
          );
        }
      } else {
        return Left(
          ServerFailure(
            message:
                'Failed to load place details: HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }
}

class LocationIQPlacesRepository implements PlacesRepository {
  final Dio _dio;

  final String _baseUrl = 'https://api.locationiq.com/v1';

  LocationIQPlacesRepository({required Dio dio}) : _dio = dio;

  @override
  FutureEither<List<PlacePrediction>> getPlacePredictions(String query) async {
    final apiKey =
        EnvSecreteConfig
            .instance
            .locationIQApiKey; // Assuming you add this to your config

    if (query.isEmpty) {
      return Right([]);
    }

    final url = '$_baseUrl/autocomplete.php?key=$apiKey&q=$query&format=json';

    try {
      final response = await _dio.get(url);

      final List<dynamic> data = response.data;
      final predictions =
          data.map((json) {
            return PlacePrediction(
              description: json['display_name'] ?? '',
              placeId:
                  json['place_id']
                      .toString(), // LocationIQ uses a numeric place_id
              // Add other relevant fields from the LocationIQ response
            );
          }).toList();
      return Right(predictions);
    } catch (e) {
      if (e is DioException && e.response?.statusCode == 404) {
        return Right([]);
      }
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  FutureEither<PlaceDetails> getPlaceDetails(String placeId) async {
    final kathmanduDetails = PlaceDetails(
      placeId:
          'ChIJV7w9-PH4kjkRtsj9T6j4NHw', // This is a Google Places API Place ID for Kathmandu
      name: 'Kathmandu',
      address: 'Kathmandu, Nepal',
      latitude: 27.7172, // Approximate latitude
      longitude: 85.3206, // Approximate longitude
    );

    return Right(kathmanduDetails);
  }
}
