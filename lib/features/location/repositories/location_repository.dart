import 'package:dartz/dartz.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart' as permission;
import 'package:geocoding/geocoding.dart';
import 'package:safari_yatri/core/errors/error_handler.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/errors/location_failure.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/utils/location_permission_status_failure_mapper.dart';
import 'package:safari_yatri/features/location/blocs/location_permission/location_permission_bloc.dart';

abstract class LocationRepository {
  FutureEither<permission.PermissionStatus> checkAndRequestPermission();
  FutureEither<permission.PermissionStatus> requestBackgroundPermission();
  FutureEither<Unit> openAppSettings();
  FutureEither<Unit> openLocationSettings();
  FutureEither<Position> getCurrentPosition();
  FutureEither<String> getAddressFromCoordinates(
    double latitude,
    double longitude,
  );
  Stream<Position> getPositionStream(int distanceFilter);

  Stream<ServiceStatus> getServiceStatusStream();
  Future<bool> isLocationServiceEnabled();
  FutureEither<permission.PermissionStatus> checkPermission();
  FutureEither<LocationGrantedStatus> getCurrentGrantedPermission();
}

class LocationRepositoryI implements LocationRepository {
  @override
  FutureEither<permission.PermissionStatus> checkAndRequestPermission() async {
    try {
      final status = await permission.Permission.location.request();
      return Right(status);
    } catch (e) {
      return Left(
        PermissionFailure(
          message: 'Failed to request permission',
          exception: e,
        ),
      );
    }
  }

  @override
  FutureEither<Unit> openAppSettings() async {
    try {
      final opened = await permission.openAppSettings();
      if (opened) {
        return const Right(unit);
      } else {
        return Left(PermissionFailure(message: 'Failed to open app settings'));
      }
    } catch (e) {
      return Left(
        PermissionFailure(message: 'Failed to open app settings', exception: e),
      );
    }
  }

  @override
  FutureEither<Unit> openLocationSettings() async {
    try {
      await Geolocator.openLocationSettings();
      return const Right(unit);
    } catch (e) {
      return Left(
        LocationFailureWithMessage(
          message: 'Failed to open location settings',
          exception: e,
        ),
      );
    }
  }

  @override
  FutureEither<Position> getCurrentPosition() async {
    try {
      final position = await Geolocator.getCurrentPosition(
        locationSettings: LocationSettings(accuracy: LocationAccuracy.high),
      );
      return Right(position);
    } catch (e) {
      return Left(
        LocationFailureWithMessage(
          message: 'Failed to get location',
          exception: e,
        ),
      );
    }
  }

  @override
  FutureEither<String> getAddressFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        latitude,
        longitude,
      );
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;

        return Right(
          "${place.street}, ${place.locality}, ${place.administrativeArea}",
        );
      } else {
        return Right("Agyat Location");
      }
    } catch (e) {
      return Left(ErrorHandler.handleException(e));
    }
  }

  @override
  Stream<Position> getPositionStream([int distanceFilter = 10]) {
    return Geolocator.getPositionStream(
      locationSettings: LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: distanceFilter,
      ),
    );
  }

  @override
  FutureEither<permission.PermissionStatus>
  requestBackgroundPermission() async {
    try {
      final status = await permission.Permission.locationAlways.request();
      return Right(status);
    } catch (e) {
      return Left(
        PermissionFailure(
          message: 'Failed to request background location permission',
          exception: e,
        ),
      );
    }
  }

  @override
  Stream<ServiceStatus> getServiceStatusStream() {
    return Geolocator.getServiceStatusStream();
  }

  @override
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  @override
  FutureEither<permission.PermissionStatus> checkPermission() async {
    try {
      final status = await permission.Permission.location.status;
      return Right(status);
    } catch (e) {
      return Left(
        PermissionFailure(message: 'Failed to check permission', exception: e),
      );
    }
  }

  @override
  FutureEither<LocationGrantedStatus> getCurrentGrantedPermission() async {
    try {
      final status = await permission.Permission.location.status;
      if (status == permission.PermissionStatus.granted) {
        final bgStatus = await permission.Permission.locationAlways.status;
        if (bgStatus == permission.PermissionStatus.granted) {
          return Right(LocationGrantedStatus.always);
        } else {
          return Right(LocationGrantedStatus.whileUsing);
        }
      } else {
        return Left(mapLocationFailure(status));
      }
    } catch (e) {
      return Left(
        PermissionFailure(
          message: 'Failed to get location permission',
          exception: e,
        ),
      );
    }
  }
}
