import 'package:safari_yatri/common/logger.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/network/api_service.dart';

abstract interface class RemoteLocationRepository {
  FutureEither<String> updateLocation({
    required double lat,
    required double lng,
  });
}

class RemoteLocationRepositoryI implements RemoteLocationRepository {
  final ApiService _apiService;
  RemoteLocationRepositoryI({required ApiService apiService})
    : _apiService = apiService;

  @override
  FutureEither<String> updateLocation({
    required double lat,
    required double lng,
  }) async {
    return await _apiService.put<String>(
      'MyProfile/SetMyCurrentLocation',
      queryParameters: {'lat': lat.toString(), 'lng': lng.toString()},
      fromJson: (data) {
        dLog.d("data send success $data");
        return "Success";
      },
    );
  }
}
