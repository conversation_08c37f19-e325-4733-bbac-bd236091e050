import 'package:flutter/material.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import '../widget/payment_option_card.dart';

class PaymentMethodPage extends StatefulWidget {
  const PaymentMethodPage({super.key});

  @override
  State<PaymentMethodPage> createState() => _PaymentMethodPageState();
}

class _PaymentMethodPageState extends State<PaymentMethodPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: Text('Payment Method')),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: AppStyles.space16,
          vertical: AppStyles.space12,
        ),
        child: Column(
          children: [
            PaymentOptionCard(
              title: 'Bank Account',
              subtitle: '****_****_****_1234',
              showUpdateButton: true,
            ),
            SizedBox(height: 16),
            PaymentOptionCard(
              title: 'eSewa Wallet',
              subtitle: 'Connected',
              showVerified: true,
            ),
          ],
        ),
      ),
    );
  }
}
