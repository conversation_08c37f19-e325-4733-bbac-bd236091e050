import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/logout/logout_cubit.dart';
import 'package:safari_yatri/common/blocs/theme_mode/theme_mode_cubit.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/app/restart_widget.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/services/cache_service.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/features/my_profile/bloc/terminate_account/terminate_account_bloc.dart';
import '../../../common/blocs/locale_cubit/locale_cubit.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../core/widget/custom_appbar.dart';
import '../../../core/widget/custom_dilog_alert_box.dart';
import '../widget/setting_menu_tile.dart';

class SettingPage extends StatelessWidget {
  const SettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: Text(L.t.settingScreenAppBarTitle)),
      body: Column(
        spacing: AppStyles.space12,
        children: [
          _themeMode(),
          _languageSelector(),

          MenuTile(
            title: L.t.settingScreenChangePasswordTitle,
            onTap: () {
              context.pushNamed(AppRoutesName.changePassword);
            },
          ),
          MenuTile(
            title: L.t.settingScreenNotificationTitle,
            onTap: () {
              context.pushNamed(AppRoutesName.notificationPage);
            },
          ),
          MenuTile(title: L.t.settingScreenRulesAndTermsTitle, onTap: () {}),
          _logoutButton(),
          _deleteAccount(),
        ],
      ),
    );
  }

  Widget _logoutButton() {
    final LogoutCubit logoutCubit = logoutSl<LogoutCubit>();

    return BlocConsumer<LogoutCubit, LogoutState>(
      bloc: logoutCubit,
      listener: (context, state) {
        state.whenOrNull(
          loading: () => AppLoadingDialog.show(context),
          loaded: (data) async {
            AppLoadingDialog.hide(context);
            CustomToast.showSuccess(data);
            RestartWidget.restartApp(context);
            context.goNamed(AppRoutesName.signIn);
            return;
          },
          failure: (failure) {
            AppLoadingDialog.hide(context);
            CustomToast.showError(failure.message);
          },
        );
      },
      builder: (context, state) {
        return MenuTile(
          title: L.t.settingScreenLogoutTitle,
          onTap: () {
            logoutCubit.logout();
          },
        );
      },
    );
  }

  BlocConsumer<TerminateAccountBloc, TerminateAccountState> _deleteAccount() {
    return BlocConsumer<TerminateAccountBloc, TerminateAccountState>(
      listener: (context, state) {
        state.whenOrNull(
          loading: () => AppLoadingDialog.show(context),
          failure: (failure) {
            CustomToast.showError(failure.message);
          },
          loaded: (data) {
            AppLoadingDialog.hide(context);
            CustomToast.showSuccess(data);
            CacheService.instance.clearTokenData();
            context.goNamed(AppRoutesName.signIn);
          },
        );
      },
      builder: (context, state) {
        return MenuTile(
          title: L.t.settingScreenDeleteAccountTitle,
          textColor: ColorScheme.of(context).error,
          isShowTrailing: false,
          onTap: () {
            _onDeleteAccountTap(context);
          },
        );
      },
    );
  }

  BlocBuilder<LocaleCubit, Locale> _languageSelector() {
    return BlocBuilder<LocaleCubit, Locale>(
      builder: (context, locale) {
        return MenuTile(
          title: L.t.settingScreenLanguageTitle,
          subTitle: locale.languageCode == 'ne' ? 'नेपाली' : 'English',
          onTap: () {
            context.pushNamed(AppRoutesName.selectLanguage);
          },
        );
      },
    );
  }

  MenuTile _themeMode() {
    return MenuTile(
      title: 'Theme Mode',
      trailing: BlocBuilder<AppThemeCubit, AppThemeState>(
        builder: (context, tState) {
          return DropdownButton<ThemeMode>(
            value: tState.mode,
            underline: SizedBox(),
            onChanged: (mode) {
              if (mode != null) {
                sl<AppThemeCubit>().setTheme(mode);
              }
            },
            items: const [
              DropdownMenuItem(value: ThemeMode.system, child: Text('System')),
              DropdownMenuItem(value: ThemeMode.light, child: Text('Light')),
              DropdownMenuItem(value: ThemeMode.dark, child: Text('Dark')),
            ],
          );
        },
      ),
    );
  }
}

void _onDeleteAccountTap(BuildContext context) async {
  final confirmed = await showConfirmationDialog(
    context: context,
    title: L.t.settingScreenDeleteDialogTitle,
    message: L.t.settingScreenDeleteDialogMessage,
    confirmText: L.t.settingScreenDeleteDialogConfirmText,
    cancelText: L.t.settingScreenDeleteDialogCancelText,
    icon: Icons.warning_amber_rounded,
    iconColor: Colors.red,
    confirmButtonColor: Colors.red,
    cancelTextColor: Colors.grey,
  );

  if (confirmed == true) {
    sl<TerminateAccountBloc>().add(TerminateAccountEvent.terminateAccount());
  }
}
