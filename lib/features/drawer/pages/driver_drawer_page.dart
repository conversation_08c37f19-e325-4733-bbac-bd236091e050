import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/passenger/core/blocs/pick_location_picking_status/pickup_location_picking_status_cubit.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/common/blocs/local_user_mode/local_user_mode_cubit.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';

import '../../../core/router/app_route_names.dart';
import '../../../core/widget/custom_button.dart';
import '../../my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import '../widget/drawer_header.dart';
import '../widget/drawer_menu_widget.dart';
import '../widget/version_text.dart';

class DriverDrawer extends StatefulWidget {
  const DriverDrawer({super.key});

  @override
  State<DriverDrawer> createState() => _DriverDrawerState();
}

class _DriverDrawerState extends State<DriverDrawer> {
  @override
  initState() {
    super.initState();
    sl<MyProfileBloc>().add(const MyProfileEvent.get());
  }

  @override
  Widget build(BuildContext context) {
    return buildDriverAppDrawer(context);
  }

  Widget buildDriverAppDrawer(BuildContext context) {
    final dividerColor = T.c(context).onSurface.withOpacity(0.12);

    return SafeArea(
      child: Drawer(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.zero,
            bottomRight: Radius.zero,
          ),
        ),
        child: BlocConsumer<MyProfileBloc, MyProfileState>(
          listener: (context, state) {
            state.whenOrNull(
              failure: (failure) {
                CustomToast.showError(failure.message);
              },
            );
          },
          builder: (context, state) {
            return state.maybeWhen(
              failure:
                  (failure) => ErrorWidgetWithRetry(
                    failure: failure,
                    onRetry: () {
                      sl<MyProfileBloc>().add(const MyProfileEvent.get(true));
                    },
                  ),
              orElse: () => const SizedBox(),
              loading: () => const Center(child: CircularProgressIndicator()),
              loaded:
                  (data) => Column(
                    children: [
                      DrawerHeaderWidget(
                        textTheme: T.t(context),
                        name: data.userName,
                        imageUrl: data.profilePicture,
                        phoneNumber: data.phoneNo,
                        rating: 4.8,
                        chipTitle: data.userType,
                      ),
                      Expanded(
                        child: _buildDriverDrawerBody(context, dividerColor),
                      ),
                    ],
                  ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildDriverDrawerBody(BuildContext context, Color dividerColor) {
    return Column(
      children: [
        DrawerWidget(
          title: L.t.driverDrawerScreenHomeTitle,
          icons: LucideIcons.house,
          onTap: () {
            Navigator.of(context).pop();
          },
        ),
        DrawerWidget(
          title: L.t.driverDrawerScreenMyTripsTitle,
          icons: LucideIcons.mapPin,
          onTap: () => context.pushNamed(AppRoutesName.myPassengerTrips),
        ),

        // DrawerWidget(
        //   title: "My Earnings",
        //   icons: LucideIcons.mapPin,
        //   onTap: () {},
        // ),
        // DrawerWidget(
        //   title: "Payment Methods",
        //   icons: LucideIcons.creditCard,
        //   onTap: () {
        //     context.pushNamed(AppRoutesName.paymentMethodPage);
        //   },
        // ),
        DrawerWidget(
          title: L.t.driverDrawerScreenProfileTitle,
          icons: LucideIcons.user,
          onTap: () {
            context.pushNamed(AppRoutesName.profileScreen);
          },
        ),
        Divider(thickness: 1, color: dividerColor),
        DrawerWidget(
          title: L.t.driverDrawerScreenSettingsTitle,
          icons: LucideIcons.settings,
          onTap: () {
            context.pushNamed(AppRoutesName.setting);
          },
        ),
        // DrawerWidget(
        //   title: "Help Center",
        //   icons: LucideIcons.handHelping,
        //   onTap: () {},
        // ),
        Divider(thickness: 1, color: dividerColor),
        DrawerWidget(
          title: L.t.driverDrawerScreenEmergencySafetyTitle,
          icons: LucideIcons.shieldCheck,
          onTap: () {
            context.pushNamed(AppRoutesName.emergencyAndSaftey);
          },
        ),
        const Spacer(),
        Padding(
          padding: const EdgeInsets.all(AppStyles.space12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const AppVersionText(),
              SizedBox(height: AppStyles.space4),
              BlocConsumer<LocalUserModeCubit, LocalUserModeState>(
                listener: (context, state) async {
                  state.whenOrNull(
                    failure: (failure) {
                      AppLoadingDialog.hide(context);
                      CustomToast.showError(failure.message);
                    },
                    loading: () {
                      AppLoadingDialog.show(context);
                    },
                    loaded: (value) {
                      AppLoadingDialog.hide(context);
                      context.goNamed(AppRoutesName.passengerHome);
                    },
                  );
                },
                builder: (context, state) {
                  return CustomButtonPrimary(
                    title: L.t.driverDrawerScreenPassengerModeButton,
                    onPressed: () {
                      sl<PickupLocationPickingStatusCubit>().enable();
                      sl<LocalUserModeCubit>().switchPassengerMode();
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
