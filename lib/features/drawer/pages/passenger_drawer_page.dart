import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/constant/string_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/common/blocs/local_user_mode/local_user_mode_cubit.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/pick_location_picking_status/pickup_location_picking_status_cubit.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

import '../../../core/router/app_route_names.dart';
import '../../../core/widget/custom_button.dart';
import '../../my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import '../widget/drawer_header_widget.dart';
import '../widget/drawer_menu_widget.dart';
import '../widget/version_text.dart';

class PassengerDrawer extends StatefulWidget {
  const PassengerDrawer({super.key});

  @override
  State<PassengerDrawer> createState() => _PassengerDrawerState();
}

class _PassengerDrawerState extends State<PassengerDrawer> {
  @override
  initState() {
    super.initState();
    sl<MyProfileBloc>().add(const MyProfileEvent.get());
  }

  @override
  Widget build(BuildContext context) {
    return buildPassengerAppDrawer(context);
  }

  Widget buildPassengerAppDrawer(BuildContext context) {
    final dividerColor = T.c(context).onSurface.withOpacity(0.12);

    return SafeArea(
      child: Drawer(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.zero,
            bottomRight: Radius.zero,
          ),
        ),
        child: CustomDrawerHeader(
          widget: _buildDrawerBody(context, dividerColor),
        ),
      ),
    );
  }

  Widget _buildDrawerBody(BuildContext context, Color dividerColor) {
    return Column(
      children: [
        DrawerWidget(
          title: L.t.passengerDrawerScreenHomeTitle,
          icons: LucideIcons.house,
          onTap: () {
            Navigator.of(context).pop();
          },
        ),
        DrawerWidget(
          title: L.t.passengerDrawerScreenYourTripsTitle,
          icons: LucideIcons.mapPin,
          onTap: () => context.pushNamed(AppRoutesName.passengerBookingTrips),
        ),
        // DrawerWidget(
        //   title: "Payment Methods",
        //   icons: LucideIcons.creditCard,
        //   onTap: () {
        //     context.pushNamed(AppRoutesName.paymentMethodPage);
        //   },
        // ),
        DrawerWidget(
          title: L.t.passengerDrawerScreenProfileTitle,
          icons: LucideIcons.user,
          onTap: () {
            context.pushNamed(AppRoutesName.profileScreen);
          },
        ),
        Divider(thickness: 1, color: dividerColor),
        DrawerWidget(
          title: L.t.passengerDrawerScreenSettingsTitle,
          icons: LucideIcons.settings,
          onTap: () {
            context.pushNamed(AppRoutesName.setting);
          },
        ),
        // DrawerWidget(
        //   title: "Help Center",
        //   icons: LucideIcons.handHelping,
        //   onTap: () {},
        // ),
        Divider(thickness: 1, color: dividerColor),
        DrawerWidget(
          title: L.t.passengerDrawerScreenEmergencySafetyTitle,
          icons: LucideIcons.shieldCheck,
          onTap: () {
            context.pushNamed(AppRoutesName.emergencyAndSaftey);
          },
        ),
        const Spacer(),
        Padding(
          padding: const EdgeInsets.all(AppStyles.space12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const AppVersionText(),
              SizedBox(height: AppStyles.space4),
              BlocConsumer<LocalUserModeCubit, LocalUserModeState>(
                listener: (context, state) async {
                  state.whenOrNull(
                    failure: (failure) {
                      AppLoadingDialog.hide(context);
                      CustomToast.showError(failure.message);
                    },
                    loading: () {
                      AppLoadingDialog.show(context);
                    },
                    loaded: (value) {
                      AppLoadingDialog.hide(context);
                      _navigateBasedOnRole(context, value);
                    },
                  );
                },
                builder: (context, state) {
                  return CustomButtonPrimary(
                    title: L.t.passengerDrawerScreenDriverModeButton,
                    onPressed: () {
                      sl<LocalUserModeCubit>().switchDriverMode();
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _navigateBasedOnRole(BuildContext context, String role) {
    sl<PassengerRouteBloc>().add(
      const PassengerRouteEvent.resetPassengerRoute(),
    );
    sl<PickupLocationPickingStatusCubit>().enable();
    if (role == kPendingRequestForRiderRole) {
      context.pushNamed(AppRoutesName.documentReviewPage);
    } else if (role == kRiderRole) {
      context.goNamed(AppRoutesName.driverHome);
    } else {
      context.pushNamed(AppRoutesName.whatsYourNamePage);
    }
  }
}
