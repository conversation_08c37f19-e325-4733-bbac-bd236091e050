import 'package:flutter/material.dart';
// import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../common/widgets/custom_toast.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../core/utils/theme_utils.dart';
import '../../../core/widget/error_widget_with_retry.dart';
import '../../my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import 'drawer_header.dart';

class CustomDrawerHeader extends StatelessWidget {
  final Widget? widget;
  const CustomDrawerHeader({super.key, this.widget});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MyProfileBloc, MyProfileState>(
      listener: (context, state) {
        state.whenOrNull(
          failure: (failure) {
            CustomToast.showError(failure.message);
          },
        );
      },
      builder: (context, state) {
        return state.maybeWhen(
          failure:
              (failure) => ErrorWidgetWithRetry(
                failure: failure,
                onRetry: () {
                  sl<MyProfileBloc>().add(const MyProfileEvent.get());
                },
              ),
          orElse: () => const SizedBox(),
          loading: () => const Center(child: CircularProgressIndicator()),
          loaded:
              (data) => Column(
                children: [
                  DrawerHeaderWidget(
                    textTheme: T.t(context),
                    name: data.userName,
                    imageUrl: data.profilePicture,
                    phoneNumber: data.phoneNo,
                    rating: 4.8,
                    chipTitle: data.userType,
                  ),
                  Expanded(child: widget!),
                ],
              ),
        );
      },
    );
  }
}
