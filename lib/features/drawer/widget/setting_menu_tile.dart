import 'package:flutter/material.dart';

import '../../../core/utils/theme_utils.dart';

class MenuTile extends StatelessWidget {
  final String title;
  final String? subTitle;
  final Widget? trailing;
  final bool isShowTrailing;
  final Color? textColor;
  final void Function()? onTap;

  const MenuTile({
    super.key,
    required this.title,
    this.subTitle,
    this.trailing,
    this.isShowTrailing = true,
    this.textColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(horizontal: 12),
          minVerticalPadding: 0,
          visualDensity: const VisualDensity(vertical: -4),
          title: Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: textColor,
            ),
          ),
          subtitle:
              subTitle != null
                  ? Text(
                    subTitle!,
                    style: T.t(context).titleSmall?.copyWith(fontSize: 12),
                  )
                  : null,
          trailing:
              isShowTrailing
                  ? trailing ?? const Icon(Icons.arrow_forward_ios, size: 16)
                  : null,
        ),
      ),
    );
  }
}
