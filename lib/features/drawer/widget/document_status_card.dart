import 'package:flutter/material.dart';

enum DocumentStatus { verified, pending }

class DocumentStatusCard extends StatelessWidget {
  final String title;
  final String expiryDate;
  final DocumentStatus status;

  const DocumentStatusCard({
    super.key,
    required this.title,
    required this.expiryDate,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    Color badgeColor;
    Color badgeTextColor;
    IconData badgeIcon;
    String badgeText;

    switch (status) {
      case DocumentStatus.verified:
        badgeColor = Colors.green.shade100;
        badgeTextColor = Colors.green;
        badgeIcon = Icons.check_circle;
        badgeText = 'Verified';
        break;
      case DocumentStatus.pending:
        badgeColor = Colors.amber.shade100;
        badgeTextColor = Colors.orange;
        badgeIcon = Icons.warning;
        badgeText = 'Pending';
        break;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(color: Colors.black12, blurRadius: 6, offset: Offset(0, 3)),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  "Expires: $expiryDate",
                  style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: badgeColor,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              children: [
                Icon(badgeIcon, size: 16, color: badgeTextColor),
                const SizedBox(width: 4),
                Text(
                  badgeText,
                  style: TextStyle(
                    color: badgeTextColor,
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          IconButton(icon: const Icon(Icons.edit, size: 20), onPressed: () {}),
        ],
      ),
    );
  }
}
