import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart'; // Your T class

class DrawerWidget extends StatelessWidget {
  final String title;
  final IconData icons;
  final VoidCallback? onTap;

  const DrawerWidget({
    super.key,
    required this.title,
    required this.icons,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // context is available here!
    return ListTile(
      horizontalTitleGap: 8,
      leading: Icon(
        icons,
        // FIX: Add (context) to T.c
        color: T
            .c(context)
            .onSurfaceVariant
            .withOpacity(0.7), // subtle adaptive color
        size: 18,
      ),
      title: Text(
        title,
        style: T.t(context).titleLarge?.copyWith(color: T.c(context).onSurface),
      ),
      onTap: onTap,
    );
  }
}
