import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';


class CustomSearchBarWidget extends StatelessWidget {
  final String placeholder;
  final IconData? leadingIcon;
  final IconData? trailingIcon;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final bool readOnly;
  final VoidCallback? onTap;
  final Widget? suffixIcon;
  final bool isPickUp;
  final Color? pickUpColor;
  final Color? destinationColor;

  const CustomSearchBarWidget({
    super.key,
    this.placeholder = 'Search',
    this.leadingIcon,
    this.trailingIcon,
    this.controller,
    this.onChanged,
    this.readOnly = false,
    this.onTap,
    this.suffixIcon,
    this.isPickUp = true,
    this.pickUpColor,
    this.destinationColor,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveController = controller ?? TextEditingController();

    return AnimatedContainer(
      duration: const Duration(milliseconds: 250),
      height: 45,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainerHighest.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          if (leadingIcon != null)
            Icon(leadingIcon, color: T.c(context).onSurfaceVariant, size: 18),
          if (leadingIcon != null) const SizedBox(width: 8),
          Expanded(
            child: CupertinoTextField(
              controller: effectiveController,
              placeholder: placeholder,
              readOnly: readOnly,
              onTap: onTap,
              padding: const EdgeInsets.symmetric(vertical: 12),
              style: T
                  .t(context)
                  .bodyLarge
                  ?.copyWith(color: T.c(context).onSurface),
              onChanged: onChanged,
              decoration: null,
              suffix: suffixIcon,
              prefix: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder:
                    (child, anim) => ScaleTransition(scale: anim, child: child),
                child:
                    effectiveController.text.isNotEmpty
                        ? Icon(
                          Icons.radio_button_checked,
                          key: const ValueKey('focusedIcon'),
                          color:
                              isPickUp
                                  ? (pickUpColor ?? T.c(context).primary)
                                  : (destinationColor ??
                                      T.c(context).secondary),
                          size: 20,
                        )
                        : Icon(
                          Icons.search,
                          key: const ValueKey('defaultIcon'),
                          color: T.c(context).onSurfaceVariant,
                          size: 20,
                        ),
              ),
            ),
          ),
          if (trailingIcon != null) const SizedBox(width: 8),
          if (trailingIcon != null)
            Icon(trailingIcon, color: T.c(context).onSurfaceVariant),
        ],
      ),
    );
  }
}
