import 'package:flutter/material.dart';
import 'package:showcaseview/showcaseview.dart';

class CustomShowcaseWrapper extends StatelessWidget {
  final Widget child;

  const CustomShowcaseWrapper({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
      blurValue: 0,
      autoPlayDelay: const Duration(seconds: 1),
      builder: (context) => child,
    );
  }
}
