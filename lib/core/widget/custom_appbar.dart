import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget title;
  final List<Widget>? actions;
  final Widget? leading;
  final Color? backgroundColor;
  final double elevation;
  final bool centerTitle;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.backgroundColor,
    this.elevation = 0.2,
    this.centerTitle = false,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // final bgColor = backgroundColor ?? theme.colorScheme.surface;
    final textColor = theme.colorScheme.onSurfaceVariant;

    return AppBar(
      // backgroundColor: bgColor,
      elevation: elevation,
      centerTitle: centerTitle,
      leading: leading ?? _buildDefaultLeading(context, textColor),
      title: DefaultTextStyle(
        style: GoogleFonts.poppins(
          textStyle:
              theme.textTheme.titleLarge?.copyWith(
                color: textColor,
                fontWeight: FontWeight.w600,
                fontSize: 20,
              ) ??
              TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: textColor,
              ),
        ),
        child: title,
      ),
      actions: actions,
    );
  }

  Widget? _buildDefaultLeading(BuildContext context, Color iconColor) {
    final ModalRoute<Object?>? parentRoute = ModalRoute.of(context);
    final bool canPop = parentRoute?.canPop ?? false;
    final ScaffoldState? scaffold = Scaffold.maybeOf(context);

    if (scaffold != null && scaffold.hasDrawer) {
      return IconButton(
        icon: Icon(Icons.menu, color: iconColor),
        onPressed: () => scaffold.openDrawer(),
      );
    }

    if (canPop) {
      return IconButton(
        icon: Icon(Icons.arrow_back, color: iconColor),
        onPressed: () => Navigator.maybePop(context),
      );
    }

    return null;
  }
}
