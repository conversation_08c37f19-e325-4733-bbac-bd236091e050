// import 'package:flutter/material.dart';

// class DraggableScrollableSheetPage<T> extends Page<T> {
//   final WidgetBuilder builder;
//   final double initialChildSize;
//   final double minChildSize;
//   final double maxChildSize;
//   final bool expand;
//   final bool snap;
//   final Duration animationDuration;
//   final Color? barrierColor;
//   final bool barrierDismissible;
//   final String? barrierLabel;

//   const DraggableScrollableSheetPage({
//     required this.builder,
//     this.initialChildSize = 0.5,
//     this.minChildSize = 0.25,
//     this.maxChildSize = 1.0,
//     this.expand = true,
//     this.snap = false,
//     this.animationDuration = const Duration(milliseconds: 300),
//     this.barrierColor = Colors.black54,
//     this.barrierDismissible = true,
//     this.barrierLabel,
//     super.key,
//     super.name,
//     super.arguments,
//     super.restorationId,
//   });

//   @override
//   Route<T> createRoute(BuildContext context) {
//     return _DraggableSheetRoute<T>(page: this);
//   }
// }

// class _DraggableSheetRoute<T> extends PageRoute<T> {
//   final DraggableScrollableSheetPage<T> page;

//   _DraggableSheetRoute({required this.page}) : super(settings: page);

//   @override
//   Duration get transitionDuration => page.animationDuration;

//   @override
//   bool get opaque => false;

//   @override
//   bool get barrierDismissible => page.barrierDismissible;

//   @override
//   Color? get barrierColor => page.barrierColor;

//   @override
//   String? get barrierLabel => page.barrierLabel;

//   @override
//   bool get maintainState => true;

//   @override
//   Widget buildPage(
//     BuildContext context,
//     Animation<double> animation,
//     Animation<double> secondaryAnimation,
//   ) {
//     return Align(
//       alignment: Alignment.bottomCenter,
//       child: FractionallySizedBox(
//         heightFactor: page.maxChildSize,
//         child: Material(
//           borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
//           clipBehavior: Clip.antiAlias,
//           child: DraggableScrollableSheet(
//             initialChildSize: page.initialChildSize,
//             minChildSize: page.minChildSize,
//             maxChildSize: page.maxChildSize,
//             expand: page.expand,
//             snap: page.snap,
//             builder: (context, scrollController) {
//               return page.builder(context);
//             },
//           ),
//         ),
//       ),
//     );
//   }
// }
