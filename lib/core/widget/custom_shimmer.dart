// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart'; // Your T class
import 'package:shimmer/shimmer.dart';

class AppCustomShimmer extends StatelessWidget {
  const AppCustomShimmer({
    super.key,
    this.height = 40,
    this.width,
    this.borderRadius,
  });

  final double height;
  final double? width;
  final double? borderRadius;

  @override
  Widget build(BuildContext context) {
    // context is available here!
    return Shimmer.fromColors(
      // FIX: Add (context) to T.c calls
      baseColor: T.c(context).surfaceContainerHighest.withOpacity(0.3),
      highlightColor: T.c(context).surface.withOpacity(0.4),
      child: Container(
        width: width,
        height: height,
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          // FIX: Add (context) to T.c calls
          color: T.c(context).surfaceContainerHighest,
          borderRadius: BorderRadius.circular(borderRadius ?? 12),
        ),
        child: Row(
          children: [
            SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                // FIX: Add (context) to T.c calls
                valueColor: AlwaysStoppedAnimation<Color>(
                  T.c(context).outlineVariant,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Container(
                height: 16,
                decoration: BoxDecoration(
                  // FIX: Add (context) to T.c calls
                  color: T.c(context).surface.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
