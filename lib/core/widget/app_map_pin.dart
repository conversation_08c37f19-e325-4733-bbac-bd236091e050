import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/common/blocs/current_latlng_address/current_lat_lng_address_bloc.dart';
import 'package:safari_yatri/core/theme/app_colors.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/features/passenger/core/blocs/map_movement/map_movement_cubit.dart';

/// Map picker is controlled with MapPickerController. Map pin is lifted up
/// whenever mapMoving() is called, and will be down when mapFinishedMoving()
/// is called. You can call disposeCallbacks() to release the listeners.
class AppMapPickerController {
  Function? mapMoving;
  Function? mapFinishedMoving;
  Function? stop;

  /// Manually dispose the assigned callbacks to avoid memory leaks
  void disposeCallbacks() {
    mapMoving = null;
    mapFinishedMoving = null;
    stop = null;
  }
}

/// MapPicker widget is main widget that gets map as a child.
/// It does not restrict user from using maps other than google map.
class AppMapPicker extends StatefulWidget {
  final Widget child;
  final Widget? iconWidget;
  final bool showDot;
  final AppMapPickerController mapPickerController;
  final bool showWidgetAboveDot;

  const AppMapPicker({
    super.key,
    required this.child,
    required this.mapPickerController,
    this.iconWidget,
    this.showDot = true,
    this.showWidgetAboveDot = true,
  });

  @override
  State<AppMapPicker> createState() => _MapPickerState();
}

class _MapPickerState extends State<AppMapPicker>
    with TickerProviderStateMixin {
  static const double _dotRadius = 8.0;
  static const double _pulseRadius = 24.0;

  late final AnimationController animationController;
  late final AnimationController pulseController;
  late final AnimationController scaleController;
  
  late final Animation<double> translateAnimation;
  late final Animation<double> pulseAnimation;
  late final Animation<double> scaleAnimation;
  late final Animation<double> opacityAnimation;
  
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    
    // Main pin animation
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Pulse animation for the dot
    pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Scale animation for smooth transitions
    scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );

    widget.mapPickerController.mapMoving = mapMoving;
    widget.mapPickerController.mapFinishedMoving = mapFinishedMoving;
    widget.mapPickerController.stop = stop;

    translateAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: animationController, 
      curve: Curves.easeInOutCubic,
    ));

    pulseAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: pulseController, 
      curve: Curves.easeInOutSine,
    ));

    scaleAnimation = Tween<double>(
      begin: 1,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: animationController, 
      curve: Curves.easeInOutCubic,
    ));

    opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: scaleController, 
      curve: Curves.easeInOut,
    ));

    // Start scale animation
    scaleController.forward();
  }

  void mapMoving() {
    if (mounted && !_isAnimating) {
      _isAnimating = true;
      animationController.forward();
      pulseController.stop();
    }
  }

  void mapFinishedMoving() {
    if (mounted && _isAnimating) {
      animationController.reverse().then((_) {
        if (mounted) {
          _isAnimating = false;
          // Small delay before starting pulse for smoother transition
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) {
              pulseController.repeat();
            }
          });
        }
      });
    }
  }

  /// Stops the animation and resets the state
  void stop() {
    if (mounted) {
      _isAnimating = false;
      animationController.stop();
      animationController.reset();
      pulseController.stop();
      pulseController.reset();
    }
  }

  @override
  void dispose() {
    animationController.dispose();
    pulseController.dispose();
    scaleController.dispose();
    widget.mapPickerController.disposeCallbacks();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          alignment: Alignment.center,
          children: [
            widget.child,
            Positioned(
              bottom: constraints.maxHeight * 0.5 - 10,
              child: AnimatedBuilder(
                animation: Listenable.merge([
                  animationController,
                  pulseController,
                  scaleController,
                ]),
                builder: (context, snapshot) {
                  return Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      if (widget.showDot)
                        BlocBuilder<MapMovementCubit, MapMovementState>(
                          builder: (context, state) {
                            return state == MapMovementState.stopped
                                ? const SizedBox()
                                : _buildDotWithPulse();
                          },
                        ),
                      Transform.translate(
                        offset: Offset(0, -18 * translateAnimation.value),
                        child: Transform.scale(
                          scale: scaleAnimation.value,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (widget.showWidgetAboveDot &&
                                  widget.iconWidget != null)
                                FadeTransition(
                                  opacity: opacityAnimation,
                                  child: const ShowCurrentLatLngAddress(),
                                ),
                              const SizedBox(height: 8),
                              FadeTransition(
                                opacity: opacityAnimation,
                                child: widget.iconWidget ?? const SizedBox.shrink(),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDotWithPulse() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Outer pulse ring
        AnimatedBuilder(
          animation: pulseAnimation,
          builder: (context, child) {
            return Container(
              width: _pulseRadius * pulseAnimation.value,
              height: _pulseRadius * pulseAnimation.value,
              decoration: BoxDecoration(
                color: AppColors.brandGreen.withOpacity(0.3 * (1 - pulseAnimation.value)),
                shape: BoxShape.circle,
              ),
            );
          },
        ),
        // Inner dot with shadow
        Container(
          width: _dotRadius,
          height: _dotRadius,
          decoration: BoxDecoration(
            color: AppColors.brandGreen,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: AppColors.brandGreen.withOpacity(0.4),
                blurRadius: 6,
                spreadRadius: 1,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class ShowCurrentLatLngAddress extends StatelessWidget {
  const ShowCurrentLatLngAddress({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MapMovementCubit, MapMovementState>(
      builder: (context, state) {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, -0.5),
                end: Offset.zero,
              ).animate(animation),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
          child: state == MapMovementState.moving
              ? const SizedBox(key: ValueKey('empty'))
              : _buildAddressWidget(context),
        );
      },
    );
  }

  Widget _buildAddressWidget(BuildContext context) {
    return BlocBuilder<CurrentLatLngAddressBloc, CurrentLatLngAddressState>(
      builder: (context, state) {
        return state.maybeWhen(
          loaded: (data) => _buildAddressCard(context, data),
          orElse: () => _buildLoadingCard(context),
        );
      },
    );
  }

  Widget _buildAddressCard(BuildContext context, String address) {
    return Container(
      key: const ValueKey('address'),
      constraints: const BoxConstraints(maxWidth: 280),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: T.c(context).surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: T.c(context).shadow.withOpacity(0.15),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: T.c(context).shadow.withOpacity(0.1),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: T.c(context).outline.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AppColors.brandGreen.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.location_on,
              size: 16,
              color: AppColors.brandGreen,
            ),
          ),
          const SizedBox(width: 12),
          Flexible(
            child: Text(
              address,
              style: T.t(context).bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                height: 1.2,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingCard(BuildContext context) {
    return Container(
      key: const ValueKey('loading'),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: T.c(context).surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: T.c(context).shadow.withOpacity(0.15),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.brandGreen),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'Loading location...',
            style: T.t(context).bodyMedium?.copyWith(
              color: T.c(context).onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }
}