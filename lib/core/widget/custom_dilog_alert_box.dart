import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'custom_button.dart';

Future<bool?> showConfirmationDialog({
  required BuildContext context,
  required String title,
  required String message,
  String confirmText = 'Yes',
  String cancelText = 'Cancel',
  IconData? icon,
  Color? iconColor,
  Color? confirmButtonColor,
  Color? cancelTextColor,
}) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) {
      final colorScheme = Theme.of(context).colorScheme;
      return AlertDialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
        actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        titlePadding: const EdgeInsets.fromLTRB(20, 16, 20, 8),
        title: Row(
          children: [
            if (icon != null)
              Icon(icon, color: iconColor ?? colorScheme.primary, size: 22),
            if (icon != null) const SizedBox(width: 6),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            IconButton(
              onPressed: () {
                context.pop();
              },
              icon: Icon(LucideIcons.x),
            ),
          ],
        ),
        content: Text(message, style: const TextStyle(fontSize: 14)),
        actions: [
          CustomButtonPrimary(
            title: confirmText,
            onPressed: () => Navigator.of(context).pop(true),
            backgroundColor: confirmButtonColor ?? colorScheme.primary,
            textColor: colorScheme.onPrimary,
            height: 40,
            // width: 120,
          ),
        ],
      );
    },
  );
}
