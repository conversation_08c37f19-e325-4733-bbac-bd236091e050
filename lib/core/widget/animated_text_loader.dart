import 'dart:async';
import 'package:flutter/material.dart';

enum AnimationType {
  fade,
  slideUp,
  slideDown,
  slideLeft,
  slideRight,
  scale,
  rotation,
  typewriter,
  bounceIn,
  flipX,
  flipY,
}

class AnimatedTextLoader extends StatefulWidget {
  final List<String> messages;
  final Duration switchDuration;
  final Duration animationDuration;
  final TextStyle? textStyle;
  final AnimationType animationType;
  final bool autoStart;
  final Curve curve;
  final TextAlign textAlign;
  final bool showLoadingDots;
  final Color? loadingDotsColor;

  const AnimatedTextLoader({
    super.key,
    required this.messages,
    this.switchDuration = const Duration(seconds: 2),
    this.animationDuration = const Duration(milliseconds: 800),
    this.textStyle,
    this.animationType = AnimationType.fade,
    this.autoStart = true,
    this.curve = Curves.easeInOut,
    this.textAlign = TextAlign.center,
    this.showLoadingDots = false,
    this.loadingDotsColor,
  });

  @override
  State<AnimatedTextLoader> createState() => _AnimatedTextLoaderState();
}

class _AnimatedTextLoaderState extends State<AnimatedTextLoader>
    with TickerProviderStateMixin {
  late int _currentIndex;
  late Timer _timer;
  late AnimationController _controller;
  late Animation<double> _animation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  // Typewriter animation
  late AnimationController _typewriterController;
  late Animation<int> _typewriterAnimation;
  String _displayedText = '';

  // Loading dots animation
  late AnimationController _dotsController;
  late Animation<double> _dotsAnimation;

  @override
  void initState() {
    super.initState();
    _currentIndex = 0;

    // Main animation controller
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // Typewriter animation controller
    _typewriterController = AnimationController(
      duration: Duration(
        milliseconds: widget.switchDuration.inMilliseconds - 200,
      ),
      vsync: this,
    );

    // Loading dots animation controller
    _dotsController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _setupAnimations();

    if (widget.autoStart) {
      _startAnimation();
      _startTimer();
    }
  }

  void _setupAnimations() {
    // Fade animation
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    // Slide animations
    _slideAnimation = Tween<Offset>(
      begin: _getSlideBeginOffset(),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    // Scale animation
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    // Rotation animation
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    // Typewriter animation
    _typewriterAnimation = IntTween(
      begin: 0,
      end: widget.messages[_currentIndex].length,
    ).animate(
      CurvedAnimation(parent: _typewriterController, curve: Curves.easeInOut),
    );

    // Loading dots animation
    _dotsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _dotsController, curve: Curves.easeInOut),
    );

    // Animation listeners
    _typewriterAnimation.addListener(() {
      setState(() {
        _displayedText = widget.messages[_currentIndex].substring(
          0,
          _typewriterAnimation.value,
        );
      });
    });

    if (widget.showLoadingDots) {
      _dotsController.repeat(reverse: true);
    }
  }

  Offset _getSlideBeginOffset() {
    switch (widget.animationType) {
      case AnimationType.slideUp:
        return const Offset(0, 1);
      case AnimationType.slideDown:
        return const Offset(0, -1);
      case AnimationType.slideLeft:
        return const Offset(1, 0);
      case AnimationType.slideRight:
        return const Offset(-1, 0);
      default:
        return const Offset(0, 1);
    }
  }

  void _startAnimation() {
    _controller.forward();
    if (widget.animationType == AnimationType.typewriter) {
      _typewriterController.forward();
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(widget.switchDuration, (timer) {
      _nextMessage();
    });
  }

  void _nextMessage() async {
    // Reverse current animation
    await _controller.reverse();
    if (widget.animationType == AnimationType.typewriter) {
      _typewriterController.reset();
    }

    setState(() {
      _currentIndex = (_currentIndex + 1) % widget.messages.length;
    });

    // Update typewriter animation end value
    if (widget.animationType == AnimationType.typewriter) {
      _typewriterAnimation = IntTween(
        begin: 0,
        end: widget.messages[_currentIndex].length,
      ).animate(
        CurvedAnimation(parent: _typewriterController, curve: Curves.easeInOut),
      );
      _typewriterAnimation.addListener(() {
        setState(() {
          _displayedText = widget.messages[_currentIndex].substring(
            0,
            _typewriterAnimation.value,
          );
        });
      });
    }

    // Start new animation
    _startAnimation();
  }

  Widget _buildAnimatedText() {
    final text =
        widget.animationType == AnimationType.typewriter
            ? _displayedText
            : widget.messages[_currentIndex];

    Widget textWidget = Text(
      text,
      style: widget.textStyle ?? Theme.of(context).textTheme.headlineSmall,
      textAlign: widget.textAlign,
    );

    // Add loading dots if enabled
    if (widget.showLoadingDots &&
        widget.animationType == AnimationType.typewriter) {
      textWidget = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          textWidget,
          AnimatedBuilder(
            animation: _dotsAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _dotsAnimation.value,
                child: Text(
                  '...',
                  style: widget.textStyle?.copyWith(
                    color:
                        widget.loadingDotsColor ??
                        Theme.of(context).primaryColor,
                  ),
                ),
              );
            },
          ),
        ],
      );
    }

    return textWidget;
  }

  Widget _buildWithAnimation(Widget child) {
    switch (widget.animationType) {
      case AnimationType.fade:
        return FadeTransition(opacity: _animation, child: child);

      case AnimationType.slideUp:
      case AnimationType.slideDown:
      case AnimationType.slideLeft:
      case AnimationType.slideRight:
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(opacity: _animation, child: child),
        );

      case AnimationType.scale:
        return ScaleTransition(
          scale: _scaleAnimation,
          child: FadeTransition(opacity: _animation, child: child),
        );

      case AnimationType.rotation:
        return AnimatedBuilder(
          animation: _rotationAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _rotationAnimation.value * 0.5,
              child: FadeTransition(opacity: _animation, child: child),
            );
          },
        );

      case AnimationType.bounceIn:
        return ScaleTransition(
          scale: Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
          ),
          child: child,
        );

      case AnimationType.flipX:
        return AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Transform(
              alignment: Alignment.center,
              transform:
                  Matrix4.identity()
                    ..setEntry(3, 2, 0.001)
                    ..rotateX(_controller.value * 3.14159),
              child: child,
            );
          },
        );

      case AnimationType.flipY:
        return AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Transform(
              alignment: Alignment.center,
              transform:
                  Matrix4.identity()
                    ..setEntry(3, 2, 0.001)
                    ..rotateY(_controller.value * 3.14159),
              child: child,
            );
          },
        );

      case AnimationType.typewriter:
        return FadeTransition(opacity: _animation, child: child);
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    _controller.dispose();
    _typewriterController.dispose();
    _dotsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return _buildWithAnimation(_buildAnimatedText());
      },
    );
  }
}

// Example usage widget
class AnimatedTextLoaderExample extends StatelessWidget {
  const AnimatedTextLoaderExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Animated Text Loader Demo')),
      body: const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedTextLoader(
                messages: [
                  'Loading your data...',
                  'Processing information...',
                  'Almost ready...',
                  'Finalizing...',
                ],
                animationType: AnimationType.typewriter,
                showLoadingDots: true,
                switchDuration: Duration(seconds: 3),
                textStyle: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              SizedBox(height: 40),
              AnimatedTextLoader(
                messages: [
                  'Welcome!',
                  'Let\'s get started',
                  'Amazing experience awaits',
                  'Ready to explore?',
                ],
                animationType: AnimationType.bounceIn,
                switchDuration: Duration(seconds: 2),
                curve: Curves.elasticOut,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
