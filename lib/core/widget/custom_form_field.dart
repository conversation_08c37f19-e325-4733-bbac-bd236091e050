// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:safari_yatri/core/animations/icon_animation.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart'; // Ensure this path points to your T class
import 'package:shadcn_ui/shadcn_ui.dart';

import 'package:safari_yatri/core/theme/app_styles.dart';

/// A customizable form field widget for text input.
/// It supports labels, prefix/suffix icons, country codes, validation,
/// and various styling options based on the app's theme.
class CustomFormFieldWidget extends StatelessWidget {
  final String label;
  final bool obscureText;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final Widget? suffixWidget;
  final VoidCallback? onPressed;
  final TextInputType? keys;
  final TextEditingController? controller;
  final String? countryCode;
  final VoidCallback? onCountryCodeTap;
  final String? Function(String?)? validator;
  final bool? enabled;

  const CustomFormFieldWidget({
    super.key,
    required this.label,
    this.prefixIcon,
    this.suffixIcon,
    this.suffixWidget,
    this.onPressed,
    this.controller,
    this.countryCode,
    this.onCountryCodeTap,
    this.validator,
    this.enabled,
    this.keys,
    this.obscureText = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: T
              .t(context)
              .labelMedium
              ?.copyWith(
                color: T.c(context).onSurface.withAlpha(153), // 0.6 opacity
              ),
        ),
        SizedBox(height: AppStyles.space4),
        TextFormField(
          autovalidateMode: AutovalidateMode.onUserInteraction,
          validator: validator,
          obscureText: obscureText,
          controller: controller,
          enabled: enabled,
          textInputAction: TextInputAction.next,
          keyboardType: keys,
          style: T
              .t(context)
              .bodyLarge
              ?.copyWith(color: T.c(context).onSurface),
          decoration: InputDecoration(
            prefixIconConstraints: const BoxConstraints(),
            prefixIcon: Padding(
              padding: const EdgeInsets.only(left: 14),
              child:
                  onCountryCodeTap != null
                      ? GestureDetector(
                        onTap: onCountryCodeTap,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8.0,
                            vertical: 12.0,
                          ),
                          child: Text(
                            countryCode ?? '+977',
                            style: T
                                .t(context)
                                .bodyLarge
                                ?.copyWith(color: T.c(context).outline),
                          ),
                        ),
                      )
                      : Icon(prefixIcon, size: 18, color: T.c(context).outline),
            ),
            hintText: label,
            hintStyle: T
                .t(context)
                .bodyMedium
                ?.copyWith(color: T.c(context).outline),
            enabledBorder: OutlineInputBorder(
              borderRadius: AppStyles.radiusMd,
              borderSide: BorderSide(color: T.c(context).outline),
            ),
            border: OutlineInputBorder(
              borderRadius: AppStyles.radiusMd,
              borderSide: BorderSide(
                color: T.c(context).onSurface.withAlpha(128),
              ), // 0.5
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: AppStyles.radiusMd,
              borderSide: BorderSide(color: T.c(context).primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: AppStyles.radiusMd,
              borderSide: BorderSide(color: T.c(context).error, width: 1.5),
            ),
            suffixIcon:
                suffixWidget != null
                    ? IconButton(
                      padding: EdgeInsets.zero,
                      icon: suffixWidget!,
                      onPressed: onPressed,
                      color: T.c(context).outline,
                    )
                    : null,
          ),
        ),
      ],
    );
  }
}

/// A customizable dropdown form field widget.
/// It supports labels, prefix icons, hint text, validation,
/// and styling based on the app's theme.
class CustomDropdownFormFieldWidget extends StatelessWidget {
  final String label;
  final String? value;
  final List<String> items;
  final String? hintText;
  final ValueChanged<String?>? onChanged;
  final String? Function(String?)? validator;
  final bool? enabled;
  final IconData? prefixIcon;

  const CustomDropdownFormFieldWidget({
    super.key,
    required this.label,
    required this.items,
    this.value,
    this.hintText,
    this.onChanged,
    this.validator,
    this.enabled,
    this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: T
              .t(context)
              .labelMedium
              ?.copyWith(
                color: T.c(context).onSurface.withAlpha(153), // 0.6
              ),
        ),
        SizedBox(height: AppStyles.space4),
        DropdownButtonFormField<String>(
          autovalidateMode: AutovalidateMode.onUserInteraction,
          validator: validator,
          value: value,
          onChanged: enabled == false ? null : onChanged,
          style: T
              .t(context)
              .bodyLarge
              ?.copyWith(color: T.c(context).onSurface),
          icon: Icon(Icons.keyboard_arrow_down, color: T.c(context).outline),
          dropdownColor: T.c(context).surface,
          decoration: InputDecoration(
            prefixIconConstraints: const BoxConstraints(),
            prefixIcon:
                prefixIcon != null
                    ? Padding(
                      padding: const EdgeInsets.only(left: 14),
                      child: Icon(
                        prefixIcon,
                        size: 18,
                        color: T.c(context).outline,
                      ),
                    )
                    : null,
            hintText: hintText ?? 'Select $label',
            hintStyle: T
                .t(context)
                .bodyMedium
                ?.copyWith(color: T.c(context).outline),
            enabledBorder: OutlineInputBorder(
              borderRadius: AppStyles.radiusMd,
              borderSide: BorderSide(color: T.c(context).outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: AppStyles.radiusMd,
              borderSide: BorderSide(color: T.c(context).primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: AppStyles.radiusMd,
              borderSide: BorderSide(color: T.c(context).error, width: 1.5),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: AppStyles.radiusMd,
              borderSide: BorderSide(color: T.c(context).error, width: 2),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: AppStyles.radiusMd,
              borderSide: BorderSide(
                color: T.c(context).outline.withAlpha(102), // 0.4
              ),
            ),
          ),
          items:
              items
                  .map(
                    (item) => DropdownMenuItem<String>(
                      value: item,
                      child: Text(item, style: T.t(context).bodyLarge),
                    ),
                  )
                  .toList(),
        ),
      ],
    );
  }
}

/// A specialized form field widget for password input,
/// featuring a toggleable obscure text icon.
class CustomPasswordFormFieldWidget extends StatefulWidget {
  final String label;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onPressed;
  final TextInputType? keys;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final bool? enabled;

  const CustomPasswordFormFieldWidget({
    super.key,
    required this.label,
    this.prefixIcon,
    this.suffixIcon,
    this.onPressed,
    this.keys,
    this.controller,
    this.validator,
    this.enabled,
  });

  @override
  State<CustomPasswordFormFieldWidget> createState() =>
      _CustomPasswordFormFieldWidgetState();
}

class _CustomPasswordFormFieldWidgetState
    extends State<CustomPasswordFormFieldWidget> {
  bool obscureText = true;

  @override
  Widget build(BuildContext context) {
    // This widget uses CustomFormFieldWidget internally,
    // so the theme access within CustomFormFieldWidget is handled by its own build method.
    return CustomFormFieldWidget(
      keys: widget.keys,
      label: widget.label,
      prefixIcon: widget.prefixIcon,
      suffixIcon: widget.suffixIcon,
      suffixWidget: AnimatedIconToggle(
        icon1: LucideIcons.eye,
        icon2: LucideIcons.eyeClosed,
        onToggle: () {
          setState(() {
            obscureText = !obscureText;
          });
        },
      ),
      onPressed: widget.onPressed,
      controller: widget.controller,
      validator: widget.validator,
      enabled: widget.enabled,
      obscureText: obscureText,
    );
  }
}
