import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../theme/app_styles.dart';

Widget buildDrawerButton(BuildContext context) {
  final theme = Theme.of(context);

  return Builder(
    builder: (context) {
      final scaffoldExists = Scaffold.maybeOf(context) != null;

      if (!scaffoldExists) {
        return const SizedBox.shrink();
      }

      return Container(
        height: 45,
        width: 45,
        margin: EdgeInsets.all(AppStyles.space12),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: T.c(context).surfaceContainerHighest,
          boxShadow: [
            BoxShadow(
              color:
                  theme.brightness == Brightness.dark
                      ? Colors.black.withAlpha(102) // 40% opacity
                      : Colors.black.withAlpha(25), // 10% opacity
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: <PERSON><PERSON><PERSON><PERSON><PERSON>(
          icon: Icon(
            LucideIcons.menu,
            color: T.c(context).onSurface.withAlpha(178), // 70% alpha
          ),
          onPressed: () {
            if (Scaffold.of(context).hasDrawer) {
              Scaffold.of(context).openDrawer();
            } else {
              debugPrint(
                'Warning: Attempted to open drawer, but no drawer is present in Scaffold.',
              );
            }
          },
        ),
      );
    },
  );
}
