import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/common/blocs/get_vehicle_type/get_vehicle_type_bloc.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/models/get_vehile_type.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';

class VehicleTypeDropdown extends StatefulWidget {
  final GetVehicleType? selectedVehicleType;
  final void Function(GetVehicleType?) onChanged;

  const VehicleTypeDropdown({
    super.key,
    required this.selectedVehicleType,
    required this.onChanged,
  });

  @override
  State<VehicleTypeDropdown> createState() => _VehicleTypeDropdownState();
}

class _VehicleTypeDropdownState extends State<VehicleTypeDropdown> {
  GetVehicleType? _selectedVehicleType;
  @override
  void initState() {
    super.initState();
    _selectedVehicleType = widget.selectedVehicleType;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    fetchVehicleTypes();
  }

  void fetchVehicleTypes() {
    sl<GetVehicleTypeBloc>().add(GetVehicleTypeEvent.get());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GetVehicleTypeBloc, GetVehicleTypeState>(
      builder: (context, state) {
        return state.maybeWhen(
          failure:
              (failure) =>
                  ErrorWidgetWithRetry(failure: failure, onRetry: fetchVehicleTypes),

          loaded: (data) {
            return DropdownButtonFormField<GetVehicleType>(
              value: _selectedVehicleType,
              decoration: InputDecoration(
                labelText: 'Select Vehicle Type',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppStyles.space12),
                ),
                contentPadding: EdgeInsets.symmetric(
                  vertical: AppStyles.space12,
                  horizontal: AppStyles.space16,
                ),
              ),
              items:
                  data.map((vehicleType) {
                    return DropdownMenuItem<GetVehicleType>(
                      value: vehicleType,
                      child: Text(vehicleType.vehicleTypeName),
                    );
                  }).toList(),
              onChanged: (value) {
                widget.onChanged(value);
                setState(() {
                  _selectedVehicleType = value;
                });
              },
              validator: (value) {
                if (value == null) {
                  return 'Please select a vehicle type';
                }
                return null;
              },
            );
          },
          orElse: () => const Center(child: CircularProgressIndicator()),
        );
      },
    );
  }
}
