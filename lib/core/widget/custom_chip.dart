import 'package:flutter/material.dart';

class CustomChipWidget extends StatelessWidget {
  final String chipTitle;
  const CustomChipWidget({super.key, required this.chipTitle});

  @override
  Widget build(BuildContext context) {
    return Text(
      chipTitle,
      style: TextStyle(
        fontSize: 16,
        color: Colors.green.shade800,
        fontWeight: FontWeight.w800,
      ),
    );
  }
}
