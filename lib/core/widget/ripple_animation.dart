import 'dart:math';

import 'package:flutter/material.dart';
import 'package:safari_yatri/core/theme/app_colors.dart';

class LayeredRippleEffect extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final bool isCircular;

  const LayeredRippleEffect({
    super.key,
    required this.child,
    this.duration = const Duration(seconds: 3),
    this.isCircular = true,
  });

  @override
  State<LayeredRippleEffect> createState() => _LayeredRippleEffectState();
}

class _LayeredRippleEffectState extends State<LayeredRippleEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _ripples;

  final List<Color> rippleColors = [
    AppColors.brandGreen.withAlpha(40),
    AppColors.brandGreen.withAlpha(100),
    AppColors.brandGreen.withAlpha(180),
  ];

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(vsync: this, duration: widget.duration)
      ..repeat();

    _ripples = List.generate(
      rippleColors.length,
      (index) => Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(
            index * 0.2,
            min(1.0, index * 0.2 + 0.8),
            curve: Curves.easeInOutCubicEmphasized,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          foregroundPainter: MultiRipplePainter(
            animations: _ripples,
            colors: rippleColors,
            isCircular: widget.isCircular,
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius:
                  widget.isCircular ? null : BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.blueAccent.withOpacity(0.05),
                  blurRadius: 20,
                  spreadRadius: 2,
                ),
              ],
              gradient: LinearGradient(
                colors: [
                  Colors.white.withOpacity(0.01),
                  Colors.white.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: ClipRRect(
              borderRadius:
                  widget.isCircular
                      ? BorderRadius.circular(1000)
                      : BorderRadius.circular(20),
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}

class MultiRipplePainter extends CustomPainter {
  final List<Animation<double>> animations;
  final List<Color> colors;
  final bool isCircular;

  MultiRipplePainter({
    required this.animations,
    required this.colors,
    required this.isCircular,
  }) : super(repaint: animations.first);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = sqrt(pow(size.width, 2) + pow(size.height, 2)) / 2;

    for (int i = 0; i < animations.length; i++) {
      final radius = animations[i].value * maxRadius;
      final opacity = (1 - animations[i].value).clamp(0.0, 1.0);
      final paint =
          Paint()
            ..color = colors[i].withOpacity(opacity)
            ..style = PaintingStyle.stroke
            ..strokeWidth = 5 + (animations[i].value * 3);

      if (isCircular) {
        canvas.drawCircle(center, radius, paint);
      } else {
        final rect = Rect.fromCenter(
          center: center,
          width: radius * 2,
          height: radius * 2,
        );
        canvas.drawRRect(
          RRect.fromRectAndRadius(rect, Radius.circular(28)),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
