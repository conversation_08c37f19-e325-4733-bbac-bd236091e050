import 'package:flutter/material.dart';

import '../theme/app_colors.dart';

class AnimatedIconToggle extends StatefulWidget {
  final IconData icon1;
  final IconData icon2;
  final VoidCallback onToggle;

  const AnimatedIconToggle({
    super.key,
    required this.icon1,
    required this.icon2,
    required this.onToggle,
  });

  @override
  // ignore: library_private_types_in_public_api
  _AnimatedIconToggleState createState() => _AnimatedIconToggleState();
}

class _AnimatedIconToggleState extends State<AnimatedIconToggle> {
  bool _isFirstIcon = true;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () {
        setState(() {
          _isFirstIcon = !_isFirstIcon; // Toggle between the two icons
        });
        widget.onToggle(); // Call the provided callback
      },
      icon: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (child, animation) {
          return ScaleTransition(scale: animation, child: child);
        },
        child: Icon(
          _isFirstIcon ? widget.icon1 : widget.icon2,
          size: 18,
          color: AppColors.borderColor,
          key: Value<PERSON><PERSON>(_isFirstIcon), // Unique key for animation
        ),
      ),
    );
  }
}
