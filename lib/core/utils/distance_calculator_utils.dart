import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/utils/format_distance.dart';
import 'package:safari_yatri/features/passenger/location/models/passenger_location.dart';

class AppDistanceCalculator {
  static double distanceInMeter(
    LatLngWithAddress source,
    LatLngWithAddress destination,
  ) {
    return Geolocator.distanceBetween(
      source.latitude,
      source.longitude,
      destination.latitude,
      destination.longitude,
    );
  }

  static String distanceInMeterOrKM(LatLng? origin, LatLng? destination) {
    if (origin == null || destination == null) return "";
    final distanceInMeters = Geolocator.distanceBetween(
      origin.latitude,
      origin.longitude,
      destination.latitude,
      destination.longitude,
    );

    return formatDistance(distanceInMeters.toInt());
  }
}
