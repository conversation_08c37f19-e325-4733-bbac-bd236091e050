import 'package:safari_yatri/features/admin/core/models/fare_rate_model.dart';

class FareUtils {
  /// Calculates fare based on the provided list of fare rate slabs, distance
  /// in meters and passenger count.
  ///
  /// The fare is calculated by finding the first slab in the list where the
  /// distance is less than or equal to the distance in the slab, and then
  /// calculating the fare based on the rate per meter and additional cost in
  /// the slab. If the passenger count is greater than 1 and the distance is
  /// greater than or equal to 2500 meters, a discount is applied based on the
  /// multiple passenger discount in percent in the slab.
  ///
  /// The total fare is then rounded up to the nearest 5 rupees.

  static double calculateFare({
    required List<FareRateModel> slabs,
    required double distanceInMeter,
    int passengerCount = 1,
  }) {
    double totalFare = 0.0;

    slabs.sort((a, b) => a.distanceInMeter.compareTo(b.distanceInMeter));

    for (int i = 0; i < slabs.length; i++) {
      if (distanceInMeter <= slabs[i].distanceInMeter) {
        totalFare =
            (slabs[i].perMeterRate * distanceInMeter +
                slabs[i].additionalCost) *
            passengerCount;

        if (passengerCount > 1 &&
            slabs[i].multiplePassengerDiscountInPercent > 0) {
          double discountAmount =
              totalFare * slabs[i].multiplePassengerDiscountInPercent;

          totalFare -= discountAmount;
        }

        break;
      }
    }

    int totalFareInt = totalFare.ceilToDouble().toInt();

    if (totalFareInt % 5 > 2) {
      totalFareInt = (totalFareInt ~/ 5) * 5 + 5;
    } else {
      totalFareInt = (totalFareInt ~/ 5) * 5;
    }
    return totalFareInt.toDouble();
  }
}
