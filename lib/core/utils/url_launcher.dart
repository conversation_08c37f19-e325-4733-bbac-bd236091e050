import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../common/widgets/custom_toast.dart';

Future<void> urlLauncher(Uri url) async {
  final bool isWebUrl = url.scheme == 'http' || url.scheme == 'https';

  final launchMode =
      isWebUrl ? LaunchMode.inAppBrowserView : LaunchMode.externalApplication;

  if (!await launchUrl(url, mode: launchMode)) {
    throw Exception('Could not launch $url');
  }
}

Future<void> urlLauncherWithFallback(BuildContext context, Uri url) async {
  try {
    await urlLauncher(url);
  } catch (e) {
    log('Error launching URL: $e');
    if (context.mounted) {
      CustomToast.showError('Could not open the link. Please try again later.');
    }
  }
}
