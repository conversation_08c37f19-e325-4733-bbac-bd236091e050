// Modified T.dart (or wherever your T class is)
import 'package:flutter/material.dart';

/// Now, this class requires a BuildContext to access theme data,
/// making it reactive to theme changes.
class T {
  // Private constructor to prevent direct instantiation of T.
  T._();

  // No more static _themeData or init method!

  /// Access TextTheme from the current BuildContext.
  /// Usage: T.t(context).bodyLarge
  static TextTheme t(BuildContext context) => Theme.of(context).textTheme;

  /// Access ColorScheme from the current BuildContext.
  /// Usage: T.c(context).primary
  static ColorScheme c(BuildContext context) => Theme.of(context).colorScheme;

  // You can add other theme-related helpers here that also take BuildContext
  static bool isDark(BuildContext context) =>
      Theme.of(context).brightness == Brightness.dark;
}
