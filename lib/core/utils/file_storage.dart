import 'package:path_provider/path_provider.dart';
import 'dart:io';

class FileStorage {
  Future<String> getLocalPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  Future<File> writeFile(String filename, String content) async {
    final path = await getLocalPath();
    return File('$path/$filename').writeAsString(content);
  }

  Future<String> readFile(String filename) async {
    final path = await getLocalPath();
    return File('$path/$filename').readAsString();
  }
}