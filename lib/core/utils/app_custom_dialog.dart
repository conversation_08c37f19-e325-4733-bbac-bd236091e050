import 'package:flutter/material.dart';

class AppCustomDialog {
  static bool _isDialogShowing = false;

  static void hide(BuildContext context) {
    if (_isDialogShowing) {
      _isDialogShowing = false;

      Navigator.of(context).pop();
    }
  }

  static Future<void> show(
    BuildContext context, {
    required Widget child,
  }) async {
    if (_isDialogShowing) {
      hide(context);
      await Future.delayed(const Duration(milliseconds: 100));
    }

    _isDialogShowing = true;
    if (!context.mounted) return;
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return child;
      },
    ).then((_) {
      _isDialogShowing = false;
    });
  }

  static Future<void> showWithContainer(BuildContext context) async {
    if (_isDialogShowing) {
      hide(context);

      await Future.delayed(const Duration(milliseconds: 100));
    }

    _isDialogShowing = true;
    if (!context.mounted) return;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Center(
          child: Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainer,
              boxShadow: const [
                BoxShadow(
                  color: Colors.black54,
                  blurRadius: 1,
                  offset: Offset(1, 1),
                ),
              ],
              borderRadius: BorderRadius.circular(15),
            ),
            child: Center(
              child: CircularProgressIndicator(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        );
      },
    ).then((_) {
      _isDialogShowing = false;
    });
  }

  static bool isDialogShowing() {
    return _isDialogShowing;
  }
}
