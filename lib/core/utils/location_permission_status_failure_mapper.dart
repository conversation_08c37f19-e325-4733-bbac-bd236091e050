import 'package:permission_handler/permission_handler.dart';
import 'package:safari_yatri/core/errors/location_failure.dart';

LocationFailure mapLocationFailure(PermissionStatus status) {
  switch (status) {
    case PermissionStatus.denied:
      return LocationPermissionDeniedFailure(
        message: 'Location permission is denied by the user.',
      );

    case PermissionStatus.permanentlyDenied:
      return LocationPermissionPermanentlyDeniedFailure(
        message: 'Location permission is permanently denied.',
      );

    case PermissionStatus.restricted:
      return LocationPermissionRestrictedFailure(
        message: 'Location permission is restricted by the OS.',
      );

    case PermissionStatus.limited:
      return LocationPermissionLimitedFailure(
        message: 'Location permission is limited on this device.',
      );

    case PermissionStatus.granted:
      // TODO: Handle this case.
      throw UnimplementedError();
    case PermissionStatus.provisional:
      return LocationPermissionProvisionalFailure(
        message: 'Location permission is provisional.',
      );
  }
}
