import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/models/map_markers.dart';
import 'package:safari_yatri/features/booking/models/booking_details.dart';

List<LatLng> convertCartDetailsToLatLng(List<BookingDetailModel> cartDetails) {
  // If the input list is empty, there are no LatLngs to create.
  if (cartDetails.isEmpty) {
    return [];
  }
  if (cartDetails.length == 1) {
    return [
      LatLng(
        cartDetails.first.sourceLatitude,
        cartDetails.first.sourceLongitude,
      ),
      LatLng(
        cartDetails.first.destinationLatitude,
        cartDetails.first.destinationLongitude,
      ),
    ];
  }

  final List<LatLng> latLngs = [];

  latLngs.add(
    LatLng(cartDetails.first.sourceLatitude, cartDetails.first.sourceLongitude),
  );

  for (int i = 1; i < cartDetails.length; i++) {
    final trip = cartDetails[i];
    latLngs.add(LatLng(trip.sourceLatitude, trip.sourceLongitude));
  }

  final lastTrip = cartDetails.last;

  latLngs.add(
    LatLng(lastTrip.destinationLatitude, lastTrip.destinationLongitude),
  );

  return latLngs;
}

// The original convertCartDetailsToMarkers function remains unchanged as it handles markers
// based on source and all destinations, which is a common requirement for markers.
List<MapMarker> convertCartDetailsToMarkers(
  List<BookingDetailModel> cartDetails,
) {
  if (cartDetails.isEmpty) {
    return [];
  }

  final List<MapMarker> markers = [];

  // Add the source of the VERY FIRST trip as the starting point of the whole journey.
  final firstTrip = cartDetails.first;
  markers.add(
    MapMarker(
      latitude: firstTrip.sourceLatitude,
      longitude: firstTrip.sourceLongitude,
      label: 'Start: ${firstTrip.sourceAddress}',
    ),
  );

  // Iterate through ALL trips and add their DESTINATION points.
  // This creates the sequence of stops.
  for (int i = 0; i < cartDetails.length; i++) {
    final trip = cartDetails[i];
    final isLastStop = i == cartDetails.length - 1;

    markers.add(
      MapMarker(
        latitude: trip.destinationLatitude,
        longitude: trip.destinationLongitude,
        label:
            isLastStop
                ? 'End: ${trip.destinationAddress}'
                : 'Stop ${i + 1}: ${trip.destinationAddress}',
      ),
    );
  }

  return markers;
}
