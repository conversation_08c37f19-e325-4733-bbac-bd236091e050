import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class AppDateTimeUtils {
  /// Format: 2025-06-15
  static String getDateInYYMMDD(DateTime datetime) {
    return DateFormat('yyyy-MM-dd').format(datetime);
  }

  /// Format: 02:05 pm
  static String getTimeInHHMMAndPeriod(TimeOfDay? timeOfDay) {
    if (timeOfDay == null) return '00:00 am';
    final hour = timeOfDay.hourOfPeriod.toString().padLeft(2, '0');
    final minute = timeOfDay.minute.toString().padLeft(2, '0');
    final period = timeOfDay.period == DayPeriod.am ? 'am' : 'pm';
    return "$hour:$minute $period";
  }

  /// Convert a given DateTime into a "time ago" string
  static String timeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final diff = now.difference(dateTime);

    if (diff.inSeconds < 60) return 'just now';
    if (diff.inMinutes < 60) {
      return '${diff.inMinutes} minute${diff.inMinutes == 1 ? '' : 's'} ago';
    }
    if (diff.inHours < 24) {
      return '${diff.inHours} hour${diff.inHours == 1 ? '' : 's'} ago';
    }
    if (diff.inDays < 7) {
      return '${diff.inDays} day${diff.inDays == 1 ? '' : 's'} ago';
    }
    if (diff.inDays < 30) {
      final weeks = (diff.inDays / 7).floor();
      return '$weeks week${weeks == 1 ? '' : 's'} ago';
    }
    if (diff.inDays < 365) {
      final months = (diff.inDays / 30).floor();
      return '$months month${months == 1 ? '' : 's'} ago';
    }

    final years = (diff.inDays / 365).floor();
    return '$years year${years == 1 ? '' : 's'} ago';
  }

  /// Parse date and time (e.g. "2025-06-15", "14:9 pm") into DateTime
  static DateTime getTimeFromString(String date, String time) {
    final cleanedTime = _cleanTime(time);
    final dateTimeString = "$date $cleanedTime";

    try {
      return DateFormat("yyyy-MM-dd hh:mm a").parse(dateTimeString);
    } catch (e) {
      // fallback if AM/PM is missing (assuming 24-hour time)
      try {
        return DateFormat("yyyy-MM-dd HH:mm").parse("$date $time");
      } catch (e) {
        return DateTime.now(); // fallback
      }
    }
  }

  /// Get "time ago" directly from date and time strings
  static String getTimeAgoWithFromString(String date, String time) {
    final dateTime = getTimeFromString(date, time);
    return timeAgo(dateTime);
  }

  /// Normalizes a string like "14:9 pm" to "02:09 pm"
  static String _cleanTime(String time) {
    final regex = RegExp(
      r"(\d{1,2}):(\d{1,2})\s?(am|pm)",
      caseSensitive: false,
    );
    final match = regex.firstMatch(time.toLowerCase());

    if (match != null) {
      int hour = int.parse(match.group(1)!);
      int minute = int.parse(match.group(2)!);
      String period = match.group(3)!;

      if (hour > 12) hour -= 12;
      return "${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period";
    }

    return time; // return as-is if no match
  }

  ///it will only work on when we have AM and PM period
  static TimeOfDay? parseTimeOfDayFromString(String timeString) {
    try {
      final parts = timeString.trim().split(' '); // ["05:30", "AM"]
      if (parts.length != 2) return null;

      final timePart = parts[0]; // "05:30"
      final period = parts[1].toUpperCase(); // "AM" or "PM"

      final timeParts = timePart.split(':');
      if (timeParts.length != 2) return null;

      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);

      // Convert to 24-hour format
      if (period == 'PM' && hour != 12) {
        hour += 12;
      } else if (period == 'AM' && hour == 12) {
        hour = 0;
      }

      return TimeOfDay(hour: hour, minute: minute);
    } catch (e) {
      print('Error parsing time string: $e');
      return null;
    }
  }
}
