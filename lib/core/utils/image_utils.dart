import 'dart:convert';
import 'dart:io' show File;
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:image_picker/image_picker.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:safari_yatri/common/extensions/image_mime_type.dart';

enum ImageSourceType { gallery, camera }

class ImageUtility {
  static final ImagePicker _picker = ImagePicker();

  /// Picks an image from camera or gallery
  static Future<XFile?> pickImage({required ImageSourceType sourceType}) async {
    try {
      final ImageSource source =
          sourceType == ImageSourceType.gallery
              ? ImageSource.gallery
              : ImageSource.camera;

      final XFile? pickedFile = await _picker.pickImage(
        source: source,
        requestFullMetadata: false,
      );

      if (pickedFile == null) {
        debugPrint('No image selected.');
        return null;
      }

      return pickedFile;
    } catch (e, stackTrace) {
      debugPrint('Error picking image: $e\n$stackTrace');
      return null;
    }
  }

  /// Show image source picker dialog
  static Future<XFile?> showImageSourceDialog(BuildContext context) async {
    return await showDialog<XFile?>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Select Image Source'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Gallery'),
                onTap: () async {
                  final result = await pickImage(
                    sourceType: ImageSourceType.gallery,
                  );
                  if (!dialogContext.mounted) return;
                  Navigator.of(dialogContext).pop(result);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Camera'),
                onTap: () async {
                  final result = await pickImage(
                    sourceType: ImageSourceType.camera,
                  );
                  if (!dialogContext.mounted) return;
                  Navigator.of(dialogContext).pop(result);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// Display image from XFile
  static Widget displayImageFromXFile(XFile? imageFile) {
    if (imageFile == null) {
      return const Text('No image selected.');
    }

    if (kIsWeb) {
      return Image.network(imageFile.path, fit: BoxFit.cover);
    } else {
      return Image.file(File(imageFile.path), fit: BoxFit.cover);
    }
  }

  static Widget displayImageFromBase64(
    String? base64Str, {
    BoxFit fit = BoxFit.cover,
  }) {
    if (base64Str == null || base64Str.isEmpty || base64Str == ' ') {
      return Container(color: Colors.grey[300]);
    }

    try {
      final cleanedBase64 =
          base64Str.contains(',') ? base64Str.split(',').last : base64Str;
      Uint8List bytes = base64Decode(cleanedBase64);
      return Image.memory(bytes, fit: fit);
    } catch (e) {
      return Container(color: Colors.grey[300]);
    }
  }

  /// Convert XFile to File (mobile only)
  File? xFileToFile(XFile? xFile) {
    if (xFile == null || kIsWeb) return null;
    return File(xFile.path);
  }

  /// Get image bytes from XFile
  static Future<Uint8List?> getImageBytes(XFile? xFile) async {
    if (xFile == null) return null;
    return await xFile.readAsBytes();
  }

  /// Convert image to base64
  static Future<String?> getImageBase64(XFile? xFile) async {
    final bytes = await getImageBytes(xFile);
    return bytes != null ? base64Encode(bytes) : null;
  }

  /// Compress image to under 40 KB (target between 10-40 KB)
  static Future<Uint8List?> compressImageToTargetSize(
    XFile? xFile, {
    int maxSizeKB = 40,
  }) async {
    if (xFile == null) return null;

    Uint8List imageData = await xFile.readAsBytes();

    int quality = 90;
    Uint8List? compressed;

    while (quality > 10) {
      compressed = await FlutterImageCompress.compressWithList(
        imageData,
        quality: quality,
      );
      if ((compressed.lengthInBytes / 1024) <= maxSizeKB) break;
      quality -= 10;
    }

    return compressed;
  }

  /// Get compressed base64 image under 40 KB
  static Future<String?> getCompressedBase64(
    XFile? xFile, {
    int maxSizeKB = 40,
  }) async {
    final compressedBytes = await compressImageToTargetSize(
      xFile,
      maxSizeKB: maxSizeKB,
    );
    return compressedBytes != null ? base64Encode(compressedBytes) : null;
  }

  /// Display compressed image from XFile
  static Future<Widget> displayCompressedImage(XFile? xFile) async {
    final compressedBytes = await compressImageToTargetSize(xFile);
    if (compressedBytes == null) return const Text('Compression failed');
    return Image.memory(compressedBytes, fit: BoxFit.cover);
  }

  //##------------------GET COMPRESSED IMAGE DATA BASE64------------------##
  static Future<String?> getCompressedImageDataBase64WithMimeType(
    XFile? xFile, {
    int maxSizeKB = 16,
  }) async {
    final compressedBytes = await compressImageToTargetSize(
      xFile,
      maxSizeKB: maxSizeKB,
    );
    if (compressedBytes == null) return null;
    final base64String = base64Encode(compressedBytes);
    return "data:${xFile!.imageMimeType};base64,$base64String";
  }
}
