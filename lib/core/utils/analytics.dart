import 'package:firebase_analytics/firebase_analytics.dart';

// Initialize Firebase Analytics with advertising disabled
// Future<void> initializeAnalytics() async {
//   FirebaseAnalytics analytics = FirebaseAnalytics.instance;

//   // Disable advertising ID collection
//   await analytics.setAnalyticsCollectionEnabled(true);

//   // Disable advertising personalization
//   await analytics.setUserProperty(
//     name: 'allow_ad_personalization_signals',
//     value: 'false'
//   );
// }

/// Initialize Firebase Analytics with advertising enabled
Future<void> initializeAnalytics() async {
  try {
    FirebaseAnalytics analytics = FirebaseAnalytics.instance;

    // Enable analytics collection
    await analytics.setAnalyticsCollectionEnabled(true);

    // Enable advertising personalization (since you're using advertising)
    await analytics.setUserProperty(
      name: 'allow_ad_personalization_signals',
      value: 'true',
    );

    print('Firebase Analytics initialized successfully');
  } catch (e) {
    print('Error initializing Firebase Analytics: $e');
  }
}
