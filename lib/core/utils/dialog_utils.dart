import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class DialogUtil {
  static Future<void> showAdaptiveDialog({
    required BuildContext context,
    required String title,
    required String content,
    String confirmText = 'OK',
    VoidCallback? onConfirm,
  }) async {
    final platform = Theme.of(context).platform;

    if (platform == TargetPlatform.iOS) {
      await showCupertinoDialog(
        context: context,
        builder:
            (_) => CupertinoAlertDialog(
              title: Text(title),
              content: Text(content),
              actions: [
                CupertinoDialogAction(
                  child: Text(confirmText),
                  onPressed: () {
                    Navigator.of(context).pop();
                    onConfirm?.call();
                  },
                ),
              ],
            ),
      );
    } else {
      await showDialog(
        context: context,
        builder:
            (_) => AlertDialog(
              title: Text(title),
              content: Text(content),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    onConfirm?.call();
                  },
                  child: Text(confirmText),
                ),
              ],
            ),
      );
    }
  }

  static Future<bool> showConfirmDialog({
    required BuildContext context,
    required String title,
    required String content,
    String confirmText = 'Yes',
    String cancelText = 'No',
  }) async {
    final platform = Theme.of(context).platform;

    return await (platform == TargetPlatform.iOS
            ? showCupertinoDialog(
              context: context,
              builder:
                  (_) => CupertinoAlertDialog(
                    title: Text(title),
                    content: Text(content),
                    actions: [
                      CupertinoDialogAction(
                        isDefaultAction: true,
                        child: Text(cancelText),
                        onPressed: () => Navigator.of(context).pop(false),
                      ),
                      CupertinoDialogAction(
                        child: Text(confirmText),
                        onPressed: () => Navigator.of(context).pop(true),
                      ),
                    ],
                  ),
            )
            : showDialog(
              context: context,
              builder:
                  (_) => AlertDialog(
                    title: Text(title),
                    content: Text(content),
                    actions: [
                      TextButton(
                        child: Text(cancelText),
                        onPressed: () => Navigator.of(context).pop(false),
                      ),
                      ElevatedButton(
                        child: Text(confirmText),
                        onPressed: () => Navigator.of(context).pop(true),
                      ),
                    ],
                  ),
            )) ??
        false;
  }
}
