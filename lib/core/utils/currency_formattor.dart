import 'package:intl/intl.dart';

class CurrencyFormatter {
  static String format(num amount, {String? locale, String? currencyCode}) {
    locale ??= 'ne_NP';
    currencyCode ??= 'NPR';

    String? symbol;
    if (currencyCode == 'NPR') {
      if (locale == 'ne_NP') {
        symbol = 'रु';
      } else {
        symbol = 'Rs';
      }
    }

    final format = NumberFormat.currency(
      locale: locale,
      symbol: symbol,
      name: currencyCode,
    );

    final formatted = format.format(amount);

    return locale == 'ne_NP' ? _toDevanagariDigits(formatted) : formatted;
  }

  static String _toDevanagariDigits(String input) {
    const devanagariDigits = ['०', '१', '२', '३', '४', '५', '६', '७', '८', '९'];
    return input.replaceAllMapped(RegExp(r'\d'), (match) {
      return devanagariDigits[int.parse(match.group(0)!)];
    });
  }
}
