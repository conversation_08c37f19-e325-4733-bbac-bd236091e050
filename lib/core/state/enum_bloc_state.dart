// import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:safari_yatri/core/errors/failure.dart';

// part 'enum_bloc_state.freezed.dart';

// enum BlocStatus { initial, loading, success, failure }

// @freezed
// class EnumBlocState<T> with _$EnumBlocState<T> {
//   const factory EnumBlocState({
//     required BlocStatus status,
//     T? data,
//     Failure? failure,
//   }) = _EnumBlocState<T>;

//   factory EnumBlocState.initial() =>
//       EnumBlocState<T>(status: BlocStatus.initial, data: null, failure: null);
      
//         @override
//         // TODO: implement data
//         T? get data => throw UnimplementedError();
      
//         @override
//         // TODO: implement failure
//         Failure? get failure => throw UnimplementedError();
      
//         @override
//         // TODO: implement status
//         BlocStatus get status => throw UnimplementedError();
// }
