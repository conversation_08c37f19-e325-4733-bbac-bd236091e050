import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/extensions/path_extension.dart';
import 'package:safari_yatri/core/models/ride_shift_model.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/router/app_router.dart';
import 'package:safari_yatri/features/admin/core/models/office_model.dart';
import 'package:safari_yatri/features/admin/dashboard/pages/admin_dashboard_page.dart'
    as dashboard;
import 'package:safari_yatri/features/admin/core/layout/widgets/admin_layout_scaffold.dart';
import 'package:safari_yatri/features/admin/ride_shift/ride_shift_management_page.dart';
import 'package:safari_yatri/features/admin/settings/pages/office_settings_combine_page.dart'
    as office_settings;
import 'package:safari_yatri/features/admin/payment/pages/admin_payment_section_page.dart'
    as payment;
import 'package:safari_yatri/features/admin/users/pages/pending_riders_detail_page.dart';
import 'package:safari_yatri/features/admin/users/pages/rider_management_page.dart';
import 'package:safari_yatri/features/admin/users/pages/user_detail_page.dart';

import 'package:safari_yatri/features/admin/users/pages/user_list_page.dart';

import '../../features/admin/ride_shift/ride_shift_details_page.dart';
import '../../features/admin/ride_shift/ride_shift_form_page.dart';
import '../../features/admin/settings/pages/notification_page.dart';
import '../../features/admin/settings/pages/office_management_page.dart';

// Navigator keys for each branch
final _dashboardNavigatorKey = GlobalKey<NavigatorState>(
  debugLabel: 'dashboard',
);
final _usersNavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'users');
final _settingsNavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'settings');
final _paymentsNavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'payments');
final _rolesNavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'roles');

List<RouteBase> adminAppRoutes = [
  // User detail page
  GoRoute(
    path: "${AppRoutesName.userDetail.path}/:id",
    name: AppRoutesName.userDetail,
    builder: (context, state) {
      final userId = int.parse(state.pathParameters['id']!);
      return UserDetailPage(userId: userId);
    },
  ),

  // Admin dashboard
  StatefulShellRoute.indexedStack(
    builder: (context, state, navigationShell) {
      return AdminLayoutScaffold(navigationShell: navigationShell);
    },
    branches: [
      StatefulShellBranch(
        navigatorKey: _dashboardNavigatorKey,
        routes: [
          GoRoute(
            path: AppRoutesName.adminDashboard.path,
            name: AppRoutesName.adminDashboard,
            builder: (context, state) => const dashboard.AdminDashboardPage(),
          ),
        ],
      ),
      StatefulShellBranch(
        navigatorKey: _usersNavigatorKey,
        routes: [
          GoRoute(
            path: AppRoutesName.userList.path,
            name: AppRoutesName.userList,
            builder: (context, state) => const UserListPage(),
          ),
        ],
      ),
      StatefulShellBranch(
        navigatorKey: _settingsNavigatorKey,
        routes: [
          GoRoute(
            path: AppRoutesName.adminOfficeSettings.path,
            name: AppRoutesName.adminOfficeSettings,
            routes: [
              GoRoute(
                parentNavigatorKey: rootNavigatorKey,
                path: AppRoutesName.adminOfficeSettingsEdit.path,
                name: AppRoutesName.adminOfficeSettingsEdit,
                builder: (context, state) {
                  final officeSetting = state.extra as OfficeModel;
                  return office_settings.EditOfficeSettingsContent(
                    settings: officeSetting,
                  );
                },
              ),

              GoRoute(
                parentNavigatorKey: rootNavigatorKey,
                path: AppRoutesName.officeManagement.path,
                name: AppRoutesName.officeManagement,
                builder: (context, state) {
                  return OfficeManagementPage();
                },
              ),
              GoRoute(
                parentNavigatorKey: rootNavigatorKey,
                path: AppRoutesName.notification.path,
                name: AppRoutesName.notification,
                builder: (context, state) {
                  return NotificationSettingsPage();
                },
              ),
              GoRoute(
                parentNavigatorKey: rootNavigatorKey,
                path: AppRoutesName.rideShiftManagement.path,
                name: AppRoutesName.rideShiftManagement,
                builder: (context, state) {
                  return RideShiftManagementPage();
                },
              ),
              GoRoute(
                parentNavigatorKey: rootNavigatorKey,
                path: AppRoutesName.rideShiftForm.path,
                name: AppRoutesName.rideShiftForm,
                builder: (context, state) {
                  return ShiftDetailsFormPage();
                },
              ),
              GoRoute(
                parentNavigatorKey: rootNavigatorKey,
                path: AppRoutesName.rideShiftDetails.path,
                name: AppRoutesName.rideShiftDetails,
                builder: (context, state) {
                  final rideShift = state.extra as RideShiftModel;
                  return RideShiftDetailPage(rideShift: rideShift);
                },
              ),
            ],
            builder:
                (context, state) =>
                    const office_settings.OfficeSettingsCombinePage(),
          ),
        ],
      ),
      StatefulShellBranch(
        navigatorKey: _paymentsNavigatorKey,
        routes: [
          GoRoute(
            path: AppRoutesName.adminPayments.path,
            name: AppRoutesName.adminPayments,
            builder:
                (context, state) => const payment.AdminPaymentSectionPage(),
          ),
        ],
      ),
      StatefulShellBranch(
        navigatorKey: _rolesNavigatorKey,
        routes: [
          GoRoute(
            path: AppRoutesName.riderManagement.path,
            name: AppRoutesName.riderManagement,
            builder: (context, state) => RiderManagementPage(),
            routes: [
              GoRoute(
                parentNavigatorKey: rootNavigatorKey,
                path: "/:id",
                name: AppRoutesName.pendingRiderDetails,
                builder: (context, state) {
                  final userId = int.parse(state.pathParameters['id']!);
                  return PendingRidersDetailPage(userId: userId);
                },
              ),
            ],
          ),
        ],
      ),
    ],
  ),
];
