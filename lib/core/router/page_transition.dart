import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

enum PageTransitionType {
  leftToRight,
  rightToLeft,
  bottomToTop,
  topToBottom,
  fade,
}

CustomTransitionPage<T> buildPageWithTransition<T>({
  required Widget child,
  required PageTransitionType transitionType,
}) {
  return CustomTransitionPage<T>(
    child: child,
    transitionDuration: const Duration(milliseconds: 200), // 👈 Duration here
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      const offset = 1.0;

      switch (transitionType) {
        case PageTransitionType.leftToRight:
          return SlideTransition(
            position: Tween(
              begin: const Offset(-offset, 0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );

        case PageTransitionType.rightToLeft:
          return SlideTransition(
            position: Tween(
              begin: const Offset(offset, 0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );

        case PageTransitionType.bottomToTop:
          return SlideTransition(
            position: Tween(
              begin: const Offset(0, offset),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );

        case PageTransitionType.topToBottom:
          return SlideTransition(
            position: Tween(
              begin: const Offset(0, -offset),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );

        case PageTransitionType.fade:
          return FadeTransition(opacity: animation, child: child);
      }
    },
  );
}
