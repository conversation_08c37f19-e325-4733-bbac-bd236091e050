import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/extensions/path_extension.dart';
import 'package:safari_yatri/core/config/env_config.dart';
import 'package:safari_yatri/core/router/admin_app_router.dart';
import 'package:safari_yatri/core/router/driver_app_router.dart';
import 'package:safari_yatri/core/router/passenger_app_router.dart';
import 'package:safari_yatri/core/router/general_app_router.dart';
import 'package:safari_yatri/core/services/cache_service.dart';
import 'package:safari_yatri/core/services/once_cache_service.dart';
import 'app_route_names.dart';

final rootNavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'rootNavigator');

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: AppRoutesName.languageSelection.path,
    navigatorKey: rootNavigatorKey,
    debugLogDiagnostics:  EnvConfig.instance.enableLogging,

    redirect: (context, state) async {
      // CacheService.instance.clearTokenData();
      // OnceCacheService.clear();

      final token = await CacheService.instance.getAuthToken();
      final onBoardingCompleted = await OnceCacheService.get();

      bool isAuthPath =
          state.matchedLocation == AppRoutesName.signIn.path ||
          state.matchedLocation == AppRoutesName.signUp.path ||
          state.matchedLocation == AppRoutesName.forgetPassword.path ||
          state.matchedLocation == AppRoutesName.verifyOpt.path;

      // Allow access to language selection at the very beginning
      if (state.matchedLocation == AppRoutesName.languageSelection.path) {
        return null; // don't redirect
      }

      if (onBoardingCompleted == null &&
          !state.matchedLocation.startsWith(AppRoutesName.onBoarding.path)) {
        return AppRoutesName.languageSelection.path;
      }

      if (token == null && onBoardingCompleted != null) {
        if (!isAuthPath) {
          return AppRoutesName.signIn.path;
        }
      }
      return null;
    },
    routes: [
      ...adminAppRoutes,
      ...driverAppRoutes,
      ...passengerAppRoutes,
      ...generalAppRoutes,
    ],
  );
}

