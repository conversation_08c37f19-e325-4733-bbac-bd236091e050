import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/extensions/path_extension.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/services/driver_navigation_mode_service.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/booking/models/new_passenger_view_model.dart';
import 'package:safari_yatri/features/driver/request_for_driver/pages/contact_information_page.dart';
import 'package:safari_yatri/features/driver/request_for_driver/pages/document_page.dart';
import 'package:safari_yatri/features/driver/request_for_driver/pages/document_review_page.dart';
import 'package:safari_yatri/features/driver/request_for_driver/pages/selfie_page.dart';
import 'package:safari_yatri/features/driver/request_for_driver/pages/vehicle_detail_page.dart';
import 'package:safari_yatri/features/driver/request_for_driver/pages/whats_your_name_page.dart';
import 'package:safari_yatri/features/driver/home/<USER>/driver_home_page.dart';
import 'package:safari_yatri/features/driver/ride/pages/my_passenger_trips.dart';
import 'package:safari_yatri/features/driver/ride/pages/ride_tracking_page_for_driver.dart';

import '../../features/driver/home/<USER>/rate_passenger_page.dart';
import '../../features/driver/home/<USER>/ride_request_details_page.dart';
import '../../features/driver/request_for_driver/pages/ride_sharing_page.dart';
import '../../features/driver/home/<USER>/ride_completed_page.dart';
import '../../features/role/services/remote_role_service.dart';

import '../constant/string_constant.dart';
import '../widget/bottom_sheet_page.dart';

List<GoRoute> driverAppRoutes = [
  GoRoute(
    path: AppRoutesName.driverHome.path,
    name: AppRoutesName.driverHome,
    redirect: (context, state) async {
      final isSwitchToPassenger =
          await RiderLocallySwitchingModeServices.isSwitchToPassenger();
      if (isSwitchToPassenger) {
        return AppRoutesName.passengerHome.path;
      }
      final role = RemoteRoleService.instance.getUserRole();

      if (role == kPassengerRole) {
        return AppRoutesName.passengerHome.path;
      } else if (role == kRiderRole) {
        return null;
      } else if (role == kAdminRole) {
        return AppRoutesName.adminDashboard.path;
      }

      return null;
    },
    builder: (context, state) => const DriverHomePage(),
  ),
  GoRoute(
    path: AppRoutesName.driverDocumentPage.path,
    name: AppRoutesName.driverDocumentPage,
    redirect: (context, state) async {
      return null;
    },
    builder: (context, state) => const DocumentPage(),
  ),
  GoRoute(
    path: AppRoutesName.whatsYourNamePage.path,
    name: AppRoutesName.whatsYourNamePage,
    builder: (context, state) => const WhatsYourNamePage(),
  ),
  GoRoute(
    path: AppRoutesName.contactInformationPage.path,
    name: AppRoutesName.contactInformationPage,

    builder: (context, state) => const ContactInformationPage(),
  ),
  GoRoute(
    path: AppRoutesName.vehicleDetailsPage.path,
    name: AppRoutesName.vehicleDetailsPage,

    builder: (context, state) => const VehicleDetailPage(),
  ),
  GoRoute(
    path: AppRoutesName.documentReviewPage.path,
    name: AppRoutesName.documentReviewPage,
    builder: (context, state) => const DocumentReviewPage(),
  ),
  GoRoute(
    path: AppRoutesName.selfePage.path,
    name: AppRoutesName.selfePage,
    builder: (context, state) => const SelfiePage(),
  ),
  GoRoute(
    path: AppRoutesName.rideSummaryPage.path,
    name: AppRoutesName.rideSummaryPage,
    builder: (context, state) => const RideSummaryPage(),
  ),
  GoRoute(
    path: AppRoutesName.rideCompletedScreen.path,
    name: AppRoutesName.rideCompletedScreen,
    builder: (context, state) => const RideCompletedScreen(),
  ),
  GoRoute(
    path: AppRoutesName.ratePassengerPage.path,
    name: AppRoutesName.ratePassengerPage,
    builder: (context, state) => const RatePassengerPage(),
  ),
  GoRoute(
    path: AppRoutesName.rideRequestDetailsPage.path,
    name: AppRoutesName.rideRequestDetailsPage,
    pageBuilder: (context, state) {
      final ride = state.extra as NewPassengerModel;
      return BottomSheetPage(
        heightFactor: 0.95,
        showDragIcon: false,
        barrierDismissible: false,
        builder: (context) => RideRequestDetailsPage(ride: ride),
      );
    },
  ),

  GoRoute(
    path: AppRoutesName.rideTrackingPageForDriver.path,
    name: AppRoutesName.rideTrackingPageForDriver,
    builder: (context, state) {
      final extra = state.extra as Map<String, dynamic>;
      final bookingModel = extra['bookingModel'] as BookingModel;
      final newPassenger = extra['newPassengerModel'] as NewPassengerModel;
      return RideTrackingPageForDriver(
        newPassengerModel: newPassenger,
        bookingModel: bookingModel,
      );
    },
  ),

  GoRoute(
    path: AppRoutesName.myPassengerTrips.path,
    name: AppRoutesName.myPassengerTrips,
    builder: (context, state) => const MyPassengerTripsPage(),
  ),
];
