import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';

class ShowcaseService {
  static const _key = 'passenger_home_showcase_completed';

  final List<GlobalKey> showcaseKeys;
  final BuildContext context;

  ShowcaseService({required this.context, required this.showcaseKeys});

  Future<void> startIfFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    final completed = prefs.getBool(_key) ?? false;

    if (!completed) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ShowCaseWidget.of(context).startShowCase(showcaseKeys);
      });
      await prefs.setBool(_key, true);
    }
  }

  /// Optional: for debugging/testing, to reset and show again
  static Future<void> resetShowcase() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_key);
  }
}
