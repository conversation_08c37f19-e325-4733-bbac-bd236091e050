import 'dart:convert';
import 'package:hive/hive.dart';

class CoreLocalDataSource {
  static final CoreLocalDataSource _instance = CoreLocalDataSource._internal();
  factory CoreLocalDataSource() => _instance;

  static late final Box _box;

  CoreLocalDataSource._internal();

  static Future<void> init() async {
    if (!Hive.isBoxOpen('core_cache')) {
      _box = await Hive.openBox('core_cache');
    } else {
      _box = Hive.box('core_cache');
    }
  }

  Future<void> saveModelList(
    String key,
    List<Map<String, dynamic>> data,
  ) async {
    final jsonString = json.encode(
      data,
    ); // Converts list of maps to JSON string
    await _box.put(key, jsonString);
  }

  /// Save any data as JSON-encoded string
  Future<void> saveModel(String key, Map<String, dynamic> data) async {
    final jsonString = json.encode(data);
    await _box.put(key, jsonString);
  }

  /// Generic method to get and decode model using custom fromJson
  T? getModel<T>(String key, T Function(Map<String, dynamic>) fromJson) {
    final jsonString = _box.get(key);
    if (jsonString == null) return null;
    final Map<String, dynamic> jsonMap = json.decode(jsonString);
    return fromJson(jsonMap);
  }

  List<T> getModelList<T>(
    String key,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    final jsonString = _box.get(key);
    if (jsonString == null) return [];

    final List<dynamic> jsonList = json.decode(jsonString);
    return jsonList.map((e) => fromJson(e as Map<String, dynamic>)).toList();
  }

  /// Save a timestamp for caching expiry
  Future<void> setTimestamp(String key, DateTime time) async {
    await _box.put('${key}_timestamp', time.toIso8601String());
  }

  /// Get timestamp for validation
  DateTime? getTimestamp(String key) {
    final raw = _box.get('${key}_timestamp');
    return raw != null ? DateTime.tryParse(raw) : null;
  }

  /// Clear cached value
  Future<void> clear(String key) async {
    await _box.delete(key);
    await _box.delete('${key}_timestamp');
  }

  Future<void> clearAllBox() async {
    await _box.clear();
  }
}
