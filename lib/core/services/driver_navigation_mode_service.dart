import 'package:shared_preferences/shared_preferences.dart';

///Helps to switch rider to passenger locally
///
class RiderLocallySwitchingModeServices {
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  static Future<void> switchLocallyToPassenger() async {
    await _ensureInitialized();
    await _prefs!.setString('rider_locally_switching_mode', 'passenger');
  }

  static Future<bool> isSwitchToPassenger() async {
    await _ensureInitialized();
    final mode = _prefs!.getString('rider_locally_switching_mode');
    return mode == 'passenger';
  }

  static Future<void> clear() async {
    await _ensureInitialized();
    await _prefs!.remove('rider_locally_switching_mode');
  }

  static Future<void> _ensureInitialized() async {
    if (_prefs == null) {
      await init();
    }
  }
}
