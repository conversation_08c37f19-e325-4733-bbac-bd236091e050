import 'package:flutter/widgets.dart';
import 'polling_service.dart';

class PollingObserverService with WidgetsBindingObserver {
  final PollingService pollingService;

  PollingObserverService(this.pollingService) {
    WidgetsBinding.instance.addObserver(this);
  }

  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        for (final id in pollingService.activeTaskIds) {
          pollingService.start(id);
        }
        break;
      case AppLifecycleState.paused:
        for (final id in pollingService.activeTaskIds) {
          pollingService.stop(id);
        }
        break;
      default:
        break;
    }
  }
}
