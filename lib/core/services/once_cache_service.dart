import 'package:shared_preferences/shared_preferences.dart';

class OnceCacheService {
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  static Future<void> set() async {
    if (_prefs == null) await init();
    await _prefs!.setString('onboarding_completed', 'completed');
  }

  static Future<String?> get() async {
    if (_prefs == null) await init();
    return _prefs!.getString('onboarding_completed');
  }

  static Future<void> clear() async {
    if (_prefs == null) await init();
    await _prefs!.remove('onboarding_completed');
  }
}
