import 'package:safari_yatri/core/errors/failure.dart';

abstract class LocationFailure extends Failure {
  const LocationFailure({
    required super.message,
    super.statusCode,
    super.exception,
  });
}

class LocationServiceDisabledFailure extends LocationFailure {
  const LocationServiceDisabledFailure({
    required super.message,
    super.statusCode,
    super.exception,
  });
}

class LocationPermissionDeniedFailure extends LocationFailure {
  const LocationPermissionDeniedFailure({
    required super.message,
    super.statusCode,
    super.exception,
  });
}

class LocationPermissionPermanentlyDeniedFailure extends LocationFailure {
  const LocationPermissionPermanentlyDeniedFailure({
    required super.message,
    super.statusCode,
    super.exception,
  }) : super();
}

class LocationPermissionRestrictedFailure extends LocationFailure {
  const LocationPermissionRestrictedFailure({
    required super.message,
    super.statusCode,
    super.exception,
  }) : super();
}

class LocationPermissionLimitedFailure extends LocationFailure {
  const LocationPermissionLimitedFailure({
    required super.message,
    super.statusCode,
    super.exception,
  }) : super();
}

class LocationPermissionProvisionalFailure extends LocationFailure {
  const LocationPermissionProvisionalFailure({
    required super.message,
    super.statusCode,
    super.exception,
  }) : super();
}

class LocationFailureWithMessage extends LocationFailure {
  const LocationFailureWithMessage({
    required super.message,
    super.statusCode,
    super.exception,
  }) : super();
}
