import 'package:dio/dio.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/config/env_config.dart';
import 'package:safari_yatri/common/logger.dart'; // Assuming dLog is defined here
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/navigation_service.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/services/cache_service.dart';

/// A class responsible for managing the Dio HTTP client and adding interceptors
/// for logging and token handling (authentication and refresh).
class DioClient {
  late final Dio _dio;
  final BaseOptions _baseOptions = BaseOptions(
    baseUrl: EnvConfig.instance.apiBaseUrl,
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    sendTimeout: const Duration(seconds: 30),
    headers: {'Content-Type': 'application/json', 'Accept': 'application/json'},
  );

  /// Constructs a [DioClient] instance and initializes the [Dio] client with
  /// base options and interceptors.
  DioClient() {
    _dio = Dio(_baseOptions);
    _addInterceptors();
  }

  /// Returns the underlying [Dio] client instance for making HTTP requests.
  Dio get dio => _dio;

  /// Adds interceptors to the [_dio] client. This includes a logging interceptor
  /// (if enabled in the environment configuration) and a custom interceptor
  /// for handling authentication tokens and refreshing them when necessary.
  void _addInterceptors() {
    // Add PrettyDioLogger for request and response logging during development.
    if (EnvConfig.instance.enableLogging) {
      _dio.interceptors.add(
        PrettyDioLogger(
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          compact: false,
        ),
      );
    }

    // Add a custom InterceptorsWrapper to handle errors (specifically 401 Unauthorized)
    // and to automatically attach the authentication token to outgoing requests.
    _dio.interceptors.add(
      InterceptorsWrapper(
        onError: _handleErrorInterceptor,
        onRequest: _handleRequestInterceptor,
      ),
    );
  }

  /// Handles Dio errors. Specifically looks for 401 Unauthorized errors, attempts
  /// to refresh the authentication token, and retries the original request if
  /// the refresh is successful. If the refresh fails or there's no refresh token,
  /// it clears the stored token data.
  Future<void> _handleErrorInterceptor(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    if (error.response?.statusCode == 401) {
      if (error.requestOptions.path == 'Auth/Login') {
        return handler.next(error);
      } else {
        dLog.i(
          'Received 401 Unauthorized (not login). Attempting to refresh token.',
        );
        final refreshToken = await CacheService.instance.getRefreshToken();

        if (refreshToken != null) {
          final refreshed = await _refreshToken();
          if (refreshed) {
            await _retryOriginalRequest(error, handler);
          } else {
            // _refreshToken failed, which likely means the refresh token is invalid.
            // Clear all token data and force login.
            await _clearTokenAndForceLogin();
            return handler.next(error);
          }
        } else {
          // No refresh token available, force login.
          dLog.w('No refresh token available. Forcing login.');
          await _clearTokenAndForceLogin();
          return handler.next(error);
        }
      }
    }
    return handler.next(error);
  }

  /// Clears all cached token data (access and refresh) and navigates the user
  /// back to the login screen. Note: Requires access to Navigator context.
  Future<void> _clearTokenAndForceLogin() async {
    dLog.w('Clearing all token data and forcing user to login.');
    // CustomToast.showError('Session Expired. Please login again.');
    // Assertion to highlight token clearing in development
    // assert(false, 'Session Expired. Clearing all token data.');
    await CacheService.instance.clearTokenData();
    // Navigate to the login screen using the NavigationService
    sl<NavigationService>().goNamed(AppRoutesName.signIn);
  }

  /// Retries the original request that resulted in a 401 error after successfully
  /// refreshing the authentication token.
  Future<void> _retryOriginalRequest(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    final requestOptions = error.requestOptions;
    final newToken = await CacheService.instance.getAuthToken();
    if (newToken != null) {
      final options = Options(
        method: requestOptions.method,
        headers: {
          ...requestOptions.headers,
          'Authorization': 'Bearer $newToken',
        },
      );
      try {
        dLog.i('Retrying original request with new token.');
        final response = await _dio.request(
          requestOptions.path,
          data: requestOptions.data,
          queryParameters: requestOptions.queryParameters,
          options: options,
        );
        dLog.i('Original request retried successfully.');
        return handler.resolve(response);
      } catch (e, stack) {
        dLog.e('Retry of original request failed: $e\n$stack');
        return handler.next(
          DioException(
            requestOptions: requestOptions,
            error: e,
            message: 'Failed to retry original request after token refresh.',
          ),
        );
      }
    } else {
      dLog.w('New token is null after refresh. Cannot retry.');
      return handler.next(error); // Propagate the original error
    }
  }

  /// Clears the authentication token and refresh token from the cache and shows
  /// a session expired message to the user.
  Future<void> _clearTokenAndNotify() async {
    dLog.w('Clearing token data due to authentication failure.');
    CustomToast.showError('Session Expired. Please login again.');
    // Assertion to highlight token clearing in development
    // assert(false, 'Session Expired. Clearing token data.');
    await CacheService.instance.clearTokenData();
    // Consider navigating the user back to the login screen here if appropriate
  }

  /// Intercepts outgoing requests to attach the authentication token from the
  /// cache to the 'Authorization' header. If the token is expired, it attempts
  /// to refresh it before attaching.
  Future<void> _handleRequestInterceptor(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      final token = await CacheService.instance.getAuthToken();
      final expiry = await CacheService.instance.getAuthTokenExpiry();

      if (token != null && expiry != null) {
        final now = DateTime.now().toUtc();
        if (now.isAfter(expiry)) {
          dLog.i('Access token expired. Attempting to refresh before request.');
          final success = await _refreshToken();
          if (success) {
            final newToken = await CacheService.instance.getAuthToken();
            if (newToken != null) {
              options.headers['Authorization'] = 'Bearer $newToken';
              dLog.i('Token refreshed and attached to the request.');
            } else {
              dLog.w(
                'New token is null after refresh during request. Request might fail.',
              );
              // Proceed with the request without the token? Or reject?
              // For now, proceeding without the token. The error handler will likely catch 401.
            }
          } else {
            // Token refresh failed during request. Clear token data.
            await _clearTokenAndNotify();
            // It might be appropriate to reject the request here to prevent API calls with an invalid token.
            // return handler.reject(DioException(requestOptions: options, message: 'Token refresh failed.'));
          }
        } else {
          // Token is still valid, attach it to the request.
          options.headers['Authorization'] = 'Bearer $token';
        }
      } else {
        // No token or expiry found, proceed without authorization.
        dLog.w(
          'No auth token or expiry found in cache. Proceeding without authorization.',
        );
      }
    } catch (e, stack) {
      dLog.e('Error during request interception (token handling): $e\n$stack');
      // If an error occurs during token retrieval or refresh, it's safer to proceed
      // without the token and let the API handle unauthorized requests.
      dLog.w(
        'Proceeding with the request without authorization due to an error.',
      );
      // Optionally, you could clear the token here if the error suggests an invalid state.
      // await CacheService.instance.clearTokenData();
    }
    return handler.next(options); // Continue the request.
  }

  /// Attempts to refresh the authentication token by calling the '/auth/refresh'
  /// API endpoint with the stored refresh token. If successful, it updates the
  /// access token, expiry, and refresh token in the cache.
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await CacheService.instance.getRefreshToken();
      if (refreshToken == null) {
        dLog.w('No refresh token found in cache. Cannot refresh.');
        return false; // Indicate refresh failed
      }

      dLog.i('Attempting to call /auth/refresh with refresh token.');
      final response = await _dio.post(
        '/auth/refresh',
        data: {'refresh_token': refreshToken},
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return _processRefreshTokenResponse(response.data);
      } else {
        dLog.w(
          'Refresh token API returned an error (${response.statusCode}). Refresh failed. Data: ${response.data}',
        );
        return false; // Indicate refresh failed due to API error (likely invalid refresh token)
      }
    } on DioException catch (e, stack) {
      dLog.e(
        'Token refresh failed due to DioException: ${e.message}\nStatus Code: ${e.response?.statusCode}\nResponse Data: ${e.response?.data}\n$stack',
      );
      return false; // Indicate refresh failed due to network or other Dio error
    } catch (e, stack) {
      dLog.e('Token refresh failed due to unexpected error: $e\n$stack');
      return false; // Indicate refresh failed due to an unexpected error
    }
  }

  /// Processes the successful refresh token API response by extracting the new
  /// access token, expiry time, and refresh token, and storing them in the cache.
  bool _processRefreshTokenResponse(dynamic data) {
    if (data == null) {
      dLog.e('Refresh token response data is null.');
      return false;
    }

    final newToken = data['access_token'] as String?;
    final expiresIn = data['expires_in'] as int?; // seconds
    final newRefreshToken = data['refresh_token'] as String?;

    if (newToken == null || expiresIn == null || newRefreshToken == null) {
      dLog.e(
        'Missing required fields in refresh token response: access_token=$newToken, expires_in=$expiresIn, refresh_token=$newRefreshToken',
      );
      return false;
    }

    final expiry = DateTime.now().toUtc().add(Duration(seconds: expiresIn));
    CacheService.instance.setAuthToken(
      newToken,
      expiry,
      refreshToken: newRefreshToken,
    );
    dLog.i('Token refresh successful. New token and expiry saved.');
    return true;
  }
}
