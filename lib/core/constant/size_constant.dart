const double kMapInitialZoom = 17;

///actually its won't control the bottom sheet height of passenge home
/// but it gives how much paddding in bottom want while showing
/// direction when user selectes destination meaning to give padding in bottom
/// of google map
const double kPassengerHomeBottomSheetHeight = 300.0;
const double kMapPadding = 60.0;

///

const int kInitialRiderSearchDiameterInMeter = 500;
const int kRiderSearchDiameterIncrement = 500;
