import 'package:google_maps_flutter/google_maps_flutter.dart';

class DirectionRouteModel {
  final List<LatLng> polylinePoints;
  final int totalDistanceInMeters;
  final int estimatedDurationInSeconds;
  final List<LatLng>? polylinePointsFromCurrentToStart; 

  const DirectionRouteModel({
    required this.polylinePoints,
    required this.totalDistanceInMeters,
    required this.estimatedDurationInSeconds,
    this.polylinePointsFromCurrentToStart,
  });
}
