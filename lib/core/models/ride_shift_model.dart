import 'package:safari_yatri/features/admin/core/models/fare_rate_model.dart';

class RideShiftModel {
  final String vehicleTypeName;
  final List<FareRateModel> fareRateItems;
  final int shiftId;
  final bool isActive;
  final String shiftName;
  final String startTime;
  final String endTime;
  final int vehicleTypeId;

  RideShiftModel({
    required this.vehicleTypeName,
    required this.fareRateItems,
    required this.shiftId,
    required this.isActive,
    required this.shiftName,
    required this.startTime,
    required this.endTime,
    required this.vehicleTypeId,
  });

  factory RideShiftModel.fromMap(Map<String, dynamic> map) {
    return RideShiftModel(
      vehicleTypeName: map['VehicleTypeName'] ?? '',
      fareRateItems:
          (map['FareRateItems'] as List)
              .map((item) => FareRateModel.fromMap(item))
              .toList(),
      shiftId: map['ShiftId'] ?? 0,
      isActive: map['IsActive'] ?? false,
      shiftName: map['ShiftName'] ?? '',
      startTime: map['StartTime'] ?? '',
      endTime: map['EndTime'] ?? '',
      vehicleTypeId: map['VehicleTypeId'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'ShiftName': shiftName,
      'StartTime': startTime,
      'EndTime': endTime,
      'VehicleTypeId': vehicleTypeId,
      'ShiftId': shiftId,
      'IsActive': isActive,
      'VehicleTypeName': vehicleTypeName,
      'FareRateItems': fareRateItems.map((item) => item.toMap()).toList(),
    };
  }

  RideShiftModel copyWith({
    String? vehicleTypeName,
    List<FareRateModel>? fareRateItems,
    int? shiftId,
    bool? isActive,
    String? shiftName,
    String? startTime,
    String? endTime,
    int? vehicleTypeId,
  }) {
    return RideShiftModel(
      vehicleTypeName: vehicleTypeName ?? this.vehicleTypeName,
      fareRateItems: fareRateItems ?? this.fareRateItems,
      shiftId: shiftId ?? this.shiftId,
      isActive: isActive ?? this.isActive,
      shiftName: shiftName ?? this.shiftName,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      vehicleTypeId: vehicleTypeId ?? this.vehicleTypeId,
    );
  }
}
