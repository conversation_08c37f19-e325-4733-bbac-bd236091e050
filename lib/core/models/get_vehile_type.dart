// ignore_for_file: public_member_api_docs, sort_constructors_first
class GetVehicleType {
  final String vehicleTypeName;
  final int maxPassengerCapacity;
  final double commissionRateInPercentage;
  final double negotiationBottomValue;
  final double negotiationStepValue;
  final int vehicleTypeId;
  final bool isActive;
  final int vehicleCount;
  final int maxSearchDistanceInMeter;

  const GetVehicleType({
    required this.vehicleTypeName,
    required this.maxPassengerCapacity,
    required this.commissionRateInPercentage,
    required this.negotiationBottomValue,
    required this.negotiationStepValue,
    required this.vehicleTypeId,
    required this.isActive,
    required this.vehicleCount,
    required this.maxSearchDistanceInMeter,
  });

  factory GetVehicleType.fromMap(Map<String, dynamic> map) {
    return GetVehicleType(
      vehicleTypeName: map['VehicleTypeName'] ?? '',
      maxPassengerCapacity: map['MaxPassengerCapacity'] ?? 0,
      commissionRateInPercentage:
          (map['CommissionRateInPercentage'] ?? 0).toDouble(),
      negotiationBottomValue: (map['NegotiationBottomValue'] ?? 0).toDouble(),
      negotiationStepValue: (map['NegotiationStepValue'] ?? 0).toDouble(),
      vehicleTypeId: map['VehicleTypeId'] ?? 0,
      isActive: map['IsActive'] ?? false,
      vehicleCount: map['VechileCount'] ?? 0,
      maxSearchDistanceInMeter: map['MaxRiderSearchDistanceInMeter'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'VehicleTypeName': vehicleTypeName,
      'MaxPassengerCapacity': maxPassengerCapacity,
      'CommissionRateInPercentage': commissionRateInPercentage,
      'NegotiationBottomValue': negotiationBottomValue,
      'NegotiationStepValue': negotiationStepValue,
      'VehicleTypeId': vehicleTypeId,
      'IsActive': isActive,
      'VechileCount': vehicleCount,
      'MaxRiderSearchDistanceInMeter': maxSearchDistanceInMeter,
    };
  }

  GetVehicleType copyWith({
    String? vehicleTypeName,
    int? maxPassengerCapacity,
    double? commissionRateInPercentage,
    double? negotiationBottomValue,
    double? negotiationStepValue,
    int? vehicleTypeId,
    bool? isActive,
    int? vehicleCount,
    int? maxSearchDistanceInMeter,
  }) {
    return GetVehicleType(
      maxSearchDistanceInMeter:
          maxSearchDistanceInMeter ?? this.maxSearchDistanceInMeter,
      vehicleTypeName: vehicleTypeName ?? this.vehicleTypeName,
      maxPassengerCapacity: maxPassengerCapacity ?? this.maxPassengerCapacity,
      commissionRateInPercentage:
          commissionRateInPercentage ?? this.commissionRateInPercentage,
      negotiationBottomValue:
          negotiationBottomValue ?? this.negotiationBottomValue,
      negotiationStepValue: negotiationStepValue ?? this.negotiationStepValue,
      vehicleTypeId: vehicleTypeId ?? this.vehicleTypeId,
      isActive: isActive ?? this.isActive,
      vehicleCount: vehicleCount ?? this.vehicleCount,
    );
  }
}
