class MapMarker {
  final double latitude;
  final double longitude;
  final String label; // e.g., "Source Address" or "Destination Address"

  MapMarker({
    required this.latitude,
    required this.longitude,
    required this.label,
  });

  @override
  String toString() {
    return 'MapMarker(label: $label, lat: $latitude, lng: $longitude)';
  }

  // Optional: For comparing markers to avoid duplicates if needed elsewhere
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MapMarker &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.label == label;
  }

  @override
  int get hashCode => latitude.hashCode ^ longitude.hashCode ^ label.hashCode;
}
