// import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:safari_yatri/core/theme/app_colors.dart';

// class AppButtonStyles {
//   // Light theme button text styles
//   static final ButtonStyle lightTextButton = TextButton.styleFrom(
//     foregroundColor: AppColors.lightPrimary,
//     textStyle: GoogleFonts.poppins(fontSize: 14, fontWeight: FontWeight.w500),
//   );

//   static final ButtonStyle lightElevatedButton = ElevatedButton.styleFrom(
//     backgroundColor: AppColors.lightPrimary,
//     foregroundColor: AppColors.lightOnPrimary,
//     textStyle: GoogleFonts.poppins(fontSize: 14, fontWeight: FontWeight.w600),
//     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//     padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
//   );

//   static final ButtonStyle lightOutlinedButton = OutlinedButton.styleFrom(
//     foregroundColor: AppColors.lightPrimary,
//     textStyle: GoogleFonts.poppins(fontSize: 14, fontWeight: FontWeight.w500),
//     side: BorderSide(color: AppColors.lightPrimary),
//     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//     padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
//   );

//   // Dark theme button text styles
//   static final ButtonStyle darkTextButton = TextButton.styleFrom(
//     foregroundColor: AppColors.darkPrimary,
//     textStyle: GoogleFonts.poppins(fontSize: 14, fontWeight: FontWeight.w500),
//   );

//   static final ButtonStyle darkElevatedButton = ElevatedButton.styleFrom(
//     backgroundColor: AppColors.darkPrimary,
//     foregroundColor: AppColors.darkOnPrimary,
//     textStyle: GoogleFonts.poppins(fontSize: 14, fontWeight: FontWeight.w600),
//     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//     padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
//   );

//   static final ButtonStyle darkOutlinedButton = OutlinedButton.styleFrom(
//     foregroundColor: AppColors.darkPrimary,
//     textStyle: GoogleFonts.poppins(fontSize: 14, fontWeight: FontWeight.w500),
//     side: BorderSide(color: AppColors.darkPrimary),
//     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//     padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
//   );
// }
