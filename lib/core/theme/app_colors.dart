import 'package:flutter/material.dart';

class AppColors {
  // Main Brand Green from logo (approx #009944)
  static const Color brandGreen = Color(0xFF82AF5c);

  // Light Theme Colors
  static const Color lightPrimary = brandGreen;
  static const Color lightOnPrimary = Colors.white;

  static const Color lightSecondary = Color(0xFF4CAF50); // Soft green
  static const Color lightOnSecondary = Colors.white;

  static const Color lightBackground = Color(
    0xFFF5FDF6,
  ); // Light green-tinted background
  static const Color lightOnBackground = Colors.black87;

  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color lightOnSurface = Colors.black87;

  static const Color lightError = Color(0xFFD32F2F);
  static const Color lightOnError = Colors.white;

  // Dark Theme Colors - Adjusted for better contrast and vibrancy
  static const Color darkPrimary = Color(
    0xFF66BB6A,
  ); // Lighter green in dark mode
  static const Color darkOnPrimary =
      Colors.black; // Good contrast with darkPrimary

  static const Color darkSecondary = Color(0xFF81C784); // Soft secondary green
  static const Color darkOnSecondary =
      Colors.black; // Good contrast with darkSecondary

  // Slightly lighter dark background for better depth and less stark black
  static const Color darkBackground = Color(0xFF1A1A1A);
  // Full white for text on background to maximize contrast and readability
  static const Color darkOnBackground = Colors.white;

  // Slightly lighter dark surface for better visual separation from background
  static const Color darkSurface = Color(0xFF2C2C2C);
  // Full white for text on surface to maximize contrast and readability
  static const Color darkOnSurface = Colors.white;

  static const Color darkError = Color(0xFFCF6679);
  static const Color darkOnError = Colors.black;

  // Border Colors
  static const Color borderColorLight = Color(0xFFE0E0E0);
  static const Color borderColor = Color(0xFFBDBDBD);
  static const Color borderColorDark = Color(0xFF757575);
  static const Color borderColorPrimary = brandGreen;

  // Map marker colors
  static const Color pickUpLocationColor = brandGreen;
  static const Color destinationLocationColor = Color(0xFFE53935); 
  
  
  // Complementary colors for better design
  static const Color brandGreenDark = Color(0xFF6B9147);
  static const Color brandGreenLight = Color(0xFF9BC470);
  
  // Accent colors
  static const Color accentOrange = Color(0xFFFF7043);
  static const Color accentBlue = Color(0xFF2196F3);
  
  // Neutral colors
  static const Color darkGrey = Color(0xFF2C2C2C);
  static const Color lightGrey = Color(0xFFF5F5F5);// Deep red
}
