import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:safari_yatri/core/theme/app_colors.dart';
import 'package:safari_yatri/core/theme/app_text_theme.dart';

class AppTheme {
  static const String fontFamily = "Poppins";

  // Light Text Theme
  static TextTheme lightTextTheme = GoogleFonts.poppinsTextTheme(
    AppTextThemes.lightTextTheme,
  );

  // Dark Text Theme
  static TextTheme darkTextTheme = GoogleFonts.poppinsTextTheme(
    AppTextThemes.darkTextTheme,
  );

  static const Color onPrimaryLightMode = Color(0xFFF8FAF5);
  static const Color onPrimaryDarkMode = Color(0xFF1B1B1B);

  // Light Theme Configuration
  static ThemeData get lightTheme {
    return FlexThemeData.light(
      onPrimary: onPrimaryLightMode,
      textTheme: lightTextTheme,
      fontFamily: fontFamily,
      scheme: FlexScheme.custom,
      colors: const FlexSchemeColor(
        primary: Color(0xFF82AF5C),
        primaryContainer: Color(0xFFE8F5E8),
        secondary: Color(0xFF4CAF50),
        secondaryContainer: Color(0xFFE8F5E8),
        tertiary: Color(0xFF66BB6A),
        tertiaryContainer: Color(0xFFE8F5E8),
        appBarColor: Color(0xFFFAFCF8), // Light surface-like color
        error: Color(0xFFD32F2F),
        errorContainer: Color(0xFFFFEBEE),
      ),
      surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
      blendLevel: 7,
      appBarStyle: FlexAppBarStyle.surface, // Changed from primary to surface
      appBarOpacity: 1.0,
      transparentStatusBar: true,
      appBarElevation: 0,
      bottomAppBarElevation: 0,
      tabBarStyle: FlexTabBarStyle.forAppBar,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      typography: Typography.material2021(),
      visualDensity: FlexColorScheme.comfortablePlatformDensity,
      subThemesData: _getSubThemesData(isLight: true),
    );
  }

  static ThemeData get darkTheme {
    return FlexThemeData.dark(
      onPrimary: onPrimaryDarkMode,
      textTheme: darkTextTheme,
      fontFamily: fontFamily,
      scheme: FlexScheme.custom,
      colors: const FlexSchemeColor(
        primary: AppColors.brandGreenLight,
        primaryContainer: AppColors.brandGreenDark,
        secondary: AppColors.accentOrange,
        secondaryContainer: Color(0xFF8D4E2A),
        tertiary: AppColors.accentBlue,
        tertiaryContainer: Color(0xFF1565C0),
        appBarColor: Color(
          0xFF1E1E1E,
        ), // Lighter than pure black but still dark
        error: Color(0xFFCF6679),
      ),
      surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
      blendLevel: 8, // Reduced from 13 to make it less dark
      subThemesData: _getSubThemesData(isLight: false),
      keyColors: const FlexKeyColors(
        useSecondary: true,
        useTertiary: true,
        keepPrimary: true,
      ),
      visualDensity: FlexColorScheme.comfortablePlatformDensity,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
    );
  }

  // Improved sub-themes configuration for better color harmony
  static FlexSubThemesData _getSubThemesData({required bool isLight}) {
    return FlexSubThemesData(
      // Unified surface approach - everything uses surface variants
      scaffoldBackgroundBaseColor: FlexScaffoldBaseColor.surface,
      scaffoldBackgroundSchemeColor:
          isLight
              ? SchemeColor.surface
              : SchemeColor.surface, // Lighter than surfaceContainerLowest
      // AppBar - matches the surface for unified look
      appBarBackgroundSchemeColor:
          isLight ? SchemeColor.surface : SchemeColor.surface,
      appBarForegroundSchemeColor: SchemeColor.onSurface,
      appBarScrolledUnderElevation: isLight ? 1 : 2,
      appBarCenterTitle: true,

      // Bottom Navigation - uses surfaceContainer for slight differentiation
      bottomNavigationBarSelectedLabelSchemeColor: SchemeColor.primary,
      bottomNavigationBarUnselectedLabelSchemeColor:
          SchemeColor.onSurfaceVariant,
      bottomNavigationBarSelectedIconSchemeColor: SchemeColor.primary,
      bottomNavigationBarUnselectedIconSchemeColor:
          SchemeColor.onSurfaceVariant,
      bottomNavigationBarBackgroundSchemeColor: SchemeColor.surfaceContainer,
      bottomNavigationBarElevation: isLight ? 0 : 2,
      bottomNavigationBarShowSelectedLabels: true,
      bottomNavigationBarShowUnselectedLabels: true,

      // Navigation Rail
      navigationRailSelectedLabelSchemeColor: SchemeColor.primary,
      navigationRailUnselectedLabelSchemeColor: SchemeColor.onSurfaceVariant,
      navigationRailSelectedIconSchemeColor: SchemeColor.primary,
      navigationRailUnselectedIconSchemeColor: SchemeColor.onSurfaceVariant,
      navigationRailBackgroundSchemeColor: SchemeColor.surface,
      navigationRailElevation: 0,

      // Navigation Drawer
      drawerBackgroundSchemeColor: SchemeColor.surface,
      drawerElevation: isLight ? 1 : 2,

      // Card theme - uses surfaceContainer for layering
      cardRadius: 12,
      cardElevation: isLight ? 1 : 3,

      // Buttons
      elevatedButtonSchemeColor: SchemeColor.primary,
      elevatedButtonSecondarySchemeColor: SchemeColor.onPrimary,
      elevatedButtonRadius: 12,
      elevatedButtonElevation: isLight ? 2 : 4,

      outlinedButtonOutlineSchemeColor: SchemeColor.primary,
      outlinedButtonRadius: 12,

      textButtonSchemeColor: SchemeColor.primary,
      textButtonRadius: 12,

      // FAB
      fabSchemeColor: SchemeColor.primary,
      fabRadius: 16,

      // Chip
      chipSchemeColor: SchemeColor.primary,
      chipRadius: 8,

      // Input fields
      inputDecoratorSchemeColor: SchemeColor.primary,
      inputDecoratorBorderSchemeColor: SchemeColor.outline,
      inputDecoratorRadius: 12,
      inputDecoratorFocusedBorderWidth: 2,
      inputDecoratorBorderType: FlexInputBorderType.outline,
      inputDecoratorFocusedHasBorder: true,
      inputDecoratorUnfocusedHasBorder: true,

      // Controls
      switchSchemeColor: SchemeColor.primary,
      checkboxSchemeColor: SchemeColor.primary,
      radioSchemeColor: SchemeColor.primary,

      // Slider
      sliderBaseSchemeColor: SchemeColor.primary,
      sliderValueTinted: true,

      // Dialogs and sheets - use surfaceContainerHigh for elevation
      dialogBackgroundSchemeColor: SchemeColor.surfaceContainerHigh,
      dialogElevation: isLight ? 6 : 8,
      dialogRadius: 16,

      bottomSheetBackgroundColor: SchemeColor.surfaceContainerHigh,
      bottomSheetElevation: isLight ? 4 : 6,
      bottomSheetRadius: 16,

      // Snackbar
      snackBarBackgroundSchemeColor: SchemeColor.inverseSurface,
      snackBarActionSchemeColor: SchemeColor.inversePrimary,
      snackBarElevation: 4,
      snackBarRadius: 8,

      // TabBar
      tabBarItemSchemeColor: SchemeColor.primary,
      tabBarUnselectedItemSchemeColor: SchemeColor.onSurfaceVariant,
      tabBarIndicatorSchemeColor: SchemeColor.primary,
      tabBarIndicatorSize: TabBarIndicatorSize.tab,
      tabBarIndicatorWeight: 3,

      // List Tiles
      listTileSelectedSchemeColor: SchemeColor.primary,
      listTileIconSchemeColor: SchemeColor.onSurfaceVariant,
      listTileTextSchemeColor: SchemeColor.onSurface,
      listTileSelectedTileSchemeColor: SchemeColor.primaryContainer,

      // Popup Menu
      popupMenuElevation: isLight ? 8 : 12,
      popupMenuRadius: 12,

      // Tooltip
      tooltipSchemeColor: SchemeColor.inverseSurface,
      tooltipRadius: 8,

      // Search Bar
      searchBarBackgroundSchemeColor: SchemeColor.surfaceContainer,
      searchBarElevation: isLight ? 1 : 2,
      searchBarRadius: 28,

      // Segmented Button
      segmentedButtonSchemeColor: SchemeColor.primary,
      segmentedButtonBorderSchemeColor: SchemeColor.outline,
      segmentedButtonRadius: 20,

      // Pickers
      timePickerDialogRadius: 16,
      datePickerDialogRadius: 16,

      // Modern navigation bar (if using NavigationBar instead of BottomNavigationBar)
      navigationBarSelectedLabelSchemeColor: SchemeColor.onSurface,
      navigationBarUnselectedLabelSchemeColor: SchemeColor.onSurfaceVariant,
      navigationBarSelectedIconSchemeColor: SchemeColor.onSecondaryContainer,
      navigationBarUnselectedIconSchemeColor: SchemeColor.onSurfaceVariant,
      navigationBarIndicatorSchemeColor: SchemeColor.secondaryContainer,
      navigationBarBackgroundSchemeColor: SchemeColor.surface,
      navigationBarElevation: 0.0,
      navigationBarHeight: 80.0,

      // Additional configurations
      tintedDisabledControls: true,
      adaptiveRemoveElevationTint: FlexAdaptive.all(),
      adaptiveElevationShadowsBack: FlexAdaptive.all(),
      adaptiveAppBarScrollUnderOff: FlexAdaptive.all(),
      adaptiveRadius: FlexAdaptive.all(),
      adaptiveSplash: FlexAdaptive.all(),
      splashType: FlexSplashType.defaultSplash,
      defaultRadius: 12,
      defaultRadiusAdaptive: 12,
      interactionEffects: true,

      // Better blend settings for dark theme
      blendOnLevel: isLight ? 10 : 15,
      blendOnColors: false,
      useM2StyleDividerInM3: true,
      alignedDropdown: true,
      useInputDecoratorThemeInDialogs: true,
    );
  }

  // System UI Overlay Style for Light Theme
  static SystemUiOverlayStyle get lightSystemUiOverlayStyle {
    return const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
      systemNavigationBarColor: Color(0xFFFAFCF8), // Matches surface
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
      systemStatusBarContrastEnforced: false,
      systemNavigationBarContrastEnforced: false,
    );
  }

  // Improved Dark System UI Overlay Style
  static SystemUiOverlayStyle get darkSystemUiOverlayStyle {
    return const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
      systemNavigationBarColor: Color(0xFF1E1E1E), // Lighter than pure black
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.light,
      systemStatusBarContrastEnforced: false,
      systemNavigationBarContrastEnforced: false,
    );
  }

  static SystemUiOverlayStyle getSystemUiOverlayStyleFromMode(ThemeMode mode) {
    return switch (mode) {
      ThemeMode.dark => darkSystemUiOverlayStyle,
      ThemeMode.light => lightSystemUiOverlayStyle,
      ThemeMode.system =>
        WidgetsBinding.instance.platformDispatcher.platformBrightness ==
                Brightness.dark
            ? darkSystemUiOverlayStyle
            : lightSystemUiOverlayStyle,
    };
  }

  // Extension colors remain the same
  static const Map<String, Color> lightExtensionColors = {
    'activeRide': Color(0xFF4CAF50),
    'waiting': Color(0xFFFF9800),
    'completed': Color(0xFF2196F3),
    'cancelled': Color(0xFFE53935),
    'pickup': Color(0xFF82AF5C),
    'destination': Color(0xFFE53935),
    'onActiveRide': Color(0xFFFFFFFF),
    'onWaiting': Color(0xFFFFFFFF),
    'onCompleted': Color(0xFFFFFFFF),
    'onCancelled': Color(0xFFFFFFFF),
    'onPickup': Color(0xFFFFFFFF),
    'onDestination': Color(0xFFFFFFFF),
  };

  static const Map<String, Color> darkExtensionColors = {
    'activeRide': Color(0xFF66BB6A),
    'waiting': Color(0xFFFFB74D),
    'completed': Color(0xFF64B5F6),
    'cancelled': Color(0xFFEF5350),
    'pickup': Color(0xFF9BCC75),
    'destination': Color(0xFFEF5350),
    'onActiveRide': Color(0xFF000000),
    'onWaiting': Color(0xFF000000),
    'onCompleted': Color(0xFF000000),
    'onCancelled': Color(0xFFFFFFFF),
    'onPickup': Color(0xFF000000),
    'onDestination': Color(0xFFFFFFFF),
  };
}

// Extension remains the same
extension CustomColors on ColorScheme {
  Color get activeRide =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['activeRide']!
          : AppTheme.darkExtensionColors['activeRide']!;

  Color get waiting =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['waiting']!
          : AppTheme.darkExtensionColors['waiting']!;

  Color get completed =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['completed']!
          : AppTheme.darkExtensionColors['completed']!;

  Color get cancelled =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['cancelled']!
          : AppTheme.darkExtensionColors['cancelled']!;

  Color get pickup =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['pickup']!
          : AppTheme.darkExtensionColors['pickup']!;

  Color get destination =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['destination']!
          : AppTheme.darkExtensionColors['destination']!;

  Color get onActiveRide =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['onActiveRide']!
          : AppTheme.darkExtensionColors['onActiveRide']!;

  Color get onWaiting =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['onWaiting']!
          : AppTheme.darkExtensionColors['onWaiting']!;

  Color get onCompleted =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['onCompleted']!
          : AppTheme.darkExtensionColors['onCompleted']!;

  Color get onCancelled =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['onCancelled']!
          : AppTheme.darkExtensionColors['onCancelled']!;

  Color get onPickup =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['onPickup']!
          : AppTheme.darkExtensionColors['onPickup']!;

  Color get onDestination =>
      brightness == Brightness.light
          ? AppTheme.lightExtensionColors['onDestination']!
          : AppTheme.darkExtensionColors['onDestination']!;
}
