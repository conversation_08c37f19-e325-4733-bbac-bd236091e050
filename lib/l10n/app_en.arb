{"@@locale": "en", "appname": "Safari Yatri", "welcomeback": "Welcome Back", "signintocontinue": "Sign in to continue", "email": "Email", "password": "Password", "signin": "Sign In", "signup": "Sign Up", "donthaveaccount": "Don't have an account? Sign Up", "selectedLanguage": "Select Language", "chooseLater": "You can change language later from settings", "next": "Next", "skip": "<PERSON><PERSON>", "done": "Done", "loginScreenRememberMeLabel": "Remember Me", "onBoardingfirstScreenTitle": "One app for all services.", "onBoardingSecondScreenTitle": "Get there, on time", "onBoardingThirdScreenTitle": "Pay, as you want.", "onBoardingfirstScreenDesc": "Get a ride with your finertips.", "onBoardingSecondScreenDesc": "Best the traffic and reach your destination fast, every time.", "onBoardingThirdScreenDesc": "Cash? Card? Wallet? We accept it all. Let's get started.", "welcomeText": "Welcome", "welcomeDescription": "Have a better sharing exprience", "locationPermissionTitle": "Enable Location Permission", "locationPermissionDescription": "To get great service you need to provide location permission. You Can always change permission from settings.", "enableLocation": "Enable Location", "locationEnableAccess": "Enable Location Access", "locationRequestingPermission": "Requesting Permission...", "locationAccessGranted": "Location Access Granted", "locationNeeded": "Location Needed", "locationDescription": "This app needs your location to connect riders with drivers and help passengers track trips in real time.", "locationWaitingConfirmation": "Waiting for your confirmation in the permission dialog.", "locationRedirecting": "Great! We've got your location. Redirecting you now…", "locationDenied": "You denied location access. Tap below to try again.", "locationPermanentlyDenied": "Location permanently denied. Please open settings to allow access.", "locationRequired": "Location permission is required to continue.", "locationEnableButton": "Enable Location", "locationOpenSettings": "Open App Settings", "locationTryAgain": "Try Again", "locationGoToSettings": "Go to Settings", "locationAllowAccess": "Allow Location Access", "locationPermissionHeader": "Location Permission Required", "locationServiceDisabled": "Location Services Disabled", "locationServiceDisabledMessage": "Location services are turned off on your device. Please enable them to continue.", "openLocationSettings": "Open Location Settings", "showCaseDrawerDescription": "View your profile and settings", "showCasePassengerCoutDescription": "Select the number of passengers", "showCasePickUpLocationDescription": "Select your pickup location", "showCaseDestinationLocationDescription": "Select your destination location", "showCaseFindRiderDescription": "Find available riders", "numberOfPassenger": "Select the number of passengers for your trip", "buttonTitle": "Find Rider", "passengerHomeBottomSheetDestination": "Your Destinations", "passengerHomeDirectionErrorText": "Direction Route is null", "loginScreenAppHeaderTitle": "LogIn your Account", "loginScreenAppHeaderSubTitle": "Please enter your details to continue", "loginScreenPhoneNumberFormLabel": "Phone Number", "loginScreenPasswordFormLabel": "Password", "loginScreenForgetPasswordBtnTextLabel": "Forget Password?", "loginScreenSucessToastLabel": "<PERSON><PERSON>l", "loginScreenErrorToastLoginNotActiveLabel": "Please verify your number.<PERSON>gin not active yet.", "loginScreenButtonTitle": "<PERSON><PERSON>", "loginScreenDontHaveAnAccount": "Don't have an account?", "loginScreenSignUpButtonTitle": "Sign Up", "footerWidgetTextSpanByContinue": "By continuing, you agree to our ", "footerWidgetTextSpanTermsOfServices": "Terms of Service", "footerWidgetTextSpanAnd": " and ", "footerWidgetTextSpanPrivacyPolicy": "Privacy Policy", "signUpScreenAppHeaderTitle": "Create your Account", "signUpScreenAppHeaderSubTitle": "Please enter your details to sign up", "signUpScreenFullNameLabel": "Full Name", "signUpScreenPhoneNumberLabel": "Phone Number", "signUpScreenEmailAddressLabel": "Email Address", "signUpScreenCurrentAddressLabel": "Current Address", "signUpScreenGenderLabel": "Gender", "signUpScreenSignUpButtonTitle": "Sign Up", "signUpScreenAlreadyHaveAccountText": "Already have an account?", "signUpScreenSignInText": "Sign In", "verifyOtpScreenAppHeaderTitle": "Verify OTP", "verifyOtpScreenAppHeaderSubTitle": "Please enter the 4-digit OTP sent to your number.", "verifyOtpScreenResendButtonText": "Resend OTP", "verifyOtpScreenResendCountdownText": "Resend available in {_secondsRemaining} seconds", "verifyOtpScreenResendSuccessToast": "OTP resent successfully", "verifyOtpScreenResendFailedToast": "Failed to resend OTP", "verifyOtpScreenOtpVerifySuccessToast": "OTP verification successful", "verifyOtpScreenOtpVerifyFailedToast": "Incorrect OTP. Please try again.", "verifyOtpScreenPleaseLoginToast": "Please, login with your credentials.", "driverDrawerScreenHomeTitle": "Home", "driverDrawerScreenMyTripsTitle": "My Trips", "driverDrawerScreenProfileTitle": "Profile", "driverDrawerScreenSettingsTitle": "Settings", "driverDrawerScreenEmergencySafetyTitle": "Emergency & Safety", "driverDrawerScreenPassengerModeButton": "Passenger Mode", "emergencyScreenAppBarTitle": "Emergency & Safety", "emergencyScreenSupportButton": "Support", "emergencyScreenEmergencyContactsButton": "Emergency contacts", "emergencyScreenCall100Button": "Call 100", "emergencyScreenHowYoureProtectedTitle": "How you're protected", "emergencyScreenFeatureProactiveSafetySupport": "Proactive safety support", "emergencyScreenFeatureDriversVerification": "Drivers verification", "emergencyScreenFeatureProtectingPrivacy": "Protecting your privacy", "emergencyScreenFeatureStayingSafe": "Staying safe on every ride", "emergencyScreenFeatureAccidentsSteps": "Accidents: Steps to take", "notificationScreenAppBarTitle": "Notification Preferences", "notificationScreenRideRequestsTitle": "Ride Requests", "notificationScreenRideRequestsSubtitle": "Get notified about new ride requests", "notificationScreenPromotionsTitle": "Promotions", "notificationScreenPromotionsSubtitle": "Receive promotional offers and bonuses", "notificationScreenEarningsTitle": "Earnings", "notificationScreenEarningsSubtitle": "Daily and weekly earnings summaries", "notificationScreenSafetyTitle": "Safety", "notificationScreenSafetySubtitle": "Safety alerts and emergency notifications", "passengerDrawerScreenHomeTitle": "Home", "passengerDrawerScreenYourTripsTitle": "Your Trips", "passengerDrawerScreenProfileTitle": "Profile", "passengerDrawerScreenSettingsTitle": "Settings", "passengerDrawerScreenEmergencySafetyTitle": "Emergency & Safety", "passengerDrawerScreenDriverModeButton": "Driver Mode", "settingScreenAppBarTitle": "Settings", "settingScreenLanguageTitle": "Language", "settingScreenLanguageSubtitle": "English", "settingScreenChangePasswordTitle": "Change Password", "settingScreenNotificationTitle": "Notification", "settingScreenRulesAndTermsTitle": "Rules and terms", "settingScreenLogoutTitle": "Logout", "settingScreenDeleteAccountTitle": "Delete Account", "settingScreenDeleteDialogTitle": "Delete Account", "settingScreenDeleteDialogMessage": "This action will permanently delete your account and all data associated with it. Are you sure you want to proceed?", "settingScreenDeleteDialogConfirmText": "Delete Account", "settingScreenDeleteDialogCancelText": "Cancel", "appVersionWidgetLoading": "Loading version...", "appVersionWidgetUnavailable": "Version unavailable", "appVersionWidgetFormat": "Version {version}+{buildNumber}", "locationPermissionScreenTitle": "Enable Location Permission", "locationPermissionScreenDescription": "To get great service you need to provide location permission. You can always change permission from settings.", "locationPermissionScreenGivePermissionButton": "Give Permissions", "locationPermissionScreenDialogMessage": "We require your precise location to seamlessly connect you to nearby services providers.\n\nPlease turn on device location.", "locationPermissionScreenDialogGivePermission": "Give Location Permission", "locationPermissionScreenDialogOpenSettings": "Open Settings", "fareOfferScreenTitle": "Offer your fare", "fareOfferScreenSuggestedFare": "Suggested Fare:", "fareOfferScreenDistance": "Total distance:", "fareOfferScreenScheduleTime": "Schedule Time", "fareOfferScreenDone": "Done", "fareOfferScreenEnterFareError": "Please enter a fare", "fareOfferScreenInvalidFareError": "Please enter a valid number", "fareOfferScreenNegativeFareError": "Fare cannot be negative", "fareOfferScreenMinimumFareError": "Minimum Fare rate is {minFare}", "scheduleTripTitle": "Schedule your trip", "schedulePickupDate": "Pickup Date", "schedulePickupTime": "Pickup Time", "selectPickupDate": "Select Pickup Date", "selectPickupTime": "Select Pickup Time", "confirm": "Confirm", "continueBtn": "Continue", "rideRequest_raiseFare": "<PERSON><PERSON>", "rideRequest_raisedFareToast": "You raised the fare to NPR {fare}", "rideRequest_payment": "Payment", "rideRequest_yourRide": "Your Current Ride", "rideRequest_pickup": "Pickup", "rideRequest_destination": "Destination", "rideRequest_cancelRequest": "Cancel Request", "rideRequest_cancelSuccess": "Request Cancel successful!!", "rideRequest_searchingMessages": "Searching for the best nearby driver...", "rideRequest_searchingMessages1": "Hang tight! We're finding your ride.", "rideRequest_searchingMessages2": "Almost there... getting a driver for you.", "rideRequest_searchingMessages3": "Your comfort ride is just a tap away 🚗💨", "rideRequest_searchingMessages4": "Making sure the driver gets your location📍"}