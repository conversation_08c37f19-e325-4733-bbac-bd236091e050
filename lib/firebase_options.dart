// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for ios - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBLyXqoEU8NkO-bgrpuO3ePlDP04Tom6BE',
    appId: '1:312690595315:web:04bd027a0ef3344af46e86',
    messagingSenderId: '312690595315',
    projectId: 'safari-yatri-f9f6f',
    authDomain: 'safari-yatri-f9f6f.firebaseapp.com',
    storageBucket: 'safari-yatri-f9f6f.firebasestorage.app',
    measurementId: 'G-XC7XKJGGGL',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDg-ucgq_JX7fxR8LwYEw6nOhxJjcp2xKg',
    appId: '1:312690595315:android:41dc9427c97b72acf46e86',
    messagingSenderId: '312690595315',
    projectId: 'safari-yatri-f9f6f',
    storageBucket: 'safari-yatri-f9f6f.firebasestorage.app',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBLyXqoEU8NkO-bgrpuO3ePlDP04Tom6BE',
    appId: '1:312690595315:web:8cd31549c43f749af46e86',
    messagingSenderId: '312690595315',
    projectId: 'safari-yatri-f9f6f',
    authDomain: 'safari-yatri-f9f6f.firebaseapp.com',
    storageBucket: 'safari-yatri-f9f6f.firebasestorage.app',
    measurementId: 'G-NPT3PR68GP',
  );
}
