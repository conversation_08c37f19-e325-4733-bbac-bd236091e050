// import 'package:flutter/material.dart';
// import 'package:go_router/go_router.dart';
// import 'package:safari_yatri/common/widgets/location_permission_widget.dart';
// import 'package:safari_yatri/core/di/dependency_injection.dart';
// import 'package:safari_yatri/core/widget/custom_button.dart';
// import 'package:safari_yatri/features/location/blocs/location/location_bloc.dart';
// import 'package:safari_yatri/features/location/blocs/location_permission/location_permission_bloc.dart';
// import '../../../../core/widget/custom_drawer_btn.dart';
// import '../../../drawer/pages/driver_drawer_page.dart';
// import '../../../passenger/location/widgets/passenger_map.dart';
// import '../widgets/passenger_request_dilog.dart';
// import '../widgets/status_card.dart';
// import 'ride_navigation_dilog_page.dart';

// class DriverHomePage extends StatefulWidget {
//   const DriverHomePage({super.key});
//   @override
//   State<DriverHomePage> createState() => _DriverHomePageState();
// }

// class _DriverHomePageState extends State<DriverHomePage> {
//   bool _isOnline = true;
//   bool _showRideNavigation = false;

//   @override
//   void didChangeDependencies() {
//     super.didChangeDependencies();
//     sl<LocationPermissionBloc>().add(
//       const LocationPermissionEvent.checkPermissionStatus(),
//     );
//     sl<LocationBloc>().add(const LocationEvent.startTracking());
//   }

//   @override
//   Widget build(BuildContext context) {
//     return LocationPermissionListenerWidget(
//       child: Scaffold(
//         drawer: buildDriverAppDrawer(context),
//         body: SafeArea(
//           child: Stack(
//             children: [
//               PassengerMap(),
//               buildDrawerButton(context),
//               CustomButtonPrimary(
//                 title: 'tap',
//                 onPressed: () {
//                   showDialog(
//                     context: context,
//                     barrierDismissible: false,
//                     builder:
//                         (context) => RideRequestDialog(
//                           onAccept: () {
//                             Navigator.pop(context);

//                             setState(() {
//                               _showRideNavigation = true;
//                             });
//                           },
//                           onDecline: () {
//                             Navigator.pop(context);
//                             // Handle decline logic
//                           },
//                         ),
//                   );
//                 },
//               ),
//               Positioned(
//                 bottom: 0,
//                 left: 0,
//                 right: 0,
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Visibility(
//                       visible: _showRideNavigation,
//                       child: RideNavigationCard(),
//                     ),
//                     Visibility(
//                       visible: !_showRideNavigation,
//                       child: OnlineStatusCard(
//                         isOnline: _isOnline,
//                         onToggle: (value) async {
//                           if (!value) {
//                             final confirm = await showModalBottomSheet<bool>(
//                               context: context,
//                               shape: const RoundedRectangleBorder(
//                                 borderRadius: BorderRadius.vertical(
//                                   top: Radius.circular(20),
//                                 ),
//                               ),
//                               builder:
//                                   (context) => Padding(
//                                     padding: const EdgeInsets.all(16.0),
//                                     child: Column(
//                                       mainAxisSize: MainAxisSize.min,
//                                       children: [
//                                         const Text(
//                                           'Go Offline?',
//                                           style: TextStyle(
//                                             fontSize: 18,
//                                             fontWeight: FontWeight.bold,
//                                           ),
//                                         ),
//                                         const SizedBox(height: 10),
//                                         const Text(
//                                           'Are you sure you want to go offline?',
//                                         ),
//                                         const SizedBox(height: 20),
//                                         Row(
//                                           mainAxisAlignment:
//                                               MainAxisAlignment.end,
//                                           children: [
//                                             TextButton(
//                                               onPressed:
//                                                   () => Navigator.of(
//                                                     context,
//                                                   ).pop(false),
//                                               child: const Text('Cancel'),
//                                             ),
//                                             CustomButtonPrimary(
//                                               width: 120,
//                                               height: 40,
//                                               title: 'Go Offline',
//                                               onPressed:
//                                                   () => context.pop(true),
//                                             ),
//                                           ],
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                             );

//                             if (confirm == true) {
//                               setState(() => _isOnline = false);
//                             }
//                           } else {
//                             setState(() => _isOnline = true);
//                           }
//                         },
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
