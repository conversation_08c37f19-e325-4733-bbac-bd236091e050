    // DropdownButtonFormField<String>(
    //                 value: _selectedGender,
    //                 decoration: InputDecoration(
    //                   labelText: 'Gender',
    //                   border: OutlineInputBorder(
    //                     borderRadius: BorderRadius.circular(AppStyles.space12),
    //                   ),
    //                   contentPadding: EdgeInsets.symmetric(
    //                     vertical: AppStyles.space12,
    //                     horizontal: AppStyles.space16,
    //                   ),
    //                 ),
    //                 hint: const Text('Select your gender'),
    //                 items:
    //                     _genderOptions.map<DropdownMenuItem<String>>((
    //                       String value,
    //                     ) {
    //                       return DropdownMenuItem<String>(
    //                         value: value,
    //                         child: Text(value),
    //                       );
    //                     }).toList(),
    //                 onChanged: (String? newValue) {
    //                   setState(() {
    //                     _selectedGender = newValue;
    //                   });
    //                 },
    //                 validator: (value) {
    //                   if (value == null || value.isEmpty) {
    //                     return 'Please select your gender';
    //                   }
    //                   return null;
    //                 },
    //               ),
             