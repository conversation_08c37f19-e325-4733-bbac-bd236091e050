// import 'package:flutter/material.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:safari_yatri/core/theme/app_colors.dart';
// import 'package:safari_yatri/core/theme/app_styles.dart';
// import 'package:safari_yatri/core/widget/custom_button.dart';
// import 'package:safari_yatri/core/widget/custom_form_field.dart';
// import 'package:shadcn_ui/shadcn_ui.dart';

// class DriverHomePage extends StatefulWidget {
//   const DriverHomePage({super.key});
//   @override
//   State<DriverHomePage> createState() => _DriverHomePageState();
// }

// class _DriverHomePageState extends State<DriverHomePage> {
//   GoogleMapController? _mapController;
//   final CameraPosition _initialCameraPosition = const CameraPosition(
//     target: LatLng(27.7172, 85.3240),
//     zoom: 14.0,
//   );

//   final Set<Marker> _markers = {};

//   @override
//   void initState() {
//     super.initState();
//   }

//   @override
//   void dispose() {
//     _mapController?.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: SafeArea(
//         child: Stack(
//           children: [
//             GoogleMap(
//               initialCameraPosition: _initialCameraPosition,
//               buildingsEnabled: false,
//             ),
//             Container(
//               height: 45,
//               width: 45,
//               margin: EdgeInsets.all(AppStyles.space12),
//               decoration: BoxDecoration(
//                 shape: BoxShape.circle,
//                 color: AppColors.lightPrimary,
//               ),
//               child: IconButton(
//                 icon: Icon(LucideIcons.menu, color: AppColors.darkOnBackground),
//                 onPressed: () {},
//               ),
//             ),
//             Positioned(
//               bottom: 0,
//               left: 0,
//               right: 0,
//               child: Container(
//                 padding: EdgeInsets.all(AppStyles.space12),
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: AppStyles.radiusLg,
//                   boxShadow: [
//                     BoxShadow(
//                       color: Colors.black26,
//                       blurRadius: 10,
//                       offset: Offset(0, -2),
//                     ),
//                   ],
//                 ),
//                 child: Column(
//                   spacing: AppStyles.space12,
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Row(
//                       spacing: AppStyles.space16,
//                       children: [
//                         SelectTransportMode(
//                           image: 'assets/images/moto.png',
//                           seat: '1',
//                           modeText: 'Moto',
//                         ),
//                         SelectTransportMode(
//                           image: 'assets/images/car.png',
//                           seat: '1',
//                           modeText: 'Car',
//                         ),
//                         SelectTransportMode(
//                           image: 'assets/images/auto.png',
//                           seat: '1',
//                           modeText: 'safari',
//                         ),
//                       ],
//                     ),

//                     CustomFormFieldWidget(
//                       label: 'To',
//                       prefixIcon: LucideIcons.search,
//                       enabled: false,
//                     ),
//                     CustomFormFieldWidget(
//                       prefixIcon: LucideIcons.mapPin200,
//                       label: 'Where are you going?',
//                       enabled: false,
//                     ),
//                     CustomButtonPrimary(
//                       title: 'Find Driver',
//                       onPressed: () {
//                         buildBottomSheet();
//                       },
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class SelectTransportMode extends StatelessWidget {
//   final String image;
//   final String modeText;
//   final String seat;
//   const SelectTransportMode({
//     super.key,
//     required this.image,
//     required this.modeText,
//     required this.seat,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: 60,
//       width: 90,
//       padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
//       decoration: BoxDecoration(
//         // border: Border.all(),
//         color: AppColors.darkPrimary,
//         borderRadius: AppStyles.radiusMd,
//       ),
//       child: Column(
//         // spacing: AppStyles.space8,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Image.asset(image, height: 30),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceAround,
//             children: [
//               Text(
//                 modeText,
//                 style: TextStyle(
//                   fontWeight: FontWeight.bold,
//                   color: AppColors.darkOnSurface,
//                 ),
//               ),
//               Icon(LucideIcons.userRound600, size: 14, color: Colors.grey[300]),
//               Text(
//                 seat,
//                 style: TextStyle(
//                   fontWeight: FontWeight.bold,
//                   color: Colors.grey[300],
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
// }

// Widget buildBottomSheet() {
//   return Positioned(
//     bottom: 0,
//     left: 0,
//     right: 0,
//     child: Container(
//       padding: EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black26,
//             blurRadius: 10,
//             offset: Offset(0, -2),
//           ),
//         ],
//       ),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Container(
//             width: 40,
//             height: 4,
//             decoration: BoxDecoration(
//               color: Colors.grey,
//               borderRadius: BorderRadius.circular(2),
//             ),
//           ),
//           SizedBox(height: 10),
//           Text('This is your bottom sheet!'),
//           SizedBox(height: 10),
//         ],
//       ),
//     ),
//   );
// }
