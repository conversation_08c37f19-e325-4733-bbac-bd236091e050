Project Title: Safari Yatri (Ride-Sharing System) 
Project Description: Safari Yatri project involves creating a Web application as well as Mobile Application 
that facilitates the creation, management, and handling of ride-sharing system. 
Feature 1. User Management & Authentication 
- User registration (Email/Phone, Social Login – Google/Facebook) 
- Secure login with OTP, password-based, or biometric authentication 
- Role-based access control (Admin, Driver, Passenger) 
- Profile management (Personal details, preferences, saved locations) 
Feature 2. Ride Booking & Management 
- Ride request system (Instant & scheduled rides) 
- Ride matching algorithm (Find nearby drivers) 
- Real-time ride tracking (GPS integration) 
- Fare estimation before booking 
- Multi-destination support (Pickup & drop-off flexibility) 
- Ride history & invoice generation 
- Cancellation & refund policies 
Feature 3. Driver Management & Onboarding 
- Driver registration & document verification 
- Availability toggle (Online/Offline status) 
- Earnings tracking & payout management 
- Driver rating & feedback system 
Feature 4. Vehicle & Fleet Management 
- Registered vehicle details (Safari,Car, Bike etc.) 
- Fare calculation based on vehicle type & distance 
Feature 5. Payment & Wallet Integration 
- Multiple payment options (Credit/Debit cards, Digital wallets, QR codes) 
- Safari Yatri wallet for quick payments 
- Transaction history & invoices 
- Cash & online payment flexibility 
Feature 6. Reviews & Ratings 
- Passenger reviews for drivers 
- Driver feedback for passengers 
- Star rating system for ride experience 
Feature 7. Emergency & Safety Features 
- SOS button for emergency alerts to authorities & contacts 
- Live ride-sharing with trusted contacts 
- Driver verification & background checks 
- In-app customer support & dispute resolution 
Feature 8. Notifications & Alerts 
- Ride confirmation & status updates (Push, SMS, Email) 
- Fare discounts & ride promotions 
- Driver & passenger communication (In-app chat) 
Feature 9. Multi-Language & Currency Support 
- UI language toggle (English/Nepali) 
- Fare display in local currency 
Feature 10. Admin Panel Features 
- User & driver management 
- Ride fare & commission settings 
- Payment reconciliation & refunds 
- Real-time dashboard (Bookings, Revenue, User Activity) 
Feature 11. Additional Features (Future Enhancements) 
- Carpooling & Shared Rides (Multiple passengers per trip) 
- Subscription & Loyalty Program (Discounts for frequent riders) 
- AI-powered Chatbot & Live Support 